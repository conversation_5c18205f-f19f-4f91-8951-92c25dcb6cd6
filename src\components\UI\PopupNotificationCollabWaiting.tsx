import React, { useEffect } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { Container, Stack, Typography } from "@mui/material";
import { openChat } from "@/utils/openChat";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import { ColabStatus, Level } from "@/constants/Const";
import { COLORS } from "@/constants/themes";

export default function PopupNotificationCollabWaiting() {
  const navigate = useNavigate();
  const [open, setOpen] = React.useState(false);
  const user = useSelector((state: RootState) => state.auth.user);
  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );

  useEffect(() => {
    if (
      user?.level === Level.Member &&
      user?.colabStatus !== ColabStatus.Not_Waiting
    ) {
      setOpen(true);
    }
  }, [user]);
  const handleClose = async () => {
    setOpen(false);
    navigate(Router.homepage);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        disableEscapeKeyDown={false}
        style={{ borderRadius: 10 }}
        PaperProps={{
          style: {
            borderRadius: 10,
            boxShadow: "none",
          },
        }}
      >
        <DialogContent>
          <Container>
            <Stack justifyContent={"center"} alignItems={"center"} gap={2}>
              <img src="/icons/check-ctv.svg" alt="" />
              <Typography color={"#1D1D5E"} fontSize={18} fontWeight={700}>
                Tài khoản đang phê duyệt
              </Typography>
              <Typography textAlign={"center"} fontSize={13}>
                Tài khoản của Bạn đang được {APP_NAME} phê duyệt Xin vui lòng
                đợi hoặc liên hệ bộ phận chăm sóc Đại lý của {APP_NAME}
              </Typography>
              <Button
                variant="contained"
                disableElevation
                style={{
                  color: "#fff",
                  height: 40,
                  fontSize: "16px",
                  fontWeight: 700,
                  lineHeight: "19.36px",
                  borderRadius: 40,
                  background: COLORS.primary,
                }}
                onClick={() => openChat()}
              >
                Chat support
              </Button>
            </Stack>
          </Container>
        </DialogContent>
      </Dialog>
    </>
  );
}
