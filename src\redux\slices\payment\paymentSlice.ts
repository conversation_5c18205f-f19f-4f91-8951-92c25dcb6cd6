import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IPayment } from "@/types/payment";
import { TransactionType, WithdrawalStatus } from "@/constants/Const";

interface PaymentState {
  payments: IPayment[];
  isLoading: boolean;
  error: string | null;
}

const initialState: PaymentState = {
  payments: [],
  isLoading: true,
  error: null,
};

export const getPayments = createAsyncThunk(
  "payment",
  async ({
    withdrawalStatus,
    startDate,
    endDate,
  }: {
    withdrawalStatus: number;
    startDate?: string;
    endDate?: string;
  }) => {
    let query = `?&filters[type][$eq]=${TransactionType.Withdraw}&sort[createdAt]=desc`;
    if (startDate && endDate) {
      query =
        query +
        `&filters[createdAt][$gte]=${startDate}&filters[createdAt][$lte]=${endDate}`;
    }
    if (withdrawalStatus) {
      query =
        query +
        `&filters[done][$eq]=${withdrawalStatus !== WithdrawalStatus.Pending}`;
    }
    const response: any = await request("get", `/api/my-payment${query}`);

    return response;
  }
);

export const withdrawalCommission = createAsyncThunk(
  "payment/withdrawal",
  async (
    {
      withdrawalValue,
    }: {
      withdrawalValue: number;
    },
    { rejectWithValue }
  ) => {
    try {
      const response: any = await request(
        "post",
        "/api/my-payment/withdrawal",
        {
          withdrawalValue,
        }
      );

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const TransactionSlice = createSlice({
  name: "Transaction",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getPayments.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getPayments.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.payments = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getPayments.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(withdrawalCommission.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        withdrawalCommission.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
        }
      )
      .addCase(withdrawalCommission.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default TransactionSlice.reducer;
