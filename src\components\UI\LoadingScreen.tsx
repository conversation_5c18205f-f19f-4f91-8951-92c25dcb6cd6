import { RootState } from "@/redux/store";
import React from "react";
import { useSelector } from "react-redux";

const LoadingSpinner = () => {
  const { isLoading } = useSelector((state: RootState) => state.loading);

  if (!isLoading) return null;

  return (
    <div className="spinner-overlay">
      <div className="spinner-container">
        <img src="/images/logo.png" alt="Loading" className="spinner-logo" />
      </div>
    </div>
  );
};

export default LoadingSpinner;
