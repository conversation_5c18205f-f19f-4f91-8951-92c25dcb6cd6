import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Stack } from "@mui/material";
import { useNavigate } from "../../utils/component-util";

export default function PopupNotifitionUse({
  buttonTitle,
  buttonStyle,
  actionAgree,
  content,
}: {
  buttonTitle: string;
  buttonStyle: any;
  actionAgree: any;
  content: React.ReactNode;
}) {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const navigate = useNavigate();
  return (
    <>
      <Button style={buttonStyle} onClick={handleClickOpen}>
        {buttonTitle}
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        style={{ borderRadius: "20px" }}
      >
        <DialogContent
          style={{ margin: 0, padding: "20px 40px", borderRadius: "20px" }}
        >
          {content}
        </DialogContent>
        <DialogActions
          style={{
            margin: 0,
            paddingBottom: 18,
            position: "absolute",
            right: "-20px",
            top: "-16px",
          }}
        >
          <Stack
            gap={2}
            direction="row"
            justifyContent={"center"}
            width={"100%"}
          >
            <Button
              style={{ color: "#666666", borderRadius: 99 }}
              onClick={handleClose}
            >
              x
            </Button>
          </Stack>
        </DialogActions>
      </Dialog>
    </>
  );
}
