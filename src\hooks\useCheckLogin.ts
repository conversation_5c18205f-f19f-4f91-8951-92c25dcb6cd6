import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { useAlert } from "../redux/slices/alert/useAlert";
import { APP_NAME } from "../constants/AppInfo";
import { Icon } from "../constants/Assets";

export const useCheckLogin = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const { showAlert } = useAlert();

  const checkLogin = (callback: () => void) => {
    if (!user) {
      showAlert({
        title: APP_NAME,
        content: "<PERSON>ui lòng kích hoạt tài khoản để tiếp tục!",
        icon: Icon.warning,
      });
    } else {
      callback();
    }
  };

  return {
    checkLogin,
  };
};
