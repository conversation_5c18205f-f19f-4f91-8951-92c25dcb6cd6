import { <PERSON>, Button, Grid, Stack } from "@mui/material";
import React, { useState } from "react";
import FrameContainer from "../../components/layout/Container";
import UserCard from "../../components/UserCard";
import { Icon } from "../../constants/Assets";
import { useTheme } from "@mui/material/styles";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { COLORS } from "@/constants/themes";

export default function MemberShip() {
  const [viewMenberShip, setViewMemberShip] = useState(0);
  const contentMemberShip = [
    {
      class: "Đồng",
      color: "#CC9D8D",
      title: "THÀNH VIÊN 1",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Bạc",
      color: "#B9CADC",
      title: "THÀNH VIÊN 2",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Vàng",
      color: "#E7B15B",
      title: "THÀNH VIÊN 3",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Bạch kim",
      color: "#6C7886",
      title: "THÀNH VIÊN 4",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
    {
      class: "Kim cương",
      color: "#72D8E4",
      title: "THÀNH VIÊN 5",
      content:
        "Tích luỹ 1% điểm trên giá trị đơn hàng Đổi điểm thành ưu đãi giảm giá khi mua hàng Voucher tặng theo chương trình ",
    },
  ];
  const theme = useTheme();
  const { user, userZalo } = useSelector((state: RootState) => state.auth);

  return (
    <FrameContainer title="Hạng thành viên">
      <Stack
        style={{
          borderRadius: 20,
          marginBlock: 12,
          background: "#fff",
          padding: 12,
          boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
        }}
      >
        <Grid container>
          <Grid item xs={7}>
            <Box sx={{ display: "flex", gap: 1 }} alignItems="center">
              <Box
                height={25}
                width={25}
                component="section"
                display="flex"
                alignItems="center"
                sx={{
                  p: 0.5,
                  borderRadius: "50%",
                  background: "white",
                }}
              >
                <img height={25} width={25} src={Icon.verify} alt="" />
              </Box>
              <Box style={{ width: "100%", fontSize: 12, fontWeight: 700 }}>
                <p style={{ marginTop: 10, color: theme.palette.primary.main }}>
                  Tài khoản xác thực
                </p>
                <p style={{ marginTop: -12, color: "#2B7BE9" }}>
                  Hạng: <span style={{ color: "#1D1D5E" }}>Membership</span>
                </p>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={5}>
            <UserCard user={user} userZalo={userZalo} />
          </Grid>
        </Grid>
        <Grid container spacing={2} marginBlock={1}>
          <Grid item xs={6}>
            <Stack
              textAlign="center"
              boxShadow="0px 0px 4px rgba(0, 0, 0, 0.25)"
              borderRadius={4}
              padding={2}
              fontSize={17}
              gap={1}
            >
              <span>Tổng chi tiêu</span>
              <span style={{ color: "#1D1D5E", fontWeight: 700 }}>
                1.000.000đ
              </span>
            </Stack>
          </Grid>
          <Grid item xs={6}>
            <Stack
              textAlign="center"
              boxShadow="0px 0px 4px rgba(0, 0, 0, 0.25)"
              borderRadius={4}
              padding={2}
              fontSize={17}
              gap={1}
              width="100%"
            >
              <span>Điểm thưởng</span>
              <span style={{ color: "#1D1D5E", fontWeight: 700 }}>200</span>
            </Stack>
          </Grid>
        </Grid>
        <Stack>
          <Stack direction="row" alignItems={"center"} gap={4}>
            <Stack
              style={{ background: "#44B57B" }}
              direction="row"
              color="#fff"
              padding={2}
              width={35}
              height={35}
              borderRadius={"50%"}
              alignItems="center"
              justifyContent={"center"}
            >
              TQ
            </Stack>
            <Stack>
              <p style={{ textAlign: "center" }}>
                Bạn cần tích luỹ thêm <b style={{ color: "#1D1D5E" }}>100k </b>
                để nâng hạng VIP
              </p>
            </Stack>
            <Stack
              style={{ background: "#44B57B" }}
              direction="row"
              color="#fff"
              padding={2}
              width={35}
              height={35}
              borderRadius={"50%"}
              alignItems="center"
              justifyContent={"center"}
            >
              VIP
            </Stack>
          </Stack>
          <Stack>
            <Box
              style={{
                background: "#B3B3B3",
                height: 7,
                width: "100%",
                marginBlock: 2,
              }}
            >
              <Box
                style={{ background: COLORS.primary, height: 7, width: "80%" }}
              ></Box>
            </Box>
            <Stack direction="row" justifyContent={"space-between"}>
              <span>0</span>
              <span>2.000.000</span>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
      <Stack paddingBottom={4}>
        <Stack direction={"row"} justifyContent="space-between" marginBlock={2}>
          {contentMemberShip.map((item, i) => (
            <Button
              key={`member-${i}`}
              onClick={() => {
                setViewMemberShip(i);
              }}
              style={{
                height: 45,
                width: "100%",
                background: viewMenberShip === i ? "transparent" : "#fff",
              }}
            >
              <Box
                style={{
                  background: item.color,
                  height: 30,
                  width: 30,
                  borderRadius: "50%",
                }}
              />
            </Button>
          ))}
        </Stack>
        {contentMemberShip.map((item, i) => (
          <Stack
            key={`membership-${i}`}
            gap={0}
            display={viewMenberShip === i ? "block" : "none"}
          >
            <p style={{ fontWeight: 700, padding: 0, margin: 0 }}>
              {item.title}
            </p>
            <p>{item.content}</p>
          </Stack>
        ))}
      </Stack>
    </FrameContainer>
  );
}
