import { COLORS } from '@/constants/themes';
import { Box, Button, Stack, Typography } from '@mui/material';
import React from 'react'

export default function ActivateAccount() {
    return (
        <Box
            sx={{
                backgroundColor: COLORS.primary, // xanh biển
                color: "#fff",
                borderRadius: 2,
                padding: 2,
                mx: 1,
                mb: 1,
            }}
        >
            <Stack direction="row" alignItems="center" justifyContent="space-between">
                {/* Title + Content */}
                <Box>
                    <Typography variant="h6" fontSize={16} fontWeight={700}>
                        Kích hoạt tài khoản
                    </Typography>
                    <Typography variant="body2" fontSize={14}>
                        Nhận nhiều ưu đãi từ LIBERTY HOLDINGS
                    </Typography>
                </Box>

                {/* Button */}
                <Button
                    variant="contained"
                    onClick={()=>{}}
                    sx={{
                        backgroundColor: "#fff",
                        color: "secondary.main",
                        boxShadow: 'none',
                        "&:hover": {
                            backgroundColor: "#f5f5f5",
                            boxShadow: 'none'
                        },
                    }}
                >
                    <PERSON><PERSON><PERSON> hoạt ngay
                </Button>
            </Stack>
        </Box>
    )
}
