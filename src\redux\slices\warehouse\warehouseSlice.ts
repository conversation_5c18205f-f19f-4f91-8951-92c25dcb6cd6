import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";

interface IWarehouse {
  id: number;
  attributes: {
    code: string;
    name: string;
    address: string;
  };
}

interface WarehouseState {
  list: IWarehouse[] | null;
  currentWarehouse: IWarehouse | null;
  isLoading: boolean;
}

const initialState: WarehouseState = {
  list: null,
  currentWarehouse: null,
  isLoading: true,
};

export const getWarehouseList = createAsyncThunk(
  "warehouse/getWarehouseList",
  async () => {
    const response = await request("get", "/api/warehouses");
    return response;
  }
);

const warehouseSlice = createSlice({
  name: "warehouse",
  initialState,
  reducers: {
    setCurrentWarehouse: (state, action: PayloadAction<IWarehouse>) => {
      state.currentWarehouse = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getWarehouseList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getWarehouseList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          state.list = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getWarehouseList.rejected, (state) => {
        state.isLoading = false;
      });
  },
});

export const { setCurrentWarehouse } = warehouseSlice.actions;
export default warehouseSlice.reducer;
