import * as React from "react";
import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Avatar from "@mui/material/Avatar";
import { RichTreeView } from "@mui/x-tree-view/RichTreeView";
import {
  unstable_useTreeItem2 as useTreeItem2,
  UseTreeItem2Parameters,
} from "@mui/x-tree-view/useTreeItem2";
import {
  TreeItem2Content,
  TreeItem2IconContainer,
  TreeItem2GroupTransition,
  TreeItem2Label,
  TreeItem2Root,
  TreeItem2Checkbox,
} from "@mui/x-tree-view/TreeItem2";
import { TreeItem2Icon } from "@mui/x-tree-view/TreeItem2Icon";
import { TreeItem2Provider } from "@mui/x-tree-view/TreeItem2Provider";
import { TreeViewBaseItem, TreeViewItemId } from "@mui/x-tree-view/models";
import { Icon } from "@/constants/Assets";
import { LevelName } from "@/constants/Const";
import { formatPrice } from "@/utils/formatPrice";

export type ExtendedTreeItemProps = {
  id: string;
  name: string;
  avatar?: string;
  children: Array<ExtendedTreeItemProps>;
};

interface IContentTreeView {
  items: Array<ExtendedTreeItemProps>;
  isSearch?: boolean;
  containerStyles?: React.CSSProperties;
}

const CustomTreeItemContent = styled(TreeItem2Content)(({ theme }) => ({
  padding: theme.spacing(0.5, 1),
}));

interface CustomTreeItemProps
  extends Omit<UseTreeItem2Parameters, "rootRef">,
    Omit<React.HTMLAttributes<HTMLLIElement>, "onFocus"> {}

const CustomTreeItem = React.forwardRef(function CustomTreeItem(
  props: CustomTreeItemProps,
  ref: React.Ref<HTMLLIElement>
) {
  const { id, itemId, label, disabled, children, ...other } = props;
  const {
    getRootProps,
    getContentProps,
    getIconContainerProps,
    getCheckboxProps,
    getLabelProps,
    getGroupTransitionProps,
    status,
    publicAPI,
  } = useTreeItem2({ id, itemId, children, label, disabled, rootRef: ref });

  const item = publicAPI.getItem(itemId);

  const getLabel = (item) => {
    const totalManagementCommissionValue =
      parseInt(item.totalCommission.totalManagementCommissionValue) || 0;
    const totalMyCommission =
      parseInt(item.totalCommission.totalMyCommission) || 0;
    const totalCommission = formatPrice(
      totalManagementCommissionValue + totalMyCommission
    );
    const totalSale = formatPrice(
      parseInt(item.mySale ?? 0) + parseInt(item.myTeamSale ?? 0)
    );
    return `F${item.fLevel} - ${item.name} - ${item.phone} - Hạng: ${
      LevelName[item.level]
    } - Doanh số :${totalSale} - Hoa hồng: ${totalCommission}`;
  };

  return (
    <TreeItem2Provider itemId={itemId}>
      <TreeItem2Root {...getRootProps(other)}>
        <CustomTreeItemContent
          {...getContentProps()}
          style={{ alignItems: "center" }}
        >
          {/* <MemberItem item={item} /> */}
          <TreeItem2IconContainer {...getIconContainerProps()}>
            <TreeItem2Icon status={status} />
          </TreeItem2IconContainer>
          <Box sx={{ flexGrow: 1, display: "flex", gap: 1 }}>
            <Avatar
              sx={(theme) => ({
                background: theme.palette.primary.main,
                width: 24,
                height: 24,
                fontSize: "0.8rem",
              })}
              src={item.avatarUrl || Icon.avatarDefault}
            />
            <TreeItem2Checkbox {...getCheckboxProps()} />
            <TreeItem2Label
              {...{
                children: getLabel(item),
              }}
            />
          </Box>
        </CustomTreeItemContent>
        {children && (
          <TreeItem2GroupTransition {...getGroupTransitionProps()} />
        )}
      </TreeItem2Root>
    </TreeItem2Provider>
  );
});

export default function ContentTreeView({
  items,
  isSearch,
  containerStyles,
}: IContentTreeView) {
  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);

  const handleExpandedItemsChange = (
    event: React.SyntheticEvent,
    itemIds: string[]
  ) => {
    setExpandedItems(itemIds);
  };

  const getAllItemsWithChildrenItemIds = () => {
    const itemIds: TreeViewItemId[] = [];
    const registerItemId = (item: TreeViewBaseItem) => {
      if (item.children?.length) {
        itemIds.push(item.id);
        item.children.forEach(registerItemId);
      }
    };
    items.forEach(registerItemId);
    return itemIds;
  };

  React.useEffect(() => {
    if (isSearch) {
      const allIds = getAllItemsWithChildrenItemIds();
      setExpandedItems(allIds);
    } else {
      setExpandedItems([]);
    }
  }, [items]);

  return (
    <Box sx={{ minHeight: 180, flexGrow: 1, ...containerStyles }}>
      <RichTreeView
        aria-label="icon expansion"
        sx={{ position: "relative" }}
        expandedItems={expandedItems}
        onExpandedItemsChange={handleExpandedItemsChange}
        items={items}
        slots={{ item: CustomTreeItem }}
      />
    </Box>
  );
}
