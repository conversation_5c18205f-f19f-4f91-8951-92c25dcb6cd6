import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { <PERSON><PERSON><PERSON>, IconButton, Stack, Typography } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import * as React from "react";
import { createShortcut } from "zmp-sdk/apis";
import { Icon } from "@/constants/Assets";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

const createMiniAppShortcut = async () => {
  try {
    await createShortcut({
      params: {
        utm_source: "shortcut",
      },
    });
  } catch (error) {
    // xử lý khi gọi api thất bại
    console.log(error);
  }
};

export default function PopupAddIconScreenApp() {
  const [open, setOpen] = React.useState(false);
  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Stack
        direction="row"
        gap={1}
        padding={2}
        justifyContent="space-between"
        onClick={handleClickOpen}
      >
        <Stack direction="row" alignItems="center" gap={2}>
          <img width={20} height={20} src={Icon.addIcon} alt="icon" />
          <Stack>
            <Typography fontSize={14} fontWeight={700}>
              Tạo icon app trên màn hình chính
            </Typography>
            <Typography fontSize={14} color={"#969595"}>
              Dễ dàng truy cập miniapp hơn
            </Typography>
          </Stack>
        </Stack>
        <IconButton>
          <KeyboardArrowRightIcon />
        </IconButton>
      </Stack>

      <Dialog
        fullWidth
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          style: {
            width: "100%",
            position: "absolute",
            bottom: "5%",
            margin: 0,
            borderRadius: "20px",
            boxShadow: "none",
          },
        }}
      >
        <DialogContent>
          <Stack gap={2}>
            <Stack gap={1}>
              <Typography fontSize={18} fontWeight={700}>
                Thêm vào màn hình chờ
              </Typography>
              <Typography fontSize={14}>
                Chạm và giữ một biểu tượng hoặc chạm
              </Typography>
              <Typography fontSize={14}>
                Thêm để thêm icon app vào màn hình chờ
              </Typography>
            </Stack>
            <Stack
              gap={1}
              alignItems={"center"}
              padding={3}
              bgcolor={"#F0F8FF"}
              borderRadius={"20px"}
            >
              <img width={"30%"} src="/images/logo.png" alt="" />
              <Stack alignItems={"center"}>
                <Typography fontSize={12} color={"#666666"} fontWeight={700}>
                  {APP_NAME}
                </Typography>
                <Typography fontSize={12} color={"#666666"}>
                  1 x 1
                </Typography>
              </Stack>
            </Stack>
          </Stack>
          <Stack direction="row" justifyContent="space-between" marginTop={3}>
            <Button
              style={{ color: "#000000", fontWeight: 700, flex: 1 }}
              disableElevation
              onClick={handleClose}
            >
              Thoát
            </Button>
            <Divider orientation="vertical" style={{ height: "30px" }} />
            <Button
              style={{ color: "#000000", fontWeight: 700, flex: 1 }}
              disableElevation
              onClick={createMiniAppShortcut}
            >
              Thêm
            </Button>
          </Stack>
        </DialogContent>
      </Dialog>
    </>
  );
}
