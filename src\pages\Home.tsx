import React from "react";
import HeaderPage from "../components/layout/LayoutHomepage";
import { Router } from "../constants/Route";
import { configAppView } from "zmp-sdk/apis";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import Banner from "../components/banner/Banner";
import ListItemHomePage from "./home/<USER>";
import ProductSlider from "@/components/products/ProductSlider";
import NewsSlider from "@/components/news/NewsSlider";
import ListFeatureHomePage from "./home/<USER>";
import useKeepPositionScroll from "@/hooks/useKeepPositionScroll";
import { Box, Button, Container, Stack, Typography } from "@mui/material";
import { COLORS } from "@/constants/themes";
import { APP_NAME } from "@/constants/AppInfo";
import CollabRegisterInform from "@/components/collab/CollabRegisterInform";
import { DEFAULT_REFER_CODE, Level } from "@/constants/Const";
import { BannerType } from "@/types/news";
import { followOA } from "zmp-sdk/apis";
import {
  getUser,
  getUserZalo,
  register,
  registerCollabration,
  updateMe,
} from "@/redux/slices/authen/authSlice";
import { getRouteParams } from "zmp-sdk";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { Icon } from "@/constants/Assets";
import { mapError, showToast } from "@/utils/common";
import { useNavigate } from "react-router-dom";
import { Platform } from "@/config";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import UserProfile from "@/components/user-profile/UserProfile";
import CategorySlider from "@/components/category/CategorySlider";
import ProductTwoColumn from "@/components/products/ProductTwoColumn";
import GradientText from "@/components/UI/GradientTextProps";
import FixButton from "@/components/UI/FixButton";
import ActivateAccount from "./home/<USER>";

configAppView({
  headerColor: "#1D1D5E",
  headerTextColor: "white",
  hideAndroidBottomNavigationBar: true,
  hideIOSSafeAreaBottom: true,
  actionBar: {
    hide: true,
    leftButton: "none",
  },
  statusBarType: "transparent",
});

const HomePage: React.FunctionComponent = () => {
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector((state: RootState) => state.auth.user);
  const userZalo = useSelector((state: RootState) => state.auth.userZalo);
  const newsList = useSelector((state: RootState) => state.news.newsListHome);
  // const reviewList = useSelector(
  //   (state: RootState) => state.news.reviewListHome
  // );
  const { productRecommendList, productBestSellerList } = useSelector(
    (state: RootState) => state.productList
  );
  const { refCode } = getRouteParams();
  const { showAlert } = useAlert();
  const navigate = useNavigate();
  const { checkLogin } = useCheckLogin();

  const onUpdateAfterFollowOA = async () => {
    const res = await dispatch(updateMe({ isZaloOA: true })).unwrap();
    if (res) {
      showAlert({
        icon: Icon.check,
        title: `Cảm ơn bạn đã quan tâm OA ${APP_NAME}`,
        content: `Mời bạn mua sắm và trải nghiệm dịch vụ của ${APP_NAME}.`,
        buttons: [
          {
            title: "Xem sản phẩm",
            action: () => {
              navigate(Router.product);
            },
          },
        ],
      });
      dispatch(getUser());
    }
  };

  const onFollowOA = async () => {
    try {
      await followOA({
        id: import.meta.env.VITE_ZMP_OA_ID as string,
        success: onUpdateAfterFollowOA,
        fail: (error) => {
          console.log(error);
          if (error.code !== -201) {
            //code: -201 - User deny process action!
            showToast({
              content:
                error.message || "Thao tác không thành công, vui lòng thử lại",
              type: "error",
            });
          }
        },
      });
    } catch (error) {
      console.log(error);
      showToast({
        content: "Thao tác không thành công, vui lòng thử lại",
        type: "error",
      });
    }
  };

  const onClickRegister = async () => {
    const referCode = refCode || DEFAULT_REFER_CODE;
    const res = await dispatch(register(referCode)).unwrap();
    if (res.jwt) {
      showAlert({
        icon: Icon.check,
        title: "Kích hoạt tài khoản thành công",
      });
      // await dispatch(
      //   registerCollabration({
      //     referCode: referCode,
      //   })
      // );
      await dispatch(getUserZalo());
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
  };

  useKeepPositionScroll(Router.homepage);
  return (
    <HeaderPage>
      <Stack paddingInline={1}>
        <UserProfile />
      </Stack>
      <ActivateAccount />
      <Banner type={BannerType.home_banner} />
      <CategorySlider />
      <Stack paddingInline={1} gap={1}>
        {!user && (
          <Box
            style={{
              display: "flex",
              background: COLORS.primary,
              padding: "15px 15px",
              color: "#fff",
              borderRadius: 5,
            }}
          >
            <Box style={{ flex: "1 1" }}>
              <Typography style={{ fontWeight: "bold", fontSize: 16 }}>
                Kích hoạt tài khoản
              </Typography>
              <Typography style={{ fontSize: 12 }}>
                Nhận nhiều ưu đãi đến từ <b>{APP_NAME.toUpperCase()}</b>
              </Typography>
            </Box>
            <Box
              style={{
                display: "flex",
                alignItems: "center",
                margin: "0 0 0 auto",
              }}
            >
              <Button
                style={{
                  background: "#fff",
                  height: 30,
                }}
                onClick={onClickRegister}
              >
                <GradientText text="Kích hoạt ngay" />
              </Button>
            </Box>
          </Box>
        )}
        {user && !user?.isZaloOA && Platform === "zalo" ? (
          <Box
            style={{
              background: "white",
              padding: "15px 15px",
              color: "#000",
              borderRadius: 5,
              border: `1px solid ${COLORS.primary}`,
            }}
          >
            <Typography style={{ fontSize: 13 }}>
              Quan tâm OA để nhận các chương trình đặc quyền ưu đãi
            </Typography>
            <Box style={{ display: "flex", marginTop: "10px" }}>
              <Box style={{ display: "flex", alignItems: "center", gap: 5 }}>
                <img src="/images/logo.png" style={{ width: 80 }} />
              </Box>
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  margin: "0 0 0 auto",
                }}
              >
                <Button
                  style={{
                    background: COLORS.primary,
                    color: "#fff",
                    height: 35,
                    padding: 15,
                  }}
                  onClick={() => checkLogin(onFollowOA)}
                >
                  Quan tâm
                </Button>
              </Box>
            </Box>
          </Box>
        ) : null}
        <ListItemHomePage
          title={"Sản phẩm nổi bật"}
          isShowMore={true}
          linkAfterClickShowMore="/product"
        >
          <ProductTwoColumn productList={productBestSellerList} />
        </ListItemHomePage>
        {/* <img width={"100%"} src="/images/big_banner.png" alt="" /> */}
        <ListItemHomePage
          title={"Tin tức"}
          isShowMore={true}
          linkAfterClickShowMore="/posts"
        >
          <NewsSlider news={newsList} />
        </ListItemHomePage>
        {/* <ListItemHomePage
        title={"Gợi ý cho bạn"}
        isShowMore={true}
        linkAfterClickShowMore="/product"
      >
        <ProductSlider productList={productRecommendList} />
      </ListItemHomePage> */}

        {/* <ListItemHomePage
        title={"Sản phẩm mới nhất"}
        isShowMore={true}
        linkAfterClickShowMore="/product"
      >
        <ProductSlider productList={newProductList} />
      </ListItemHomePage> */}
        {/* <ListItemHomePage title={"Review Sản phẩm"}>
        <NewsSlider news={reviewList} />
      </ListItemHomePage> */}
        {/* <ListFeatureHomePage /> */}
      </Stack>
    </HeaderPage>
  );
};

export default HomePage;
