import React, { useEffect, useMemo, useState } from "react";
import FrameContainer from "../../components/layout/Container";
import {
  Stack,
  FormControl,
  TextField,
  InputAdornment,
  CircularProgress,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { getMember } from "../../redux/slices/team/team";
import NoDataView from "@/components/UI/NoDataView";
import { useSearchParams } from "react-router-dom";
import MemberItem from "./components/MemberItem";

export default function CollabMembers() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [keyword, setKeyword] = useState("");
  const { list, isLoading } = useSelector((state: RootState) => state.team);
  const dispatch = useDispatch<AppDispatch>();

  const [showClearIcon, setShowClearIcon] = useState("none");

  const handleChangeSearch = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setKeyword(event.target.value);
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
  };

  const handleClick = (): void => {
    setKeyword("");
  };

  const userInfo = useMemo(() => {
    const data: {
      userId: null | number;
      userName: null | string;
      level: number;
    } = {
      userId: null,
      userName: null,
      level: 0,
    };
    const userId = searchParams.get("userId");
    if (userId) {
      data.userId = Number(userId);
    }

    const userName = searchParams.get("userName");
    if (userName) {
      data.userName = userName;
    }

    const level = searchParams.get("level");
    if (level) {
      data.level = Number(level);
    }

    return data;
  }, [searchParams]);

  useEffect(() => {
    getData();
  }, [keyword, userInfo]);

  const getData = async () => {
    const searchCondition = {
      keyword,
      ...(userInfo.userId ? { userId: userInfo.userId } : undefined),
    };
    return await dispatch(getMember(searchCondition));
  };

  return (
    <FrameContainer title="Danh sách khách hàng" style={{ background: "#FFF" }}>
      <Stack sx={{ marginBottom: 1 }}>
        <FormControl>
          <TextField
            placeholder="Nhập tên, SĐT hoặc ID thành viên"
            style={{ width: "100%" }}
            size="small"
            variant="outlined"
            value={keyword}
            onChange={handleChangeSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {<SearchIcon />}
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment
                  position="end"
                  style={{ display: showClearIcon }}
                  onClick={handleClick}
                >
                  <ClearIcon />
                </InputAdornment>
              ),
            }}
          />
        </FormControl>
      </Stack>
      {isLoading ? (
        <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
          <CircularProgress />
        </Stack>
      ) : (
        <Stack>
          {list.length > 0 ? (
            list.map((item, index) => {
              if (item.fLevel !== 1 && !keyword) {
                return;
              }

              const data = { ...item, fLevel: item.fLevel + userInfo.level };

              return (
                <MemberItem
                  item={data}
                  key={String(index)}
                  onClick={() => {
                    setSearchParams({
                      userId: item.id,
                      userName: item.name,
                      level: data.fLevel,
                    });
                  }}
                />
              );
            })
          ) : (
            <NoDataView
              content={
                userInfo.level
                  ? `Bạn chưa có F${userInfo.level + 1} nào`
                  : `Đội nhóm của ${
                      userInfo.userName ? userInfo.userName : "bạn"
                    } chưa có thành viên nào`
              }
            />
          )}
        </Stack>
      )}
    </FrameContainer>
  );
}
