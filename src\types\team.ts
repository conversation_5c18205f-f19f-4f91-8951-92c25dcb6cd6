export interface ITeamReport {
  totalCommission: number | 0;
  totalCommissionSuccess: number | 0;
  totalOrderSuccess: number | 0;
  totalMembers: number | 0;
  userLevelData: any;
  commissionSales: ICommissionSale;
}

export interface ICommissionSale {
  totalCommissionF1: {
    commission: number;
    sales: number;
  };
  totalCommissionF2: {
    commission: number;
    sales: number;
  };
  totalFixedCommission: number
}
