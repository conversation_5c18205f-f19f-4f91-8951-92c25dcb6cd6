import { Divider, Stack, Box } from "@mui/material";
import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import FrameContainer from "../../../components/layout/Container";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../redux/store";
import { clearTermDetail, getTermDetail } from "../../../redux/slices/term/termSlice";

export default function PolicyDetail() {
  const { id } = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const { termDetail } = useSelector((state: RootState) => state.term);

  useEffect(() => {
    if (!id) return;
    dispatch(getTermDetail(id));
    return () => {dispatch(clearTermDetail())}
  }, [id]);

  return (
    <FrameContainer title={termDetail?.attributes?.title || ""}>
      <Stack
        fontSize={14}
        style={{
          backgroundColor: "#fff",
          paddingBlock: 12,
          borderRadius: 10,
          color: "#000",
        }}
      >
        <Stack p={2} textAlign="center" fontWeight={700}>
          <span> {termDetail?.attributes?.title}</span>
        </Stack>
        <Divider />
        <Stack p={2}>
          <Box
            dangerouslySetInnerHTML={{
              __html: termDetail?.attributes?.content || "",
            }}
          />
        </Stack>
      </Stack>
    </FrameContainer>
  );
}
