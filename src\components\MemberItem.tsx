import { <PERSON>, <PERSON>rid, <PERSON>ack, <PERSON>po<PERSON>, <PERSON><PERSON>, Button } from "@mui/material";
import dayjs from "dayjs";
import React from "react";
import { COLORS } from "@/constants/themes";
import { Icon } from "@/constants/Assets";
import { formatPrice } from "@/utils/formatPrice";
import { LevelName } from "@/constants/Const";

type ItemType = {
  icon: string;
  title: string;
  value: number;
  isPrice?: boolean;
};

export default function MemberItem({ item, index }) {
  const totalManagementCommissionValue =
    parseInt(item.totalCommission.totalManagementCommissionValue) || 0;
  const totalMyCommission =
    parseInt(item.totalCommission.totalMyCommission) || 0;
  const totalCommission = totalManagementCommissionValue + totalMyCommission;
  const infoItems: Array<ItemType> = [
    {
      icon: Icon.icon_commission_1,
      title: "Hoa hồng",
      value: totalCommission,
      isPrice: true,
    },
    {
      icon: Icon.icon_commission_2,
      title: "Tổng doanh số",
      value: parseInt(item.mySale ?? 0) + parseInt(item.myTeamSale ?? 0),
      isPrice: true,
    },
  ];
  return (
    <Stack style={styles.container}>
      <Stack direction={"row"} marginBottom={"10px"}>
        <Stack direction={"row"} flex={1} alignItems={"center"} gap={1}>
          <Stack>
            <Avatar src={item.avatarUrl || Icon.avatarDefault} size={46} />
          </Stack>
          <Stack gap={1}>
            <Typography style={styles.usernameText}>
              {item.name ?? "N/A"}
            </Typography>
            <Typography style={{ ...styles.smallTitle }}>
              Ngày tham gia: {dayjs(item.createdAt).format("DD/MM/YYYY")}
            </Typography>
          </Stack>
        </Stack>
        <Stack gap={1}>
          <Typography style={styles.rightText}>
            Hệ cấp: <span style={styles.levelText}>F{item.fLevel}</span>
          </Typography>
          <Typography style={styles.rightText}>
            Hạng: <span style={styles.rankText}>{LevelName[item.level]}</span>
          </Typography>
        </Stack>
      </Stack>

      {infoItems.map((info) => (
        <Box key={info.title} sx={styles.infoItemContainer}>
          <Box sx={{ display: "flex" }} marginBlock={1}>
            <img
              src={info.icon}
              width={20}
              height={20}
              style={{ objectFit: "contain" }}
            />
            <Typography style={styles.infoTitleText}>{info.title}</Typography>
          </Box>
          <Typography style={styles.valueText}>
            {info.isPrice ? formatPrice(info.value) : info.value}
          </Typography>
        </Box>
      ))}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: "#fff",
    border: "1px solid #D9D9D9",
    borderRadius: 10,
    paddingBlock: 12,
    paddingInline: 12,
    marginBlock: 8,
    flex: 1,
  },
  smallTitle: {
    fontSize: 12,
  },
  processContainer: {
    background: "#B3B3B3",
    height: 7,
    width: "100%",
    marginBlock: 2,
  },
  currentProcess: {
    background: COLORS.primary,
    height: 7,
    width: "80%",
  },
  rightText: {
    color: COLORS.gray,
    textAlign: "right",
  },
  levelText: {
    color: "#FF9900",
    fontWeight: 700,
  },
  rankText: {
    color: "#1D1D5E",
    fontWeight: 700,
  },
  infoItemContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  usernameText: {
    fontWeight: 700,
    color: COLORS.blue,
  },
  valueText: {
    color: COLORS.primary,
    fontWeight: 700,
  },
  infoTitleText: {
    color: COLORS.gray,
    paddingLeft: 8,
  },
};
