import React, { useEffect, useRef } from "react";
import { Box, Stack, Typography } from "@mui/material";
import { IProduct } from "../../types/product";
import { formatPrice } from "../../utils/formatPrice";
import { round } from "lodash";
import { useNavigate } from "../../utils/component-util";
import { Icon } from "@/constants/Assets";
import { useCart } from "@/hooks/useCart";
import { showToast } from "@/utils/common";
import { COLORS } from "@/constants/themes";
import GradientText from "../UI/GradientTextProps";
interface ProductItemProps {
  item: IProduct;
  isShowAdd: boolean;
}

const ProductItem: React.FC<ProductItemProps> = ({
  item,
  isShowAdd = false,
}) => {
  const navigate = useNavigate();
  const { addProductToCart } = useCart();
  const productRef = useRef<HTMLDivElement>(null);

  const handleAddProduct = (e) => {
    e.stopPropagation();
    const result = addProductToCart(item, 1);
    if (result) {
      showToast({
        content: "Thêm vào giỏ thành công",
        type: "success",
      });
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("product-item");
            observer.unobserve(entry.target);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px",
      }
    );

    if (productRef.current) {
      observer.observe(productRef.current);
    }

    return () => {
      if (productRef.current) {
        observer.unobserve(productRef.current);
      }
    };
  }, []);

  return (
    <Box
      ref={productRef}
      style={{
        display: "flex",
        flexDirection: "column",
        cursor: "pointer",
        position: "relative",
        background: COLORS.white,
        borderRadius: "10px",
        opacity: 0,
      }}
      onClick={() => {
        navigate(`/product-detail/${item.id}`);
      }}
    >
      <img
        width="100%"
        style={{
          aspectRatio: "1 / 1",
          objectFit: "cover",
          borderTopLeftRadius: "10px",
          borderTopRightRadius: "10px",
        }}
        src={item.image[0]?.url}
        alt={item.name}
      />

      {item.discount > 0 && round((item.discount / item.price) * 100) > 1 && (
        <Box
          style={{
            borderRadius: "15px",
            position: "absolute",
            top: 10,
            left: 10,
            background: " #E14337",
            color: "#fff",
            padding: "3px",
            fontSize: 14,
          }}
        >
          -{round((item.discount / item.price) * 100)}%
        </Box>
      )}
      <Box
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
          fontSize: 12,
          paddingBlock: 10,
        }}
      >
        <Box
          display={"flex"}
          gap={"5px"}
          alignItems={"baseline"}
          marginInline={"5px"}
          minHeight={"40px"}
        >
          <div
            style={{
              color: "#252627",
              lineHeight: "20px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitBoxOrient: "vertical",
              WebkitLineClamp: 2,
              flex: 1,
              fontSize: 14,
              fontWeight: 500,
            }}
          >
            {item.name}
          </div>
        </Box>

        <Box style={styles.priceContainer}>
          <GradientText
            text={formatPrice(item.price - item.discount)}
            style={styles.salePriceText}
          />
          {item.discount > 0 && (
            <Typography style={styles.discountPrice}>
              {formatPrice(item.price)}
            </Typography>
          )}
        </Box>
        <Box
          sx={{
            ...styles.discountPriceTextContainer,
          }}
        >
          <Box
            style={styles.rateContainer}
            display={"flex"}
            flexWrap={"wrap-reverse"}
            className="rate-container"
          >
            <Box
              style={styles.rateText}
              display={"inline"}
              alignItems={"center"}
            >
              <Box paddingBlock={"auto"}>
                <img src={Icon.star} width={14} height={12} />
              </Box>
              <Box px={0.3}>
                <Typography
                  color={COLORS.accent1}
                  fontSize={12}
                  fontWeight={500}
                >
                  4.9
                </Typography>
              </Box>
            </Box>
            <Typography style={styles.soldText}>
              ({item.sold || 0} đã bán)
            </Typography>
          </Box>
          {isShowAdd && (
            <img src={Icon.add_product} alt="add" onClick={handleAddProduct} />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ProductItem;

const styles: Record<string, React.CSSProperties> = {
  backgroundImageProduct: {
    background: "#fff",
    border: "1px solid #E9EBED",
    borderRadius: "10px",
  },
  priceContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 4,
    marginInline: "5px",
  },
  salePriceText: {
    fontSize: 16,
    fontWeight: 500,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 12,
    fontWeight: 400,
    color: "#B8B8B8",
  },
  rateContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 2,
  },
  rateText: {
    background: "#E6E6E6",
    color: "#747474",
    fontStyle: "normal",
    paddingInline: 3,
    paddingBlock: 0,
    borderRadius: 3,
    display: "flex",
    alignItems: "baseline",
    gap: 1,
  },
  soldText: {
    fontSize: 11,
    fontWeight: 400,
    fontStyle: "normal",
    color: "#B5B5B5",
  },
  discountPriceTextContainer: {
    marginInline: "5px",
    display: "flex",
    fontWeight: 700,
    alignItems: "end",
    justifyContent: "space-between",
    color: COLORS.primary1,
  },
};
