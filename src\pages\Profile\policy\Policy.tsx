import { Box, Divider, Stack, Typography } from "@mui/material";
import { default as React, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../redux/store";
import { getCollaboratorPolicyList, getTermList } from "../../../redux/slices/term/termSlice";
import { ITerm } from "../../../types/term";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import Banner from "@/components/banner/Banner";
import { BannerType } from "@/types/news";

export default function PolicyList() {
  const dispatch = useDispatch<AppDispatch>();
  const { collaboratorPolicyList } = useSelector(
    (state: RootState) => state.term
  );

  useEffect(() => {
    dispatch(getCollaboratorPolicyList());
  }, []);

  return (
    <FrameContainerFull
      title="Chính sách cộng tác viên"
      overrideStyle={{ background: "#fff", height: "100%" }}
    >
      <Box>
        <Banner type={BannerType.collab_banner} />

        <Box style={{ padding: 15 }}>
          <Typography fontWeight={700}>Chính sách cộng tác viên</Typography>

          <Box marginBlock={"15px"}>
            {collaboratorPolicyList &&
              collaboratorPolicyList.map((term: ITerm, i) => (
                <Box
                  className="slider-policy-item"
                  padding={"10px"}
                  key={`policy-${i}`}
                  style={{
                    background: "rgba(111, 213, 9, 0.3)",
                    borderRadius: 5,
                  }}
                >
                  <Stack>
                    <Stack padding={1} justifyContent="space-between">
                      <Stack direction="row" alignItems="center">
                        <Typography fontWeight={700}>
                          {term?.attributes.title}
                        </Typography>
                      </Stack>
                      <Divider />
                      <Box
                        dangerouslySetInnerHTML={{
                          __html: term?.attributes?.content || "",
                        }}
                      />
                    </Stack>
                  </Stack>
                </Box>
              ))}
          </Box>
        </Box>
      </Box>
    </FrameContainerFull>
  );
}
