@import "../../node_modules/react-image-gallery/styles/css/image-gallery.css";

/* Global font family */
* {
  font-family: "Be Vietnam Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

body {
  font-family: "Be Vietnam Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  margin: 0;
  padding: 0;
}

.page {
  padding: 16px 16px 96px 16px;
  font-family: "Be Vietnam Pro", sans-serif;
}

.section-container {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 24px;
  font-family: "Be Vietnam Pro", sans-serif;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.label {
  white-space: nowrap;
}

.root {
  min-width: none;
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.product .Mui-selected {
  background-color: #fff !important;
}

.product-detail-action :first-child {
  width: 26px;
}

.bottom-mainmenu a {
  min-width: 0;
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: 1;
}

.bottom-mainmenu {
  display: flex !important;
  justify-content: space-between;
}

.bottom-mainmenu .Mui-selected {
  font-size: 9 !important;
  font-weight: 700;
  color: #0973ba;
}

.collab-history .Mui-selected {
  color: #0973ba !important;
  font-weight: 700 !important;
  font-size: 14 !important;
  display: flex !important;
  gap: 1rem;
  justify-content: space-between !important;
  width: 50%;
}

.bottom-mainmenu .label {
  white-space: nowrap;
}

.bottom-mainmenu .root {
  min-width: none;
}

.popup-add-to-cart .MuiDialog-scrollPaper {
  align-items: flex-end;
}

.popup-add-to-cart .MuiDialog-paperFullScreen {
  height: 50%;
}

.input-discount-popup-add-to-cart input {
  /* padding: 2; */
  border: #1d1d5e 1px solid;
  border-radius: 5px;
}

.input-discount-popup-add-to-cart label {
  /* top: -13px; */
  font-size: 12px;
  color: #1d1d5e;
}

.homepage {
  /* background-image: url("/images/Bg header.png"); */
}

/* .zaui-bottom-navigation-content {
    height: 60px;
} */

.zaui-bottom-navigation-content {
  height: calc(var(--zaui-safe-area-inset-bottom) + 60px);
}

.voucher-profile .wrap {
  width: 100%;
  background-color: rgb(240, 240, 240);
}

/* .voucher-profile .coupon {
    display: flex;
    overflow: hidden;
    border-radius: 10px;
    position: relative;
} */

.voucher-profile .wrap .coupon {
  display: flex;
  height: 150px !important;
}

.voucher-profile .coupon-left {
  height: 100%;
  width: 30%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.voucher-profile .coupon-left::before {
  content: "";
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: rgb(240, 240, 240);
  top: -10px;
  right: -10px;
  border-radius: 50%;
}

.voucher-profile .coupon-left::after {
  content: "";
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: rgb(240, 240, 240);
  bottom: -10px;
  right: -10px;
  border-radius: 50%;
}

.voucher-profile .coupon-con {
  height: 100%;
  width: 70%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.voucher-profile .coupon-con::before {
  content: "";
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: rgb(240, 240, 240);
  top: -10px;
  left: -10px;
  border-radius: 50%;
}

.voucher-profile .coupon-con::after {
  content: "";
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: rgb(240, 240, 240);
  bottom: -10px;
  left: -10px;
  border-radius: 50%;
}

.popup-voucher .MuiDialog-paperFullScreen {
  background-color: #e9ebed;
  height: 90%;
}

.popup-voucher .MuiDialog-scrollPaper {
  align-items: flex-end;
}

.popup-voucher .MuiDialogContent-root {
  background-color: rgb(240, 240, 240) !important;
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  /* Để căn giữa nội dung trong popup */
}

.popup-content p {
  margin-bottom: 20px;
}

.popup-content button {
  padding: 10px 20px;
  background-color: #1d1d5e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
  /* Hiệu ứng hover */
}

.popup-content button:hover {
  background-color: #1e8c75;
  /* Đổi màu khi hover */
}

.zaui-bottom-navigation-item .zaui-bottom-navigation-item-icon {
  justify-content: center;
}

.react-datepicker-wrapper input {
  padding: 17px;
  border-radius: 8px;
  border: 1px solid #b9bdc1;
  min-width: min-content;
  font-size: 1rem;
  width: 100%;
}

.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker-popper {
  left: 10px !important;
}

.search-homepage .MuiOutlinedInput-notchedOutline {
  display: none !important;
}

.search-homepage input {
  padding: 11px;
}

.slider-banner .slick-dots {
  position: absolute !important;
  bottom: 5%;
}

.slider-banner .slick-dots li {
  margin: 0;
}

.slider-banner .slick-dots li.slick-active button:before {
  color: #fff !important;
  opacity: 1 !important;
}

.zaui-bottom-navigation-item .zaui-bottom-navigation-item-label {
  font-weight: 700;
}

.category-product-page {
  margin-block: 15px;
}

.category-product-page .MuiTabs-flexContainer {
  gap: 8px;
  align-items: flex-start;
}

/* .category-product-page .MuiTabScrollButton-horizontal {
  display: none;
} */

.category-product-page .category-product-page-indicatorSpan {
  max-width: 80%;
  background: #3057a4;
  width: 100%;
}

.category-product-page-item {
  width: calc(100vw / 6 - 15px);
  max-width: 80px;
  padding: 0 !important;
  font-size: 10px !important;
}

.category-product-page-label {
  margin: 5px 0;
}

.category-product-page-icon-container {
  display: flex;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 10px;
  box-shadow: 0px 5.73px 12.6px 0px #26468445;
}

.slider-images-product-detail {
  margin-block: 10px;
}

.slider-images-product-detail .slick-track {
  display: flex;
  /* gap: 10px */
  margin-left: 10px;
}

.slider-images-product-detail .slick-slide {
  margin-right: 20px;
}

.slider-images-product-detail .slider-images-product-detail-item {
  background: #fff;
  border: 1px solid #e9ebed;
}

.datepicker-collab-order-history {
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.datepicker-collab-order-history:focus-visible {
  outline: none;
  border-color: #1d1d5e;
  border-width: 2px;
}

.slider-policy .slick-track {
  display: flex;
  gap: 15px;
}

.slider-policy .slick-slide {
  height: auto;

  & > div {
    height: 100%;

    .slider-policy-item {
      height: 100%;
    }
  }
}

.slider-policy .slick-dots {
  position: static;
}

.slick-track {
  margin-left: 0 !important;
}

.zaui-header {
  max-width: 425px;
  left: 50% !important;
  transform: translate(-50%, 0);
  min-width: 0;
}

#app {
  max-width: 425px;
  margin: auto;
}

.web-bottom-tab-navigation {
  max-width: 425px;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.product-item {
  animation-name: fadeIn;
  animation-delay: 0.3s;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-iteration-count: 1;
  animation-fill-mode: both;
}

.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.spinner-logo {
  width: 200px;
  height: 100%;
  object-fit: cover;
}

