import { Icon } from "@/constants/Assets";
import { COLORS } from "@/constants/themes";
import { Box, Typography } from "@mui/material";
import React from "react";
import { openShareSheet } from "zmp-sdk/apis";

interface IShareView {
  title: string;
  description: string;
  thumbnail: string;
  path?: string;
  overrideStyles?: React.CSSProperties;
}

export default function ShareView({
  title,
  description,
  thumbnail,
  path,
  overrideStyles,
}: IShareView) {
  const onShareCurrentPage = async () => {
    try {
      let sharedObj = {
        title,
        description,
        thumbnail,
      };
      console.log(thumbnail);
      if (path) {
        sharedObj = Object.assign(sharedObj, { path });
      }
      await openShareSheet({
        type: "zmp_deep_link",
        data: sharedObj,
      });
    } catch (err) {
      console.log("onShareCurrentPage error", err);
    }
  };

  return (
    <Box
      sx={{ ...styles.shareContainer, ...overrideStyles }}
      onClick={onShareCurrentPage}
    >
      <Box sx={styles.shareContent} flexDirection="row">
        <Typography style={styles.shareText}>
          Chia sẻ để nhận hoa hồng ngay
        </Typography>
      </Box>
      <img style={styles.image} src={Icon.icon_share_outline} />
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  shareContainer: {
    background: COLORS.primary,
    color: "#fff",
    fontWeight: 700,
    gap: 1,
    alignItems: "center",
    justifyItems: "baseline",
    justifyContent: "space-around",
    borderRadius: 2,
    display: "flex",
    cursor: "pointer",
  },
  shareContent: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  shareText: {
    fontSize: 16,
    paddingLeft: 8,
    fontWeight: 700,
    color: "#fff",
  },
  image: {
    marginRight: "15px",
  },
};
