import { Box, Container, Grid, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { Header } from "zmp-ui";
import styles from "../../css/styles.module.css";
import { COLORS } from "@/constants/themes";

export default function FrameContainerNoIcon({
  children,
  title,
  overrideStyle,
}: {
  children: ReactNode;
  title: string;
  overrideStyle?: React.CSSProperties;
}) {
  const [positionCss, setPositionCss] = useState({});
  const handleScroll = (e) => {
    const bottom =
      e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };

  return (
    <Box style={{ backgroundColor: "#F2F3F5", ...overrideStyle }}>
      <Header
        showBackIcon={false}
        style={{
          background: COLORS.colorHeader,
        }}
        textColor="#fff"
        title={title}
      />
      <Box className={styles.pageContent}>
        <Container onScroll={handleScroll} style={{ paddingBottom: 64 }}>
          <Stack paddingBlock={2}>{children}</Stack>
        </Container>
      </Box>
    </Box>
  );
}
