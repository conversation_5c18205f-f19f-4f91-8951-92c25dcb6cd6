import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { request } from "../../utils/request";

interface AppConfig {
  reviewing?: boolean;
  appName?: string;
  appSlogan?: string;
  FIRST_COMBO_NAME: string;
}

interface NotificationState {
  data: AppConfig;
  isLoading: boolean;
  error: string | null;
  deepLinkStartAppNavigated?: boolean | null;
}

const initialState: NotificationState = {
  data: { appName: "", appSlogan: "", FIRST_COMBO_NAME: "" },
  deepLinkStartAppNavigated: null,
  isLoading: true,
  error: null,
};

export const getConfig = createAsyncThunk("config/get", async () => {
  const response: any = await request("get", "/api/configs/app");
  return response;
});

const configSlice = createSlice({
  name: "config",
  initialState,
  reducers: {
    setDeepLinkStartAppNavigated: (
      state,
      action: PayloadAction<boolean | null>
    ) => {
      state.deepLinkStartAppNavigated = action?.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getConfig.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getConfig.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          if (payload.content?.APP_NAME) {
            state.data.appName = payload.content.APP_NAME;
          }
          if (payload.content?.APP_SLOGAN) {
            state.data.appSlogan = payload.content.APP_SLOGAN;
          }

          if (payload.FIRST_COMBO_NAME) {
            state.data.FIRST_COMBO_NAME = payload.FIRST_COMBO_NAME;
          }

          state.isLoading = false;
        }
      )
      .addCase(getConfig.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const { setDeepLinkStartAppNavigated } = configSlice.actions;
export default configSlice.reducer;
