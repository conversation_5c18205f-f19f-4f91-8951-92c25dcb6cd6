import React from "react";
import { Box, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { COLORS } from "@/constants/themes";
import GradientText from "../UI/GradientTextProps";

export default function CategoryItem({ item }) {
  const navigate = useNavigate();

  return (
    <Box
      display={"flex"}
      flexDirection={"column"}
      alignItems={"center"}
      style={{
        marginRight: "5px",
        cursor: "pointer",
        textAlign: "center",
      }}
      gap={1}
      onClick={() => {
        navigate(`/product?categoryId=${item.id}`);
      }}
    >
      <Box
        style={{
          width: "70px",
          height: "70px",
          borderRadius: "100%",
          display: "flex",
          justifyContent: "center",
          overflow: "hidden",
          background: "rgba(31, 57, 108, 0.2)",
        }}
      >
        <img
          src={`${import.meta.env.VITE_API_URL}${
            item?.image?.data?.attributes?.url
          }`}
          style={{
            width: "100%",
            objectFit: "cover",
            aspectRatio: "1/1",
          }}
        />
      </Box>
      <GradientText
        text={item.name}
        style={{ fontSize: 12, fontWeight: 700 }}
      />
    </Box>
  );
}
