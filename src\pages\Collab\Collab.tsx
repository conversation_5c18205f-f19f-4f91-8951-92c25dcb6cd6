import PopupNotificationCollabNeedRegister from "@/components/UI/PopupNotificationCollabNeedRegister";
import CollabMenuContent from "@/components/collab/CollabMenuContent";
import CollabMenuTool from "@/components/collab/CollabMenuTool";
import CollabOrderHistory from "@/components/collab/CollabOrderHistory";
import LayoutLogoPage from "@/components/layout/LayoutLogoPage";
import store, { AppDispatch, RootState } from "@/redux/store";
import {
  Button,
  CircularProgress,
  Container,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { getReport } from "@/redux/slices/team/team";
import { useDispatch, useSelector } from "react-redux";
import {
  getUserZalo,
  register,
  registerCollabration,
} from "@/redux/slices/authen/authSlice";
import { mapError } from "@/utils/common";
import { Icon } from "@/constants/Assets";
import { APP_NAME } from "@/constants/AppInfo";
import { useAlert } from "@/redux/slices/alert/useAlert";
import LayoutAccountPage from "@/components/layout/LayoutAccountPage";
import { getRouteParams } from "zmp-sdk";
import { DEFAULT_REFER_CODE } from "@/constants/Const";
import { COLORS } from "@/constants/themes";

export default function Collab() {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const { showAlert } = useAlert();
  const { user } = useSelector((state: RootState) => state.auth);
  const { refCode } = getRouteParams();

  useEffect(() => {
    store.dispatch(getReport());
  }, []);

  const onClickRegister = async () => {
    setLoading(true);
    const referCode = refCode || DEFAULT_REFER_CODE;
    const res = await dispatch(register(referCode)).unwrap();
    if (res.jwt) {
      showAlert({
        icon: Icon.check,
        title: "Kích hoạt tài khoản thành công",
      });
      await dispatch(
        registerCollabration({
          referCode: referCode,
        })
      );
      await dispatch(getUserZalo());
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  const renderActiveAccountPage = (): JSX.Element => {
    return (
      <LayoutAccountPage>
        <Container>
          <Stack
            justifyContent={"center"}
            alignItems={"center"}
            padding={2}
            style={{
              borderRadius: 20,
              background: "#fff",
              padding: "15px",
              boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
            }}
            height={500}
          >
            {loading ? (
              <CircularProgress />
            ) : (
              <>
                <Button
                  style={{
                    background: COLORS.primary,
                    fontWeight: 700,
                  }}
                  variant="contained"
                  onClick={onClickRegister}
                >
                  Kích hoạt tài khoản
                </Button>
                <Typography variant="body2" textAlign={"center"} pt={2}>
                  Bằng việc bấm "Kích hoạt tài khoản", chúng tôi hiểu rằng bạn
                  đã đồng ý với điều khoản của {APP_NAME}.
                </Typography>
              </>
            )}
          </Stack>
        </Container>
      </LayoutAccountPage>
    );
  };

  const renderCollabPage = (): JSX.Element => {
    return (
      <LayoutLogoPage>
        <Container style={{ padding: "0", marginBottom: "20px" }}>
          <CollabMenuContent />
        </Container>
        {/* <Container style={{ padding: "0 15px" }}>
          <Typography fontSize={16} fontWeight={700} color={COLORS.blue}>
            {" "}
            Lịch sử đơn hàng
          </Typography>
          <CollabOrderHistory isFilter={false} />
        </Container> */}
        {/* <PopupNotificationCollabNeedRegister /> */}
      </LayoutLogoPage>
    );
  };

  return !user ? renderActiveAccountPage() : renderCollabPage();
}
