import { ConfirmationNumber } from "@mui/icons-material";
import React from "react";
export const BottomTabs = [
  {
    label: "Trang chủ",
    value: "/",
    icon: (color) => (
      <svg
        width="27"
        height="24"
        viewBox="0 0 27 24"
        fill={color}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M21.3694 8.28014V1.80435H18.3384V5.2629L13.7936 0.735352L0.862549 13.6112V15.1905H3.94531V23.5733H12.0074V18.5216H15.5453V23.5733H23.6419V15.1905H26.7246V13.6112L21.3694 8.28014Z" />
      </svg>
    ),
  },
  {
    label: "Giỏ hàng",
    value: "/cart",
    icon: (color) => (
      <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        xmlns="http://www.w3.org/2000/svg"
        fill={color}
      >
        <path d="M5.82287 16.8003C6.24721 16.8003 6.65417 16.9689 6.95423 17.2689C7.25428 17.569 7.42285 17.9759 7.42285 18.4003C7.42285 18.8246 7.25428 19.2316 6.95423 19.5316C6.65417 19.8317 6.24721 20.0002 5.82287 20.0002C5.39854 20.0002 4.99158 19.8317 4.69152 19.5316C4.39147 19.2316 4.2229 18.8246 4.2229 18.4003C4.2229 17.9759 4.39147 17.569 4.69152 17.2689C4.99158 16.9689 5.39854 16.8003 5.82287 16.8003ZM15.4227 16.8003C15.8471 16.8003 16.254 16.9689 16.5541 17.2689C16.8541 17.569 17.0227 17.9759 17.0227 18.4003C17.0227 18.8246 16.8541 19.2316 16.5541 19.5316C16.254 19.8317 15.8471 20.0002 15.4227 20.0002C14.9984 20.0002 14.5914 19.8317 14.2914 19.5316C13.9913 19.2316 13.8227 18.8246 13.8227 18.4003C13.8227 17.9759 13.9913 17.569 14.2914 17.2689C14.5914 16.9689 14.9984 16.8003 15.4227 16.8003Z" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.453784 0.0789286C0.645118 -0.0129877 0.865129 -0.0251321 1.06542 0.0451668L1.38755 0.158232L1.40762 0.165303C2.05599 0.3937 2.60801 0.588156 3.04299 0.801421C3.51338 1.03288 3.91657 1.31661 4.2195 1.76034C4.5203 2.1998 4.64403 2.68299 4.70163 3.21312C4.72765 3.47478 4.74295 3.73739 4.74749 4.0003H16.0956C17.8929 4.0003 19.5099 4.0003 19.9835 4.61576C20.4571 5.23122 20.2726 6.15814 19.9025 8.01411L19.3691 10.6007C19.0331 12.2306 18.8646 13.0455 18.2758 13.5255C17.6881 14.0055 16.855 14.0055 15.19 14.0055H9.53462C7.98107 14.0055 6.83332 14.0055 5.95965 13.8668H5.33394V13.7288C4.84758 13.5844 4.46471 13.3648 4.14803 13.0306C3.22325 12.0556 3.15605 11.021 3.15605 7.88291V5.10749C3.15605 4.31817 3.15392 3.79017 3.11125 3.38485C3.06859 2.99765 2.99392 2.80352 2.89792 2.66272C2.80406 2.52619 2.66219 2.39713 2.339 2.2382C1.99447 2.0686 1.52621 1.90327 0.813688 1.65261L0.535292 1.55554C0.335004 1.48524 0.170845 1.33826 0.0789288 1.14693C-0.0129876 0.955592 -0.0251322 0.735581 0.0451667 0.535292C0.115466 0.335004 0.26245 0.170845 0.453784 0.0789286Z"
        />
        <path
          d="M14.3447 5.47809C14.2131 5.40485 14.0683 5.35828 13.9187 5.34103C13.7691 5.32379 13.6176 5.3362 13.4727 5.37757C13.3279 5.41894 13.1927 5.48845 13.0747 5.58213C12.9568 5.6758 12.8585 5.79181 12.7854 5.92351L10.6569 9.75404L9.43873 8.53586C9.33297 8.42636 9.20646 8.33901 9.06658 8.27893C8.9267 8.21884 8.77625 8.18721 8.62402 8.18589C8.47178 8.18456 8.32081 8.21357 8.1799 8.27122C8.039 8.32887 7.91099 8.414 7.80334 8.52165C7.69569 8.6293 7.61056 8.75731 7.55291 8.89822C7.49526 9.03912 7.46625 9.19009 7.46757 9.34233C7.4689 9.49456 7.50052 9.64501 7.56061 9.78489C7.6207 9.92477 7.70804 10.0513 7.81755 10.157L10.1106 12.4501C10.3273 12.6674 10.6197 12.7866 10.9212 12.7866L11.08 12.7751C11.2557 12.7506 11.4233 12.6855 11.5697 12.5851C11.716 12.4848 11.837 12.3518 11.9232 12.1967L14.7896 7.03736C14.8627 6.90577 14.9093 6.76105 14.9265 6.61147C14.9438 6.46189 14.9314 6.31038 14.8901 6.16559C14.8488 6.02079 14.7794 5.88555 14.6858 5.76759C14.5922 5.64963 14.4763 5.55126 14.3447 5.47809Z"
          fill="white"
        />
      </svg>
    ),
  },
  {
    label: "Ưu đãi",
    value: "/voucher",
    icon: (color) => (
      <svg
        width="20"
        height="22"
        viewBox="0 0 20 16"
        fill={color}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.8 4L14 5.2L7.2 12L6 10.8L12.8 4ZM2 0H18C19.11 0 20 0.89 20 2V6C19.4696 6 18.9609 6.21071 18.5858 6.58579C18.2107 6.96086 18 7.46957 18 8C18 8.53043 18.2107 9.03914 18.5858 9.41421C18.9609 9.78929 19.4696 10 20 10V14C20 15.11 19.11 16 18 16H2C1.46957 16 0.960859 15.7893 0.585786 15.4142C0.210714 15.0391 0 14.5304 0 14V10C1.11 10 2 9.11 2 8C2 7.46957 1.78929 6.96086 1.41421 6.58579C1.03914 6.21071 0.530433 6 0 6V2C0 1.46957 0.210714 0.960859 0.585786 0.585786C0.960859 0.210714 1.46957 0 2 0ZM2 2V4.54C2.60768 4.8904 3.11236 5.39466 3.46325 6.00205C3.81415 6.60944 3.9989 7.29854 3.9989 8C3.9989 8.70146 3.81415 9.39056 3.46325 9.99795C3.11236 10.6053 2.60768 11.1096 2 11.46V14H18V11.46C17.3923 11.1096 16.8876 10.6053 16.5367 9.99795C16.1858 9.39056 16.0011 8.70146 16.0011 8C16.0011 7.29854 16.1858 6.60944 16.5367 6.00205C16.8876 5.39466 17.3923 4.8904 18 4.54V2H2ZM7.5 4C8.33 4 9 4.67 9 5.5C9 6.33 8.33 7 7.5 7C6.67 7 6 6.33 6 5.5C6 4.67 6.67 4 7.5 4ZM12.5 9C13.33 9 14 9.67 14 10.5C14 11.33 13.33 12 12.5 12C11.67 12 11 11.33 11 10.5C11 9.67 11.67 9 12.5 9Z"
          fill={color}
        />
      </svg>
    ),
  },
  {
    label: "Đại lý",
    value: "/collab",
    icon: (color) => (
      <svg
        width="30"
        height="20"
        viewBox="0 0 30 20"
        fill={color}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M4.35484 8.57143C5.95615 8.57143 7.25806 7.29018 7.25806 5.71429C7.25806 4.13839 5.95615 2.85714 4.35484 2.85714C2.75353 2.85714 1.45161 4.13839 1.45161 5.71429C1.45161 7.29018 2.75353 8.57143 4.35484 8.57143ZM24.6774 8.57143C26.2787 8.57143 27.5806 7.29018 27.5806 5.71429C27.5806 4.13839 26.2787 2.85714 24.6774 2.85714C23.0761 2.85714 21.7742 4.13839 21.7742 5.71429C21.7742 7.29018 23.0761 8.57143 24.6774 8.57143ZM26.129 10H23.2258C22.4274 10 21.7062 10.317 21.1799 10.8304C23.0081 11.817 24.3054 13.5982 24.5867 15.7143H27.5806C28.3836 15.7143 29.0323 15.0759 29.0323 14.2857V12.8571C29.0323 11.2812 27.7303 10 26.129 10ZM14.5161 10C17.3241 10 19.5968 7.76339 19.5968 5C19.5968 2.23661 17.3241 0 14.5161 0C11.7082 0 9.43548 2.23661 9.43548 5C9.43548 7.76339 11.7082 10 14.5161 10ZM18 11.4286H17.6235C16.6799 11.875 15.6321 12.1429 14.5161 12.1429C13.4002 12.1429 12.3569 11.875 11.4088 11.4286H11.0323C8.14718 11.4286 5.80645 13.7321 5.80645 16.5714V17.8571C5.80645 19.0402 6.78175 20 7.98387 20H21.0484C22.2505 20 23.2258 19.0402 23.2258 17.8571V16.5714C23.2258 13.7321 20.8851 11.4286 18 11.4286ZM7.85232 10.8304C7.32611 10.317 6.60484 10 5.80645 10H2.90323C1.30192 10 0 11.2812 0 12.8571V14.2857C0 15.0759 0.64869 15.7143 1.45161 15.7143H4.44103C4.72681 13.5982 6.02419 11.817 7.85232 10.8304Z" />
      </svg>
    ),
  },
  {
    label: "Tài khoản",
    value: "/profile",
    icon: (color) => (
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill={color}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M10.001 0C11.3271 0 12.5988 0.526784 13.5365 1.46447C14.4742 2.40215 15.001 3.67392 15.001 5C15.001 6.32608 14.4742 7.59785 13.5365 8.53553C12.5988 9.47322 11.3271 10 10.001 10C8.67489 10 7.40312 9.47322 6.46544 8.53553C5.52776 7.59785 5.00098 6.32608 5.00098 5C5.00098 3.67392 5.52776 2.40215 6.46544 1.46447C7.40312 0.526784 8.67489 0 10.001 0ZM10.001 12.5C15.526 12.5 20.001 14.7375 20.001 17.5V20H0.000976562V17.5C0.000976562 14.7375 4.47598 12.5 10.001 12.5Z" />
      </svg>
    ),
  },
];
