import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IVoucher } from "../../../types/voucher";

interface AuthState {
  list: any;
  voucherList: IVoucher[];
  isLoading: boolean;
}

const initialState: AuthState = {
  list: [],
  voucherList: [],
  isLoading: true,
};

export const getOwnerVoucherList = createAsyncThunk(
  "voucher/getOwnerVoucherList",
  async () => {
    const response: any = await request("get", "/api/my-voucher");
    return response;
  }
);

const myVoucherSlice = createSlice({
  name: "myVouchers",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getOwnerVoucherList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getOwnerVoucherList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.voucherList = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getOwnerVoucherList.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default myVoucherSlice.reducer;
