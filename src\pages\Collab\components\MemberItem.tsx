import { Box, Grid, Stack, Typography } from "@mui/material";
import dayjs from "dayjs";
import React from "react";
import { LevelName } from "../../../constants/Const";
import { COLORS } from "@/constants/themes";
import { Avatar } from "zmp-ui";
import { Icon } from "@/constants/Assets";
import { formatPrice } from "@/utils/formatPrice";

type ItemType = {
  icon: string;
  title: string;
  value: number;
  isPrice?: boolean;
};

export default function MemberItem({ item, onClick }) {
  const infoItems: Array<ItemType> = [
    {
      icon: Icon.icon_members_2,
      title: "Tổng thành viên",
      value: item.totalChildren?.length ?? 0,
    },
    {
      icon: Icon.icon_branch,
      title: "Số lượng nhánh",
      value: item.totalBranch ?? 0,
    },
    {
      icon: Icon.icon_commission_2,
      title: "Hoa hồng",
      value: item.totalCommissionSuccess ?? 0,
      isPrice: true,
    },
    {
      icon: Icon.icon_team_sale,
      title: "<PERSON><PERSON>h số nhóm",
      value: item.myTeamSale ?? 0,
      isPrice: true,
    },
    {
      icon: Icon.icon_person_sale,
      title: "Doanh số cá nhân",
      value: item.mySale ?? 0,
      isPrice: true,
    },
  ];

  return (
    <Stack style={styles.container} onClick={onClick}>
      <Grid container sx={{ paddingBottom: 1 }}>
        <Grid item xs={7}>
          <Box sx={{ display: "flex" }} alignItems="center">
            <Box>
              <Avatar src={item.avatarUrl} size={46} />
            </Box>
            <Box width="100%" paddingLeft="8px">
              <Typography style={styles.usernameText}>
                {item.name ?? "N/A"} - {item.phone ?? "N/A"}
              </Typography>
              <Typography style={{ ...styles.smallTitle }}>
                Ngày tham gia: {dayjs(item.createdAt).format("DD/MM/YYYY")}
              </Typography>
            </Box>
          </Box>
        </Grid>
        <Grid item xs={5}>
          <Box justifyContent="flex-end">
            <Typography style={styles.rightText}>
              Hệ cấp: <span style={styles.levelText}>F{item.fLevel}</span>
            </Typography>
            <Typography style={styles.rightText}>
              Hạng: <span style={styles.rankText}>{LevelName[item.level]}</span>
            </Typography>
          </Box>
        </Grid>
      </Grid>
      {infoItems.map((info) => (
        <Box key={info.title} sx={styles.infoItemContainer}>
          <Box sx={{ display: "flex" }} paddingBlock={1}>
            <img
              src={info.icon}
              width={20}
              height={20}
              style={{ objectFit: "contain" }}
            />
            <Typography style={styles.infoTitleText}>{info.title}</Typography>
          </Box>
          <Typography style={styles.valueText}>
            {info.isPrice ? formatPrice(info.value) : info.value}
          </Typography>
        </Box>
      ))}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: "#fff",
    border: "1px solid #D9D9D9",
    borderRadius: 10,
    paddingBlock: 12,
    paddingInline: 12,
    marginBlock: 8,
  },
  smallTitle: {
    fontSize: 12,
  },
  processContainer: {
    background: "#B3B3B3",
    height: 7,
    width: "100%",
    marginBlock: 2,
  },
  currentProcess: {
    background: "#29BB9C",
    height: 7,
    width: "80%",
  },
  rightText: {
    color: COLORS.neutral4,
    textAlign: "right",
    paddingBottom: 12,
  },
  levelText: {
    color: "#FF9900",
    fontWeight: 700,
  },
  rankText: {
    color: "#29BB9C",
    fontWeight: 700,
  },
  infoItemContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  usernameText: {
    fontWeight: 700,
    color: COLORS.blue,
    paddingBottom: 12,
  },
  valueText: {
    color: COLORS.blue,
    fontWeight: 700,
  },
  infoTitleText: {
    color: COLORS.gray,
    paddingLeft: 8,
  },
};
