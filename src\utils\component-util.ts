import { <PERSON>rowser<PERSON>outer } from "react-router-dom";
import { ZMPRouter } from "zmp-ui";
import { useNavigate as useBrowserNavigate } from "react-router-dom";
import { useNavigate as useZMPNavigate } from "zmp-ui";
import WebBottomTabNavigation from "../components/menu/WebBottomTabNavigation";
import ZaloBottomTabNavigation from "../components/menu/ZaloBottomTabNavigation";
import { Platform } from "../config";

export const AppRouter = Platform === "web" ? BrowserRouter : ZMPRouter;

export const useNavigate =
  Platform === "web" ? useBrowserNavigate : useZMPNavigate;

export const BottomNavigation =
  Platform === "web" ? WebBottomTabNavigation : ZaloBottomTabNavigation;
