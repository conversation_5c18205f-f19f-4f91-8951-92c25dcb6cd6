import React, { useState } from "react";
import { formatPrice } from "@/utils/formatPrice";
import { Box, Collapse, Typography } from "@mui/material";
import Divider from "@mui/material/Divider";
import { TransitionGroup } from "react-transition-group";
import { Icon } from "@/constants/Assets";
import { COLORS } from "@/constants/themes";
import { ICommissionSale } from "@/types/team";

interface ICollapseCommission {
  title: string;
  data?: ICommissionSale;
  isFirstOpen?: boolean;
}

export default function CollapseCommission({
  title,
  data,
  isFirstOpen,
}: ICollapseCommission) {
  const [isOpen, setOpen] = useState<boolean>(isFirstOpen ?? false);

  const PriceItem = ({ title, price }) => (
    <Box display="flex" justifyContent="space-between" paddingBlock={1}>
      <Typography>{title}</Typography>
      <Typography style={styles.priceText}>{formatPrice(price)}</Typography>
    </Box>
  );

  const totalCommission =
    data?.totalCommissionF1?.commission +
    data?.totalCommissionF2?.commission +
    data?.totalFixedCommission;
  return (
    <Box sx={styles.container}>
      <Box
        display="flex"
        justifyContent="space-between"
        onClick={() => setOpen(!isOpen)}
      >
        <Typography style={{ fontWeight: 700, color: COLORS.blue }}>
          {title}
        </Typography>
        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography style={styles.priceText}>
            {formatPrice(totalCommission)}
          </Typography>
          <img
            src={isOpen ? Icon.icon_up : Icon.icon_down}
            width={20}
            height={10}
            style={{ paddingLeft: 4 }}
          />
        </Box>
      </Box>
      <TransitionGroup>
        {isOpen && (
          <Collapse>
            <Divider style={{ marginBlock: 8 }} />
            <Typography style={{ fontWeight: 700 }}>Trực tiếp</Typography>
            <PriceItem
              title="Doanh số"
              price={data?.totalCommissionF1?.sales || 0}
            />
            <PriceItem
              title="Hoa hồng"
              price={data?.totalCommissionF1?.commission || 0}
            />
            <PriceItem
              title="Hoa hồng cố định"
              price={data?.totalFixedCommission || 0}
            />
            <Divider style={{ marginBlock: 8 }} />
            <Typography style={{ fontWeight: 700 }}>Gián tiếp</Typography>
            <PriceItem
              title="Doanh số"
              price={data?.totalCommissionF2?.sales || 0}
            />
            <PriceItem
              title="Hoa hồng"
              price={data?.totalCommissionF2?.commission || 0}
            />
          </Collapse>
        )}
      </TransitionGroup>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: 2,
    background: "#fff",
    padding: 1.5,
    border: 1,
    borderColor: "#D9D9D9",
    marginBlock: 1,
  },
  priceText: {
    fontWeight: 700,
    color: COLORS.primary,
  },
};
