import React from "react";
import Slider from "react-slick";
import { useSelector } from "react-redux";
import store, { RootState } from "../../redux/store";
import ProductItem from "./ProductItem";
import { IProduct } from "../../types/product";
import { Icon } from "@/constants/Assets";

export default function ProductSlider({ productList }) {
  const productListing = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 2,
    arrows: false,
  };

  return (
    <Slider {...productListing}>
      {productList?.map((product: IProduct) => (
        <ProductItem key={product.id} item={product} />
      ))}
    </Slider>
  );
}
