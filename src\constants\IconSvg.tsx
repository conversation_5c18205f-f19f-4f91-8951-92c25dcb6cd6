import React from "react";

export const MoneySVG = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    viewBox="0 0 20 20"
  >
    <path
      fill="#0973BA"
      d="M10.75 8.75c-.5-.125-1-.375-1.375-.75-.375-.125-.5-.5-.5-.75S9 6.625 9.25 6.5c.375-.25.75-.5 1.125-.375.75 0 1.375.375 1.75.875l1.125-1.5c-.375-.375-.75-.625-1.125-.875s-.875-.375-1.375-.375V2.5h-1.5v1.75c-.625.125-1.25.5-1.75 1-.5.625-.875 1.375-.75 2.125 0 .75.25 1.5.75 2 .625.625 1.5 1 2.25 1.375.375.125.875.375 1.25.625.25.25.375.625.375 1s-.125.75-.375 1.125c-.375.375-.875.5-1.25.5-.5 0-1.125-.125-1.5-.5a3.804 3.804 0 0 1-1-1L6 13.875c.375.5.75.875 1.25 1.25.625.375 1.375.75 2.125.75V17.5h1.375v-1.875c.75-.125 1.375-.5 1.875-1 .625-.625 1-1.625 1-2.5 0-.75-.25-1.625-.875-2.125-.625-.625-1.25-1-2-1.25ZM10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0Zm0 18.625c-4.75 0-8.625-3.875-8.625-8.625S5.25 1.375 10 1.375 18.625 5.25 18.625 10 14.75 18.625 10 18.625Z"
    />
  </svg>
);
export const Address = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="15"
    fill="none"
    viewBox="0 0 20 15"
  >
    <path
      fill="#0973BA"
      d="M4.545 14.546a2.63 2.63 0 0 1-1.931-.796 2.63 2.63 0 0 1-.796-1.932H.91a.88.88 0 0 1-.648-.262A.876.876 0 0 1 0 10.91V1.82C0 1.318.178.89.535.533A1.747 1.747 0 0 1 1.818 0h10.91c.5 0 .928.178 1.284.535.356.356.534.784.534 1.283v1.818h1.818a1.807 1.807 0 0 1 1.454.728l2 2.659a.8.8 0 0 1 .136.25c.************.046.295v3.341a.88.88 0 0 1-.262.648.876.876 0 0 1-.647.261h-.91a2.63 2.63 0 0 1-.795 1.932 2.63 2.63 0 0 1-1.931.796 2.63 2.63 0 0 1-1.932-.796 2.63 2.63 0 0 1-.796-1.932H7.273a2.63 2.63 0 0 1-.796 1.932 2.63 2.63 0 0 1-1.932.796Zm0-1.819a.88.88 0 0 0 .649-.261.876.876 0 0 0 .26-.648.88.88 0 0 0-.261-.648.876.876 0 0 0-.648-.26.88.88 0 0 0-.648.26.876.876 0 0 0-.26.648.88.88 0 0 0 .261.648c.175.175.39.262.647.261ZM1.818 10h.727c.258-.273.553-.492.887-.66a2.46 2.46 0 0 1 1.113-.25c.41 0 .78.084 1.114.25.333.168.629.387.886.66h6.182V1.818H1.818V10Zm13.636 2.727a.88.88 0 0 0 .649-.261.876.876 0 0 0 .26-.648.88.88 0 0 0-.261-.648.876.876 0 0 0-.648-.26.88.88 0 0 0-.648.26.876.876 0 0 0-.26.648.88.88 0 0 0 .261.648c.175.175.39.262.647.261Zm-.909-4.545h3.864l-2.045-2.727h-1.819v2.727Z"
    />
  </svg>
);

export const Voucher = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="16"
    fill="none"
    viewBox="0 0 20 16"
  >
    <path
      fill="#0973BA"
      d="M12.8 4 14 5.2 7.2 12 6 10.8 12.8 4ZM2 0h16c1.11 0 2 .89 2 2v4a2 2 0 0 0 0 4v4c0 1.11-.89 2-2 2H2a2 2 0 0 1-2-2v-4c1.11 0 2-.89 2-2a2 2 0 0 0-2-2V2a2 2 0 0 1 2-2Zm0 2v2.54a3.994 3.994 0 0 1 0 6.92V14h16v-2.54a3.994 3.994 0 0 1 0-6.92V2H2Zm5.5 2C8.33 4 9 4.67 9 5.5S8.33 7 7.5 7 6 6.33 6 5.5 6.67 4 7.5 4Zm5 5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5Z"
    />
  </svg>
);

export const AppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    viewBox="0 0 20 20"
  >
    <path
      fill="#0973BA"
      d="M0 0h9v9H0V0Zm2 2v5h5V2H2Zm13.5 0a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5ZM11 4.5a4.5 4.5 0 1 1 9.002 0A4.5 4.5 0 0 1 11 4.5ZM0 11h9v9H0v-9Zm2 2v5h5v-5H2Zm9-2h9v9h-9v-9Zm2 2v5h5v-5h-5Z"
    />
  </svg>
);
export const Setting = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    viewBox="0 0 20 20"
  >
    <path
      fill="#0973BA"
      d="M18.044 10.661a1 1 0 0 1 0-1.32l1.28-1.44a1 1 0 0 0 .12-1.17l-2-3.46a1 1 0 0 0-1.07-.48l-1.88.38a1 1 0 0 1-1.15-.66l-.61-1.83a1 1 0 0 0-.95-.68h-4a1 1 0 0 0-1 .68l-.56 1.83a1 1 0 0 1-1.15.66l-1.93-.38a1 1 0 0 0-1 .48l-2 3.46a1 1 0 0 0 .1 1.17l1.27 1.44a1 1 0 0 1 0 1.32l-1.27 1.44a1 1 0 0 0-.1 1.17l2 3.46a1 1 0 0 0 1.07.48l1.88-.38a1 1 0 0 1 1.15.66l.61 1.83a1 1 0 0 0 1 .68h4a1 1 0 0 0 .95-.68l.61-1.83a1 1 0 0 1 1.15-.66l1.88.38a1 1 0 0 0 1.07-.48l2-3.46a1 1 0 0 0-.12-1.17l-1.35-1.44Zm-1.49 1.34.8.9-1.28 2.22-1.18-.24a3 3 0 0 0-3.45 2l-.38 1.12h-2.56l-.36-1.14a3 3 0 0 0-3.45-2l-1.18.24-1.3-2.21.8-.9a3 3 0 0 0 0-4l-.8-.9 1.28-2.2 1.18.24a3 3 0 0 0 3.45-2l.38-1.13h2.56l.38 1.14a3 3 0 0 0 3.45 2l1.18-.24 1.28 2.22-.8.9a3 3 0 0 0 0 3.98Zm-6.77-6a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm0 6a2 2 0 1 1 0-4 2 2 0 0 1 0 4Z"
    />
  </svg>
);
export const Policy = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="22"
    fill="none"
    viewBox="0 0 20 22"
  >
    <path
      fill="#0973BA"
      d="M20 12.307a4.616 4.616 0 1 0-7.692 3.431v5.8l3.077-1.456 3.077 1.456v-5.8A4.6 4.6 0 0 0 20 12.308Zm-3.077 6.8-1.538-.728-1.539.729v-2.454a4.53 4.53 0 0 0 3.077 0v2.454Zm-1.538-3.723a3.077 3.077 0 1 1 0-6.153 3.077 3.077 0 0 1 0 6.153ZM3.846 9.232h5.385v1.538H3.846V9.231Zm0-4.616h7.693v1.539H3.846v-1.54Z"
    />
    <path
      fill="#0973BA"
      d="M1.538 21.538A1.54 1.54 0 0 1 0 20V1.538A1.54 1.54 0 0 1 1.538 0h12.308a1.54 1.54 0 0 1 1.539 1.538v3.077h-1.539V1.538H1.538V20h7.693v1.538H1.538Z"
    />
  </svg>
);
export const Question = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    viewBox="0 0 20 20"
  >
    <path
      fill="#0973BA"
      d="M10 0a10 10 0 1 0 10 10A10.01 10.01 0 0 0 10 0Zm0 16.154a1.154 1.154 0 1 1 0-2.308 1.154 1.154 0 0 1 0 2.308Zm.77-4.685v.07a.77.77 0 1 1-1.54 0v-.77A.77.77 0 0 1 10 10c1.272 0 2.308-.865 2.308-1.923S11.272 6.154 10 6.154c-1.272 0-2.308.865-2.308 1.923v.385a.77.77 0 1 1-1.538 0v-.385c0-1.909 1.725-3.462 3.846-3.462s3.846 1.553 3.846 3.462c0 1.671-1.323 3.07-3.077 3.392Z"
    />
  </svg>
);

export const LogoutIcon = () => (
  <svg
    width="30px"
    height="30px"
    viewBox="0 0 30 30"
    xmlns="http://www.w3.org/2000/svg"
    fill="red"
  >
    <path d="M25.429 21.446H23.37a0.45 0.45 0 0 0 -0.36 0.17 9 9 0 0 1 -0.657 0.717 10.35 10.35 0 0 1 -3.301 2.223 10.35 10.35 0 0 1 -4.04 0.815 10.35 10.35 0 0 1 -4.04 -0.815 10.35 10.35 0 0 1 -3.301 -2.223 10.35 10.35 0 0 1 -2.226 -3.296c-0.543 -1.276 -0.816 -2.632 -0.816 -4.037a10.35 10.35 0 0 1 3.041 -7.334 10.35 10.35 0 0 1 7.341 -3.037 10.35 10.35 0 0 1 4.04 0.815 10.35 10.35 0 0 1 3.301 2.223q0.345 0.347 0.657 0.717a0.465 0.465 0 0 0 0.36 0.17h2.059a0.234 0.234 0 0 0 0.197 -0.36 12.6 12.6 0 0 0 -10.646 -5.79 12.585 12.585 0 0 0 -12.578 12.726c0.071 6.9 5.69 12.47 12.609 12.47a12.6 12.6 0 0 0 10.614 -5.792 0.234 0.234 0 0 0 -0.197 -0.36m2.604 -6.63L23.875 11.535a0.235 0.235 0 0 0 -0.381 0.184v2.226H14.297a0.234 0.234 0 0 0 -0.234 0.234v1.641c0 0.129 0.105 0.234 0.234 0.234h9.2v2.228c0 0.197 0.228 0.307 0.381 0.184l4.158 -3.282a0.234 0.234 0 0 0 0 -0.369" />
  </svg>
);

export const ZaloIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="26"
    height="26"
    fill="none"
    viewBox="0 0 26 26"
  >
    <path fill="url(#a)" d="M0 0h26v26H0z" />
    <defs>
      <pattern
        id="a"
        width="1"
        height="1"
        patternContentUnits="objectBoundingBox"
      >
        <use href="#b" transform="scale(.00083)" />
      </pattern>
      <image
        id="b"
        width="1200"
        height="1200"
        data-name="Icon_of_Zalo.png"
        href="data:image/png;base64,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"
      />
    </defs>
  </svg>
);

export const SurPlus = () => (
  <svg
    width="15"
    height="14"
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.2862 3.2877C12.4841 3.30654 12.6709 3.30968 12.85 3.34423C13.7686 3.52168 14.4423 4.32099 14.4439 5.25377C14.4502 7.18688 14.4502 9.11998 14.4439 11.0531C14.4407 12.1429 13.5739 12.9925 12.4574 13.016C12.2265 13.0207 11.9973 13.0176 11.7664 13.0176C8.67597 13.0176 5.58553 13.0192 2.49665 13.016C1.63139 13.016 0.976551 12.6423 0.607519 11.8508C0.492883 11.6043 0.428499 11.3106 0.428499 11.0389C0.414366 8.16363 0.417506 5.28518 0.420647 2.4083C0.420647 1.3122 1.28748 0.428089 2.38515 0.424948C5.0202 0.417096 7.65525 0.415526 10.2903 0.424948C11.4351 0.429659 12.2784 1.29806 12.2862 2.44599C12.2878 2.72708 12.2862 3.0066 12.2862 3.2877ZM1.49791 4.16395C1.49791 4.24875 1.49791 4.30371 1.49791 4.35867C1.49791 6.55559 1.49791 8.75408 1.49791 10.951C1.49791 11.5666 1.86694 11.9403 2.48252 11.9403C5.78025 11.9419 9.07956 11.9419 12.3773 11.9403C12.9944 11.9403 13.3666 11.5697 13.3666 10.9557C13.3666 9.09014 13.3666 7.22456 13.3666 5.35899C13.3666 5.28047 13.365 5.20038 13.3509 5.12501C13.2614 4.66018 12.9049 4.38694 12.3883 4.38694C9.30412 4.38694 6.21838 4.38851 3.13421 4.38537C2.58616 4.3838 2.03026 4.44661 1.49791 4.16395ZM11.2058 3.29712C11.2058 3.04115 11.2058 2.79618 11.2058 2.5512C11.2074 1.85554 10.8556 1.50221 10.1615 1.50221C7.61599 1.50221 5.07202 1.50221 2.52649 1.50221C2.45896 1.50221 2.39144 1.50064 2.32391 1.50849C1.84181 1.56188 1.49477 1.94191 1.49791 2.41144C1.50105 2.92809 1.91091 3.30497 2.47623 3.30497C5.32485 3.30654 8.17346 3.30497 11.0221 3.30497C11.077 3.30497 11.132 3.30026 11.2058 3.29712Z"
      fill="#1D1D5E"
    />
  </svg>
);

export const Wallet = () => (
  <svg
    width="13"
    height="11"
    viewBox="0 0 13 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.4045 2.19958H1.71518C1.58669 2.19958 1.46346 2.14854 1.37261 2.05768C1.28175 1.96683 1.23071 1.8436 1.23071 1.71512C1.23071 1.58663 1.28175 1.4634 1.37261 1.37255C1.46346 1.28169 1.58669 1.23065 1.71518 1.23065H9.95109C10.0796 1.23065 10.2028 1.17961 10.2937 1.08875C10.3845 0.997898 10.4356 0.874673 10.4356 0.746184C10.4356 0.617696 10.3845 0.49447 10.2937 0.403616C10.2028 0.312761 10.0796 0.261719 9.95109 0.261719H1.71518C1.32971 0.261719 0.960035 0.414844 0.68747 0.687409C0.414905 0.959974 0.26178 1.32965 0.26178 1.71512V9.46657C0.26178 9.85203 0.414905 10.2217 0.68747 10.4943C0.960035 10.7668 1.32971 10.92 1.71518 10.92H11.4045C11.6615 10.92 11.9079 10.8179 12.0896 10.6362C12.2713 10.4545 12.3734 10.208 12.3734 9.95103V3.16851C12.3734 2.91154 12.2713 2.66508 12.0896 2.48337C11.9079 2.30166 11.6615 2.19958 11.4045 2.19958ZM9.22439 7.04424C9.08067 7.04424 8.94017 7.00162 8.82066 6.92177C8.70116 6.84192 8.60801 6.72842 8.55301 6.59563C8.49801 6.46285 8.48362 6.31673 8.51166 6.17577C8.5397 6.0348 8.60891 5.90532 8.71054 5.80369C8.81217 5.70205 8.94166 5.63284 9.08262 5.6048C9.22359 5.57676 9.3697 5.59116 9.50249 5.64616C9.63527 5.70116 9.74877 5.7943 9.82862 5.91381C9.90847 6.03331 9.95109 6.17381 9.95109 6.31754C9.95109 6.51027 9.87453 6.69511 9.73825 6.83139C9.60196 6.96767 9.41712 7.04424 9.22439 7.04424Z"
      fill="#1D1D5E"
    />
  </svg>
);

export const Bill = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.45908 0.716797H2.03738C1.79408 0.716797 1.56073 0.81345 1.38869 0.985493C1.21665 1.15754 1.12 1.39088 1.12 1.63418V11.7254C1.12 11.9687 1.21665 12.2021 1.38869 12.3741C1.56073 12.5462 1.79408 12.6428 2.03738 12.6428H12.1286C12.3719 12.6428 12.6053 12.5462 12.7773 12.3741C12.9494 12.2021 13.046 11.9687 13.046 11.7254V5.30373L8.45908 0.716797Z"
      stroke="#1D1D5E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.78971 4.38634V3.01026M4.78971 4.38634C4.02828 4.38634 3.41364 4.38634 3.41364 5.30373C3.41364 6.67981 6.16579 6.67981 6.16579 8.05589C6.16579 8.97327 5.55115 8.97327 4.78971 8.97327M4.78971 4.38634C5.55115 4.38634 6.16579 4.73495 6.16579 5.30373M3.41364 8.05589C3.41364 8.74393 4.02828 8.97327 4.78971 8.97327M4.78971 8.97327V10.3494M8.45926 8.97327H11.2114M8.45926 4.84504V0.716797L13.0462 5.30373H8.91795C8.7963 5.30373 8.67963 5.2554 8.59361 5.16938C8.50759 5.08336 8.45926 4.96669 8.45926 4.84504Z"
      stroke="#1D1D5E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Cart = () => (
  <svg
    width="15"
    height="14"
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.54631 11.4678C4.8254 11.4678 5.09306 11.5786 5.2904 11.776C5.48774 11.9733 5.59861 12.241 5.59861 12.5201C5.59861 12.7992 5.48774 13.0668 5.2904 13.2642C5.09306 13.4615 4.8254 13.5724 4.54631 13.5724C4.26723 13.5724 3.99957 13.4615 3.80223 13.2642C3.60488 13.0668 3.49402 12.7992 3.49402 12.5201C3.49402 12.241 3.60488 11.9733 3.80223 11.776C3.99957 11.5786 4.26723 11.4678 4.54631 11.4678ZM10.8601 11.4678C11.1392 11.4678 11.4068 11.5786 11.6042 11.776C11.8015 11.9733 11.9124 12.241 11.9124 12.5201C11.9124 12.7992 11.8015 13.0668 11.6042 13.2642C11.4068 13.4615 11.1392 13.5724 10.8601 13.5724C10.581 13.5724 10.3133 13.4615 10.116 13.2642C9.91866 13.0668 9.80779 12.7992 9.80779 12.5201C9.80779 12.241 9.91866 11.9733 10.116 11.776C10.3133 11.5786 10.581 11.4678 10.8601 11.4678Z"
      fill="#1D1D5E"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.01525 0.470368C1.14109 0.409915 1.28579 0.401928 1.41752 0.448163L1.62938 0.522525L1.64258 0.527176C2.06901 0.677392 2.43207 0.805284 2.71815 0.945548C3.02753 1.09778 3.29271 1.28439 3.49194 1.57622C3.68977 1.86525 3.77115 2.18305 3.80903 2.53171C3.82615 2.7038 3.83621 2.87652 3.8392 3.04944H11.3028C12.4849 3.04944 13.5484 3.04944 13.8599 3.45422C14.1713 3.859 14.05 4.46863 13.8065 5.6893L13.4558 7.39051C13.2348 8.46245 13.124 8.99842 12.7367 9.31411C12.3502 9.6298 11.8023 9.6298 10.7072 9.6298H6.98767C5.96526 9.6298 5.21008 9.6298 4.63533 9.5384H4.22447V9.44768C3.9048 9.35275 3.65312 9.20832 3.44494 8.9886C2.83671 8.3474 2.79252 7.66691 2.79252 5.60301V3.77763C2.79252 3.25849 2.79111 2.91124 2.76305 2.64466C2.73499 2.39 2.68588 2.26232 2.62275 2.16972C2.56101 2.07992 2.46771 1.99504 2.25514 1.89051C2.02855 1.77897 1.72058 1.67023 1.25196 1.50537L1.06886 1.44153C0.937127 1.3953 0.829161 1.29862 0.768708 1.17278C0.708255 1.04695 0.700268 0.902245 0.746503 0.770516C0.792738 0.638788 0.889409 0.530821 1.01525 0.470368Z"
      fill="#1D1D5E"
    />
    <path
      d="M10.1506 4.02137C10.064 3.9732 9.96883 3.94257 9.87043 3.93123C9.77202 3.91988 9.67235 3.92805 9.5771 3.95526C9.48185 3.98247 9.39291 4.02818 9.31534 4.08979C9.23778 4.1514 9.17312 4.2277 9.12506 4.31432L7.72515 6.83364L6.92395 6.03245C6.85439 5.96043 6.77119 5.90298 6.67919 5.86347C6.58719 5.82395 6.48824 5.80314 6.38812 5.80227C6.28799 5.8014 6.1887 5.82048 6.09603 5.8584C6.00336 5.89631 5.91916 5.9523 5.84836 6.02311C5.77756 6.09391 5.72157 6.1781 5.68365 6.27077C5.64574 6.36344 5.62666 6.46274 5.62753 6.56286C5.6284 6.66298 5.6492 6.76193 5.68872 6.85393C5.72824 6.94593 5.78569 7.02914 5.85771 7.0987L7.36584 8.60682C7.50835 8.74972 7.70064 8.82814 7.89896 8.82814L8.0034 8.8206C8.11897 8.80443 8.22922 8.76166 8.32546 8.69564C8.4217 8.62963 8.50131 8.54218 8.55801 8.44018L10.4432 5.04689C10.4913 4.96034 10.5219 4.86516 10.5333 4.76679C10.5446 4.66841 10.5365 4.56876 10.5093 4.47353C10.4821 4.3783 10.4365 4.28935 10.3749 4.21177C10.3134 4.13419 10.2371 4.06949 10.1506 4.02137Z"
      fill="white"
    />
  </svg>
);

export const Agency = () => (
  <svg
    width="19"
    height="16"
    viewBox="0 0 19 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.871 14.5922C18.7843 14.8995 18.6837 15.1976 18.4613 15.4421C18.1762 15.755 17.8292 15.9681 17.4093 15.9811C16.5695 16.006 15.7288 15.9931 14.8881 15.9949C14.8715 15.9949 14.854 15.9848 14.8706 15.9903C14.8872 15.6498 14.9149 15.3212 14.9158 14.9927C14.9204 13.447 14.9167 11.9003 14.9186 10.3546C14.9195 9.80086 14.8983 9.25177 14.7128 8.72207C14.7054 8.70084 14.7118 8.67408 14.7118 8.62425C15.5987 8.67685 16.5058 8.48029 17.3475 8.91679C18.1743 9.3459 18.6763 10.015 18.8351 10.9369C18.8397 10.9655 18.8581 10.9913 18.8692 11.019C18.871 12.2094 18.871 13.4008 18.871 14.5922Z"
      fill="#0973BA"
    />
    <path
      d="M9.4392 15.9972C8.19892 15.9972 6.95955 15.9972 5.71927 15.9972C5.2394 15.9972 5.05022 15.8098 5.05114 15.3309C5.05206 13.4834 5.0456 11.6359 5.06037 9.78744C5.0696 8.58868 6.08563 7.44529 7.27147 7.26349C7.44035 7.23765 7.61292 7.22012 7.78364 7.2192C8.9501 7.21735 10.1166 7.21089 11.2821 7.22658C12.4652 7.24319 13.6104 8.26569 13.783 9.44045C13.8134 9.64532 13.8263 9.85573 13.8273 10.0624C13.831 11.8121 13.8291 13.5618 13.8291 15.3106C13.8291 15.8135 13.6464 15.9944 13.1416 15.9944C11.9069 15.9972 10.673 15.9972 9.4392 15.9972Z"
      fill="#0973BA"
    />
    <path
      d="M12.7158 3.22812C12.7195 5.05532 11.3002 6.54385 9.4416 6.5457C7.61439 6.54754 6.18954 5.10239 6.16463 3.28718C6.13879 1.49319 7.61624 0.0102039 9.42776 5.27847e-05C11.218 -0.0100984 12.7001 1.44521 12.7158 3.22812Z"
      fill="#0973BA"
    />
    <path
      d="M3.99981 15.9918C4.01457 15.9891 3.99058 15.9964 3.96659 15.9964C3.16834 15.9955 2.37009 16.0094 1.57276 15.9881C0.719144 15.9651 0.0297888 15.2822 0.0159464 14.4322C-0.00251027 13.3405 -0.0089701 12.247 0.0187148 11.1553C0.0491683 9.93252 1.13719 8.77252 2.35348 8.67101C2.94963 8.62118 3.55316 8.66271 4.16223 8.66271C4.16223 8.67378 4.17146 8.71162 4.1613 8.74115C3.99243 9.22286 3.96105 9.72304 3.96105 10.2278C3.96197 11.7376 3.95828 13.2473 3.9629 14.7571C3.9629 15.1659 3.98597 15.5738 3.99981 15.9918Z"
      fill="#0973BA"
    />
    <path
      d="M3.69446 3.04982C5.04548 3.05443 6.15749 4.16737 6.14181 5.49809C6.12612 6.85004 5.02795 7.95098 3.69261 7.95282C2.34712 7.95651 1.22219 6.82881 1.23327 5.48886C1.24526 4.13968 2.34989 3.04521 3.69446 3.04982Z"
      fill="#0973BA"
    />
    <path
      d="M15.1873 3.04981C16.5328 3.04704 17.6375 4.14337 17.6458 5.4907C17.6541 6.8325 16.5291 7.95835 15.1846 7.95374C13.8483 7.94912 12.7501 6.84634 12.7381 5.49716C12.7252 4.16367 13.8382 3.05258 15.1873 3.04981Z"
      fill="#0973BA"
    />
  </svg>
);
