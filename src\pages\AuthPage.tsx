import React from "react";
import LayoutLogoPage from "@/components/layout/LayoutLogoPage";
import { Router } from "@/constants/Route";
import { useNavigate } from "@/utils/component-util";
import { Box, Button, Container, Stack, Typography } from "@mui/material";
import { COLORS } from "@/constants/themes";

export default function AuthPage() {
  const navigate = useNavigate();

  const handleLogin = () => {
    navigate(Router.login);
  };
  const handleRegister = () => {
    navigate(Router.register);
  };
  return (
    <LayoutLogoPage>
      <img src="/images/login-banner.png" width={"100%"} alt="" />
      <Container>
        <Stack alignItems={"center"}>
          <Stack
            marginBlock={"15px"}
            gap={1}
            width={"100%"}
            alignItems={"center"}
          >
            <Button
              variant="contained"
              disableElevation
              style={{
                background: COLORS.primary,
                width: "100%",
                borderRadius: "10px",
              }}
              onClick={handleLogin}
            >
              Đ<PERSON>ng nhập
            </Button>
            <Typography color={"#555555"}>
              Chưa có tài kho<PERSON>n?{" "}
              <span
                style={{ color: "#1D1D5E", fontWeight: 700 }}
                onClick={handleRegister}
              >
                Đăng ký CTV
              </span>
            </Typography>
          </Stack>
          <Box marginBlock={"15px"} display={"flex"} justifyContent={"center"}>
            <img width={"80%"} src="/images/big-logo.png" alt="" />
          </Box>
        </Stack>
      </Container>
    </LayoutLogoPage>
  );
}
