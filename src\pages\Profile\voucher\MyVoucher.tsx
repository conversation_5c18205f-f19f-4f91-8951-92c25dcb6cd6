import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import {
  FormControl,
  InputAdornment,
  Stack,
  TextField,
  RadioGroup,
  Box,
  CircularProgress,
  Button,
} from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import FrameContainer from "../../../components/layout/Container";
import { useDispatch, useSelector } from "react-redux";
import { getMyVoucherList } from "../../../redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "../../../redux/store";
import { IVoucher } from "../../../types/voucher";
import { useNavigate } from "../../../utils/component-util";
import VoucherItem from "../../../components/discount/VourcherItem";
import { COLORS, commonStyle } from "../../../constants/themes";
import { useDebounce } from "use-debounce";
import { removeMark } from "../../../utils/common";
import { Router } from "../../../constants/Route";
import NoDataView from "../../../components/UI/NoDataView";

export default function MyVoucher() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [showClearIcon, setShowClearIcon] = useState("none");
  const { myVoucherList, isLoading } = useSelector(
    (state: RootState) => state.vouchers
  );
  console.log("🚀 ~ MyVoucher ~ myVoucherList:", myVoucherList);
  const [searchKey, setSearchKey] = useState("");
  const [debounceValue] = useDebounce(searchKey, 1000);

  useEffect(() => {
    dispatch(getMyVoucherList());
  }, []);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  const handleClick = (): void => {
    setSearchKey("");
  };

  const filteredVoucherList = useMemo(() => {
    return debounceValue && myVoucherList.length
      ? myVoucherList?.filter((voucher: IVoucher) => {
          const removeMarkVoucherCode = removeMark(voucher.code).toLowerCase();
          const removeMarkSearchKey = removeMark(debounceValue).toLowerCase();
          return removeMarkVoucherCode.includes(removeMarkSearchKey);
        })
      : myVoucherList || [];
  }, [debounceValue, myVoucherList]);

  return (
    <Box>
      <FrameContainer
        title="Ưu đãi của tôi"
        style={{ background: COLORS.neutral10 }}
      >
        <Stack sx={styles.contentContainer}>
          <Stack direction={"row"} gap={2} justifyContent="space-between">
            <FormControl style={{ flexGrow: 1 }}>
              <TextField
                placeholder="Nhập mã ưu đãi"
                size="small"
                variant="outlined"
                style={{ width: "100%" }}
                onChange={handleChange}
                value={searchKey}
                className="search-input"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      {<SearchIcon />}
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment
                      position="end"
                      style={{ display: showClearIcon }}
                      onClick={handleClick}
                    >
                      <ClearIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </FormControl>
            <Button
              style={styles.moreVoucher}
              onClick={() =>
                navigate(Router.voucher.index, {
                  state: { valueSearch: searchKey },
                })
              }
            >
              Tìm thêm Voucher
            </Button>
          </Stack>
          {isLoading ? (
            <Stack sx={styles.loadingContainer}>
              <CircularProgress />
            </Stack>
          ) : (
            <Stack className="voucher-profile" width={"100%"} marginBlock={2}>
              <RadioGroup
                row
                aria-labelledby="demo-form-control-label-placement"
                name="position"
                defaultValue="top"
              >
                {filteredVoucherList?.length > 0 ? (
                  filteredVoucherList?.map((item: IVoucher) => (
                    <Box sx={{ paddingBottom: 2, width: "100%" }} key={item.id}>
                      <VoucherItem
                        item={item}
                        isShowMatchPoint={!!item.matchPoint}
                        onNavigateToDetail={() => {
                          navigate(`${Router.myVoucher.index}/${item.id}`, {
                            state: { voucher: item },
                          });
                        }}
                        isMyVoucher={true}
                      />
                    </Box>
                  ))
                ) : (
                  <NoDataView content="Không có voucher" />
                )}
              </RadioGroup>
            </Stack>
          )}
        </Stack>
      </FrameContainer>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1,
    marginBottom: 4,
  },
  bottomBtn: {
    marginBottom: 72,
    width: "90%",
    padding: 12,
    borderRadius: 99,
    background: COLORS.accent1,
    color: "#fff",
    fontSize: 16,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
  moreVoucher: {
    ...commonStyle.headline14,
    backgroundColor: COLORS.primary,
    color: "#fff",
    borderRadius: 10,
    minWidth: 150,
  },
};
