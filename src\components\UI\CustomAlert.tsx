import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { CircularProgress, Stack, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useAlert } from "../../redux/slices/alert/useAlert";
import GradientText from "./GradientTextProps";
import { COLORS } from "@/constants/themes";

export default function CustomAlert() {
  const { alert } = useSelector((state: RootState) => state.alert);
  const { hideAlert } = useAlert();
  const theme = useTheme();

  /**
   * styles
   */
  const containerContentStyle: React.CSSProperties = {
    margin: 0,
    paddingInline: 12,
    paddingTop: 12,
    paddingBottom: 0,
  };
  const titleStyle: React.CSSProperties = {
    color: "theme.palette.primary.main",
    fontWeight: 700,
    marginBlock: 2,
  };
  const contentStyle: React.CSSProperties = {
    marginBlock: 2,
  };
  const containerButtonStyle: React.CSSProperties = {
    margin: 0,
    paddingBottom: 16,
    minWidth: 300,
  };
  const defaultFirstBtnSyle: React.CSSProperties = {
    background: "#E8E8EA",
    color: "#666666",
    boxShadow: "none",
    width: "50%",
    margin: 8,
  };
  const defaultSecondBtnSyle: React.CSSProperties = {
    background: COLORS.primary,
    color: "white",
    boxShadow: "none",
    width: "50%",
    margin: 8,
  };

  //-----------------------

  return (
    <Dialog
      open={!!alert?.isShow || false}
      onClose={hideAlert}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      PaperProps={{ sx: { borderRadius: 4 } }}
    >
      <DialogContent style={containerContentStyle}>
        <Stack marginBlock={2} textAlign="center" width={"100%"} gap={2}>
          {alert?.icon && (
            <Stack direction={"row"} justifyContent="center" width={"100%"}>
              <img
                src={alert?.icon}
                style={alert.iconStyle && alert.iconStyle}
              />
            </Stack>
          )}
          <Stack>
            {alert?.title && (
              <GradientText
                text={alert?.title}
                style={alert?.titleStyle || titleStyle}
              />
            )}
            {alert?.content && (
              <GradientText
                text={alert?.content}
                style={alert?.contentStyle || contentStyle}
              />
            )}
          </Stack>
        </Stack>
      </DialogContent>
      <DialogActions style={containerButtonStyle}>
        {alert !== null && alert.buttons && alert.buttons.length > 0 ? (
          <Stack
            direction={alert.buttons.length < 3 ? "row" : "column"}
            justifyContent="center"
            alignItems="center"
            width={"100%"}
          >
            {alert.buttons.map((e, index) => {
              return (
                <Button
                  key={`btn-${index}`}
                  style={
                    e.customButtonStyle
                      ? e.customButtonStyle
                      : index % 2 === 0 && alert.buttons!.length > 1
                      ? defaultFirstBtnSyle
                      : defaultSecondBtnSyle
                  }
                  onClick={() => {
                    //--settimeout to avoid repeatedly calling the showAlert causing errors
                    setTimeout(() => {
                      if (e.action) {
                        e.action();
                      }
                    }, 200);
                    hideAlert();
                  }}
                  variant="contained"
                >
                  {alert?.loading && (
                    <CircularProgress
                      size={18}
                      sx={{ color: "#fff", marginRight: 1 }}
                    />
                  )}
                  {e.title}
                </Button>
              );
            })}
          </Stack>
        ) : (
          <Stack
            direction={"row"}
            justifyContent="center"
            alignItems="center"
            width={"100%"}
          >
            <Button
              style={defaultSecondBtnSyle}
              onClick={hideAlert}
              variant="contained"
            >
              {alert?.loading && (
                <CircularProgress
                  size={18}
                  sx={{ color: "#fff", marginRight: 1 }}
                />
              )}
              OK
            </Button>
          </Stack>
        )}
      </DialogActions>
    </Dialog>
  );
}
