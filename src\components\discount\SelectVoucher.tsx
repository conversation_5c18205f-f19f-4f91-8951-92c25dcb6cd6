import {
  <PERSON>ton,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  FormControl,
  IconButton,
  RadioGroup,
  Slide,
  Stack,
  InputAdornment,
  TextField,
  Container,
  Typography,
  Box,
  Divider,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import React, { useEffect, useMemo } from "react";
import { useState } from "react";
import { Icon } from "../../constants/Assets";
import { TransitionProps } from "@mui/material/transitions";
import ClearIcon from "@mui/icons-material/Clear";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { getMyVoucherList } from "../../redux/slices/voucher/voucherSlice";
import { IVoucher } from "../../types/voucher";
import { SelectedVoucher } from "./checkVoucherType";
import { useNavigate } from "../../utils/component-util";
import { COLORS, commonStyle } from "../../constants/themes";
import VoucherItem from "./VourcherItem";
import { useDebounce } from "use-debounce";
import { removeMark } from "../../utils/common";
import NoDataView from "../UI/NoDataView";
import { APP_NAME } from "../../constants/AppInfo";
import { Router } from "../../constants/Route";
import GradientText from "../UI/GradientTextProps";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function SelectVoucher({ onApplyVoucher, initVoucher }) {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [showClearIcon, setShowClearIcon] = useState("none");
  const [open, setOpen] = useState(false);
  const [tempVoucher, setTempVoucher] = useState<IVoucher | null>();
  const [selectedVoucher, setSelectedVoucher] = useState<IVoucher | null>();
  const [searchKey, setSearchKey] = useState("");
  const [debounceValue] = useDebounce(searchKey, 1000);

  const { myVoucherList } = useSelector((state: RootState) => state.vouchers);

  useEffect(() => {
    dispatch(getMyVoucherList());
  }, []);

  useEffect(() => {
    setSelectedVoucher(initVoucher);
    setTempVoucher(initVoucher);
  }, [initVoucher]);

  // Handle open voucher list
  const handleClickOpen = () => {
    setOpen(true);
    dispatch(getMyVoucherList());
  };
  const handleClose = () => {
    setOpen(false);
  };

  // Handle select voucher
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  // Handle search voucher
  const handleClick = (): void => {
    setSearchKey("");
  };

  // handle apply voucher
  const applyVoucher = () => {
    setSelectedVoucher(tempVoucher);
    onApplyVoucher(tempVoucher);
    handleClose();
  };

  const filteredVoucherList = useMemo(() => {
    return debounceValue && myVoucherList.length
      ? myVoucherList?.filter((voucher: IVoucher) => {
          const removeMarkVoucherCode = removeMark(voucher.code).toLowerCase();
          const removeMarkSearchKey = removeMark(debounceValue).toLowerCase();
          return removeMarkVoucherCode.includes(removeMarkSearchKey);
        })
      : myVoucherList || [];
  }, [debounceValue, myVoucherList]);

  return (
    <Box>
      {selectedVoucher ? (
        <Stack
          direction="row"
          justifyContent={"space-between"}
          onClick={handleClickOpen}
        >
          <Stack direction="row" gap={3}>
            <img src={Icon.voucher} />
            <Stack>
              <span style={{ fontWeight: 700 }}>
                {SelectedVoucher(selectedVoucher)}
              </span>
              <span
                style={{
                  color: "#969595",
                  WebkitLineClamp: 1,
                  WebkitBoxOrient: "vertical",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  textAlign: "start",
                  display: "-webkit-box",
                }}
              >
                Voucher của {APP_NAME}
              </span>
            </Stack>
          </Stack>
          <IconButton>
            <KeyboardArrowRightIcon />
          </IconButton>
        </Stack>
      ) : (
        <Stack
          direction="row"
          justifyContent={"space-between"}
          onClick={handleClickOpen}
        >
          <Stack direction="row" gap={3}>
            <img src={Icon.voucher} />
            <Stack>
              <span style={{ fontWeight: 700 }}>
                Chưa áp dụng (chọn hoặc nhập mã)
              </span>
              <span
                style={{
                  color: "#969595",
                  WebkitLineClamp: 1,
                  WebkitBoxOrient: "vertical",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  textAlign: "start",
                  display: "-webkit-box",
                }}
              >
                Voucher của {APP_NAME}
              </span>
            </Stack>
          </Stack>
          <IconButton>
            <KeyboardArrowRightIcon />
          </IconButton>
        </Stack>
      )}

      <Dialog
        className="popup-voucher"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        scroll={"paper"}
      >
        <DialogActions sx={{ justifyContent: "center" }}>
          <Container>
            <Stack width={"100%"}>
              <Stack direction="row" sx={styles.topContainer}>
                {/* <img src="/images/voucher.png" /> */}
                <Stack sx={{ alignItems: "center" }}>
                  <GradientText
                    style={{
                      ...commonStyle.headline16,
                    }}
                    text={APP_NAME}
                  />
                  <GradientText
                    style={{
                      fontSize: 16,
                    }}
                    text="Ưu đãi của bạn"
                  />
                </Stack>
              </Stack>
              <Stack direction={"row"} gap={2} justifyContent="space-between">
                <FormControl style={{ flexGrow: 1 }}>
                  <TextField
                    placeholder="Nhập mã ưu đãi"
                    size="small"
                    variant="outlined"
                    style={{ width: "100%" }}
                    onChange={handleChange}
                    value={searchKey}
                    className="search-input"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {<SearchIcon />}
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment
                          position="end"
                          style={{ display: showClearIcon }}
                          onClick={handleClick}
                        >
                          <ClearIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </FormControl>
                <Button
                  style={styles.moreVoucher}
                  onClick={() =>
                    navigate(Router.voucher.index, {
                      state: { valueSearch: searchKey },
                    })
                  }
                >
                  Tìm thêm Voucher
                </Button>
              </Stack>
            </Stack>
          </Container>
        </DialogActions>
        <DialogContent style={{ width: "100%" }}>
          <Stack className="voucher-profile" width={"100%"}>
            <RadioGroup
              row
              aria-labelledby="demo-form-control-label-placement"
              name="position"
              defaultValue="top"
            >
              {filteredVoucherList?.length > 0 ? (
                filteredVoucherList?.map((item: IVoucher) => (
                  <Box sx={{ paddingBottom: 2, width: "100%" }} key={item.id}>
                    <VoucherItem
                      item={item}
                      onSelectVoucher={() => setTempVoucher(item)}
                      isChecked={item.id === tempVoucher?.id}
                      isChooseVoucherCart={true}
                    />
                  </Box>
                ))
              ) : (
                <NoDataView content="Không có voucher" />
              )}
            </RadioGroup>
          </Stack>
        </DialogContent>
        <DialogActions sx={styles.bottomBtnContainer}>
          <Container>
            <Stack width="100%">
              <Divider />
              <Stack sx={styles.bottomBtnContainer} direction="column">
                {filteredVoucherList.length > 0 && (
                  <Box textAlign={"center"}>
                    <Typography>
                      {selectedVoucher
                        ? "Một voucher đã được chọn"
                        : "Chỉ chọn một voucher"}
                    </Typography>
                    {tempVoucher && (
                      <Typography
                        style={{
                          ...commonStyle.headline14,
                        }}
                      >
                        Áp dụng mã ưu đãi
                      </Typography>
                    )}
                  </Box>
                )}
                {tempVoucher ? (
                  <Button style={styles.bottomBtn} onClick={applyVoucher}>
                    Áp dụng
                  </Button>
                ) : (
                  <Button
                    style={{
                      ...styles.bottomBtn,
                      background: COLORS.accent4,
                      color: COLORS.accent1,
                    }}
                    onClick={handleClose}
                  >
                    Đóng
                  </Button>
                )}
              </Stack>
            </Stack>
          </Container>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentTitle: {
    fontWeight: 700,
    color: COLORS.neutral2,
  },
  contentText: {
    color: COLORS.neutral5,
    paddingTop: 4,
  },
  topContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginBlock: 1,
    gap: 2,
    width: "100%",
  },
  inputContainer: {
    gap: 2,
    justifyContent: "space-between",
    marginBlock: 2,
    width: "100%",
  },
  applyBtn: {
    color: "white",
    whiteSpace: "nowrap",
    background: COLORS.accent1,
    paddingInline: 16,
  },
  bottomBtnContainer: {
    justifyContent: "space-around",
    alignItems: "center",
    gap: 2,
    marginBlock: 1,
  },
  bottomBtn: {
    margin: 0,
    height: "100%",
    width: "100%",
    paddingBlock: 10,
    borderRadius: 99,
    display: "flex",
    gap: "8px",
    background: COLORS.primary,
    color: "#fff",
  },
  moreVoucher: {
    ...commonStyle.headline14,
    background: COLORS.primary,
    color: "#fff",
    borderRadius: 10,
    minWidth: 150,
  },
};
