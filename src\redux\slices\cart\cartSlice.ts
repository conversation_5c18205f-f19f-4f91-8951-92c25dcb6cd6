import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ICart } from "../../../types/cart";

interface CartState {
  cart: ICart | null;
}

const initialState: CartState = {
  cart: null,
};

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    setCart: (state, action: PayloadAction<ICart | null>) => {
      state.cart = action?.payload;
    },
  },
});

export const { setCart } = cartSlice.actions;

export default cartSlice.reducer;
