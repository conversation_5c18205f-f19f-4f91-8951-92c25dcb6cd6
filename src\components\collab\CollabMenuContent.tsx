import { Icon } from "@/constants/Assets";
import { RootState } from "@/redux/store";
import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import InfoUser from "../InfoUser";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import React, { useMemo } from "react";
import { Level } from "@/constants/Const";
import CollabRegisterInform from "./CollabRegisterInform";
import { BannerType } from "@/types/news";
import Banner from "../banner/Banner";
import ReportCard from "@/pages/Collab/components/ReportCard";
import { COLORS, commonStyle } from "@/constants/themes";
import { formatPrice } from "@/utils/formatPrice";
import { useShareReferLink } from "@/hooks/useShareReferLink";
import CollabMenuTool from "./CollabMenuTool";

export default function CollabMenuContent() {
  const navigate = useNavigate();
  const user = useSelector((state: RootState) => state.auth.user);
  const { teamReport } = useSelector((state: RootState) => state.team);
  const { shareLink } = useShareReferLink();
  // const TopItems = [
  //   {
  //     icon: Icon.icon_tdh,
  //     title: "Tạo đơn hàng",
  //     link: Router.product,
  //   },
  //   {
  //     icon: Icon.icon_hdsd,
  //     title: "Hướng dẫn sử dụng",
  //     link: "instruction",
  //   },
  //   {
  //     icon: Icon.icon_qldh,
  //     title: "Quản lý đơn hàng",
  //     link: "/order",
  //   },
  //   {
  //     icon: Icon.icon_csdl,
  //     title: "Chính sách đại lý",
  //     link: "/profile/policy",
  //   },
  // ];
  const redirectLink = (link) => {
    navigate(link);
  };

  const upgradeAccountBtn = useMemo(() => {
    if (user?.level !== Level.Member) {
      return;
    }

    let btnText = "Nâng cấp gói đại sứ affiliate";
    let path = [Router.refer.index, Router.refer.upgrade].join("/");

    if (!user?.fParent && !user?.updatedReferCode) {
      btnText = "Đăng ký trở thành nhà bán hàng";
      path = Router.refer.index;
    }

    return (
      <Stack style={{ padding: "15px 0", textAlign: "center" }}>
        <Box>
          <Button
            onClick={() => navigate(path)}
            style={{ background: COLORS.primary, color: COLORS.white }}
          >
            {btnText}
          </Button>
          <Typography style={{ marginTop: 5, fontSize: 14 }}>
            Để hưởng những quyền lợi của đại lý
          </Typography>
        </Box>
      </Stack>
    );
  }, [user]);

  return (
    <>
      <Banner type={BannerType.collab_banner} />
      {upgradeAccountBtn}
      <Box
        style={{
          background: "#fff",
          padding: "15px",
          borderRadius: "5px",
        }}
      >
        <InfoUser />
        <Stack
          style={{
            ...commonStyle.shadowBorder,
            ...styles.container,
            marginTop: 20,
          }}
        >
          <Grid style={{ width: "100%", padding: 12 }}>
            <Box sx={{ ...styles.container }} justifyContent="space-between">
              <Typography
                style={{ ...styles.text, fontSize: 15, fontWeight: 700 }}
              >
                Số dư ví
              </Typography>
              {/* <Link
                style={{ ...styles.link }}
                to={Router.collabhistory.withdrawal.index}
              >
                <span>
                  Lịch sử{" "}
                  <img src={Icon.black_arrow} style={styles.iconStyle} />
                </span>
              </Link> */}
            </Box>
            <Box
              sx={{
                ...styles.container,
                marginTop: "16px",
                padding: "0px 20px",
              }}
              justifyContent="space-between"
            >
              <Typography
                style={{ ...styles.textBalance }}
                onClick={() => navigate(Router.point)}
              >
                {formatPrice(user?.balance ?? 0)}
                {/* <img src={Icon.black_arrow} style={styles.iconStyle} /> */}
              </Typography>
              <Button
                style={{
                  color: "#fff",
                  borderRadius: 99,
                  fontSize: 15,
                  fontWeight: 700,
                  background: COLORS.primary,
                }}
                variant="contained"
                onClick={() => {
                  navigate(Router.collab.withdrawal);
                }}
              >
                Lịch sử
              </Button>
            </Box>
            <Box
              sx={{ ...styles.container, marginTop: "16px" }}
              justifyContent="space-between"
            >
              <Typography style={{ ...styles.text, fontSize: 12 }}>
                Bạn đã có{" "}
                {formatPrice(user?.commission?.pendingCommission || 0)} đang chờ
                duyệt, chia sẻ để kiếm thêm!!
              </Typography>
              <Box onClick={shareLink}>
                <img src={Icon.share_icon} />
              </Box>
            </Box>
          </Grid>
        </Stack>

        <ReportCard
          items={[
            {
              icon: Icon.icon_hdsd,
              title: "Doanh số đội nhóm",
              value: user?.myTeamSale,
              isMoney: true,
            },
            {
              icon: Icon.icon_hdsd,
              title: "Doanh số cá nhân",
              value: user?.mySale,
              isMoney: true,
            },
            {
              icon: Icon.icon_hdsd,
              title: "Hoa hồng chờ duyệt",
              value: user?.commission?.pendingCommission || 0,
              isMoney: true,
            },
            {
              icon: Icon.icon_hdsd,
              title: "Hoa hồng đã duyệt",
              value: user?.commission?.totalCommission,
              isMoney: true,
            },
            {
              icon: Icon.icon_hdsd,
              title: "Đơn hàng thành công",
              value: teamReport?.totalOrderSuccess,
              isMoney: false,
            },
            {
              icon: Icon.icon_hdsd,
              title: "Số thành viên nhóm",
              value: teamReport?.totalMembers,
              isMoney: false,
            },
          ]}
        />
        <CollabMenuTool />

        <Stack
          style={{
            ...commonStyle.shadowBorder,
            ...styles.container,
            marginTop: 16,
            borderRadius: "20px",
          }}
        >
          <Grid style={{ width: "100%" }}>
            <Box
              onClick={shareLink}
              sx={{ ...styles.container }}
              justifyContent="center"
            >
              <Typography
                style={{ ...styles.text, fontSize: 15, fontWeight: 700 }}
              >
                Chia sẻ ngay để nhận tới 400.000đ
              </Typography>
              <Box>
                <img src={Icon.mdi_share} />
              </Box>
            </Box>
          </Grid>
        </Stack>
        {/* <ToolsView /> */}
        <Stack style={{ gap: 10 }}>
          <Grid style={{ width: "100%", padding: 12 }}>
            <Typography
              style={{
                ...styles.text,
                ...styles.label,
                textAlign: "center",
              }}
            >
              3 Bước để nhận hoa hồng dễ dàng
            </Typography>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                marginTop: 1,
              }}
            >
              <Stack style={{ ...styles.commissionStepGroup }}>
                <img
                  src={Icon.fluent_share}
                  style={{ ...styles.commissionStepIcon }}
                />
                <Typography
                  style={{
                    ...styles.text,
                  }}
                >
                  Chia sẻ link
                </Typography>
              </Stack>
              <img src={Icon.si_play_rewind_line} />
              <Stack style={{ ...styles.commissionStepGroup }}>
                <img
                  src={Icon.hugeicons_add_team}
                  style={{ ...styles.commissionStepIcon }}
                />
                <Typography
                  style={{
                    ...styles.text,
                  }}
                >
                  Nhận giới thiệu
                </Typography>
              </Stack>
              <img src={Icon.si_play_rewind_line} />
              <Stack style={{ ...styles.commissionStepGroup }}>
                <img
                  src={Icon.grommet_icons_money}
                  style={{ ...styles.commissionStepIcon }}
                />
                <Typography
                  style={{
                    ...styles.text,
                  }}
                >
                  Nhận hoa hồng
                </Typography>
              </Stack>
            </Box>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Button
                style={{
                  color: "#fff",
                  borderRadius: 99,
                  fontSize: 15,
                  fontWeight: 700,
                  background: COLORS.primary,
                  marginTop: 8,
                  width: 211,
                }}
                variant="contained"
                onClick={shareLink}
              >
                Chia sẻ ngay
              </Button>
            </Box>
          </Grid>
        </Stack>
      </Box>
    </>
  );
}
const styles: Record<string, React.CSSProperties> = {
  title: {
    fontWeight: 700,
    color: COLORS.blue,
  },
  headerContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  seeMoreContainer: {
    display: "flex",
    alignItems: "center",
    color: COLORS.blue,
  },
  activeAccount: {
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  },
  activeAccountBtn: {
    background: COLORS.accent1,
    color: "#FFF",
  },
  container: {
    display: "flex",
    gap: 1,
    alignItems: "center",
    paddingBlock: 1,
  },
  link: {
    textDecoration: "unset",
    color: COLORS.neutral1,
    fontSize: 15,
    fontWeight: 400,
  },
  textBalance: {
    fontWeight: 700,
    fontSize: 24,
    color: COLORS.neutral1,
  },
  iconStyle: {
    width: "14px",
    height: "14px",
    objectFit: "fill",
  },
  shareIcon: {
    height: "26px",
    width: "28px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#005DE0",
    borderRadius: "100%",
  },
  text: {
    color: COLORS.neutral1,
    fontWeight: 700,
    overflow: "hidden",
    textOverflow: "ellipsis",
    textAlign: "center",
    fontSize: 14,
  },
  commissionStepIcon: {
    width: "53px",
    height: "53px",
  },
  commissionStepGroup: {
    alignItems: "center",
  },
  commissionStepLabel: {
    fontSize: 15,
    fontWeight: 700,
  },
};
