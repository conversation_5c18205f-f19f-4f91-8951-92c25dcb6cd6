import React, { useEffect } from "react";
import { Box, Grid, useTheme } from "@mui/material";
import { useSelector } from "react-redux";
import UserCard from "@/components/UserCard";
import { RootState } from "@/redux/store";
import { Icon } from "@/constants/Assets";
import { Level, LevelIcon, LevelName } from "@/constants/Const";
import { COLORS } from "@/constants/themes";
import GradientText from "@/components/UI/GradientTextProps";

const RankIcon = {
  // [Level.Membership]: Icon.rank_dtkd,
  // [Level.SilverMember]: Icon.rank_daisu,
  // [Level.GoldenMember]: Icon.rank_doanhchu,
};

export default function ProfileHeader() {
  const { user, userZalo } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();

  if (!user) return null;
  return (
    <Grid container>
      <Grid item xs={7}>
        <UserCard user={user} userZalo={userZalo} />
      </Grid>
      <Grid item xs={5}>
        <Box sx={{ display: "flex", gap: 1 }} alignItems="center">
          <img
            src='/images/shield-check.png'
            alt=""
            width={41}
            height={41}
            style={{ aspectRatio: "1/1" }}
          />
          <Box style={{ width: "100%", fontSize: 12, fontWeight: 700 }}>
            <GradientText
              style={{
                fontSize: 12,
                fontWeight: 500,
              }}
              text={
                user.level !== Level.Member
                  ? "Hạng thành viên!"
                  : "Chưa đăng ký gói đại lý"
              }
            />
            <p style={{ margin: 0, color: COLORS.primary }}>
              <span style={{ color: COLORS.primary }}>
                {user?.level !== undefined && LevelName[user.level]}
              </span>
            </p>
          </Box>
        </Box>
      </Grid>

    </Grid>
  );
}
