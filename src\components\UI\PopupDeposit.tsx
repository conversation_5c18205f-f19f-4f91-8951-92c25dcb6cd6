import React, { useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Box, InputLabel, Slide, TextField, Typography } from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import PopupQrTransfer from "./PopupQrTransfer";
import { COLORS } from "@/constants/themes";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const PopupDeposit = ({
  open,
  setOpen,
  setDepositValue,
  handleContinue,
  finalPrice,
}: {
  open: boolean;
  setOpen: (status: boolean) => void;
  setDepositValue: (value: number) => void;
  handleContinue: () => void;
  finalPrice: number;
}) => {
  const [error, setError] = useState(false);
  const [amount, setAmount] = useState(0);
  // const [isOpenQrCode, setisOpenQrCode] = useState(false);

  return (
    <Dialog
      open={open}
      onClose={() => {
        // setisOpenQrCode(false);
        setOpen(false);
      }}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      TransitionComponent={Transition}
    >
      {/* {isOpenQrCode ? (
        <PopupQrTransfer
          amount={amount}
          handleContinue={() => {
            handleContinue();
          }}
        />
      ) : ( */}
      <Box sx={{ marginBottom: "10px" }}>
        <DialogContent>
          <InputLabel
            sx={{
              fontweight: "bold",
            }}
          >
            Nhập số tiền đặt cọc
          </InputLabel>
          <TextField
            placeholder="Tối thiểu 50.000đ"
            sx={{
              width: "100%",
              marginTop: "10px",
            }}
            size="small"
            variant="outlined"
            onChange={(e) => {
              setAmount(Number(e.target.value));
              setError(
                !(Number(e.target.value) >= 50000) ||
                  Number(e.target.value) > finalPrice
              );
            }}
          />

          {error && (
            <Typography fontSize={12} color={"red"}>
              {amount < 50000 && "Số tiền tối thiểu là 50.000đ"}
              {amount > finalPrice && "Số đặt cọc lớn hơn số tiền của sản phâm"}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              if (!error && amount >= 50000) {
                // setisOpenQrCode(true);
                setDepositValue(amount);
                handleContinue();
              }
            }}
            type="submit"
            style={{
              background: COLORS.primary,
              color: "#fff",
              paddingInline: 18,
              fontSize: 12,
            }}
          >
            Tiếp tục
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default PopupDeposit;
