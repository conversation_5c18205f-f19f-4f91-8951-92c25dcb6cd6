import { Container, Stack, Box } from "@mui/material";
import React, { useEffect } from "react";
import FrameContainerFull from "../../components/layout/ContainerFluid";
import dayjs from "dayjs";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import store, { RootState } from "../../redux/store";
import ListNews from "../../components/news/ListNews";
import { getNewsRecommend } from "../../redux/slices/news/newsListSlice";

export default function PostDetail() {
  const { newsList, banner, recommend } = useSelector(
    (state: RootState) => state.news
  );
  const { id } = useParams();
  const newsId: number = parseInt(id || "");
  const item = [...newsList, ...banner].find((n) => n.id === newsId);

  useEffect(() => {
    if (item) {
      store.dispatch(getNewsRecommend(item.id));
    }
  }, [item]);

  if (!item) return null;

  return (
    <FrameContainerFull title="Tin tức">
      <Stack>
        <img
          style={{ aspectRatio: 1.75 / 1, objectFit: "cover" }}
          width="100%"
          src={`${import.meta.env.VITE_API_URL}${
            item?.image?.data?.attributes?.url
          }`}
          alt={`${item?.title}`}
        />
      </Stack>
      <Stack paddingBlock={2} style={{ background: "#fff" }} mb={2}>
        <Container>
          <p style={{ fontSize: 18, fontWeight: 700, margin: 0 }}>
            {item?.title}
          </p>
          <span> {dayjs(item?.createdAt).format("HH:mm, DD/MM")}</span>
        </Container>
      </Stack>

      <Stack paddingBlock={2} style={{ background: "#fff" }}>
        <Container>
          <p style={{ fontSize: 18, fontWeight: 700, margin: 0 }}>
            {item?.title}
          </p>
          <Box
            dangerouslySetInnerHTML={{
              __html: item?.content,
            }}
          />
        </Container>
      </Stack>

      <Stack marginBlock={2} style={{ background: "#fff" }}>
        <Container style={{ background: "#fff" }}>
          <Stack marginTop={2} marginBottom={1}>
            <p style={{ margin: 0, fontWeight: 700, fontSize: 18 }}>
              Tin tức liên quan
            </p>
          </Stack>
          <ListNews list={recommend} />
        </Container>
      </Stack>
    </FrameContainerFull>
  );
}
