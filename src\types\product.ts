export interface IProductCategory {
  id: number;
  name: string;
  image: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
}

export interface IProductImage {
  name: string;
  alternativeText: any;
  caption: any;
  width: any;
  height: any;
  formats: any;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: any;
  provider: string;
  provider_metadata: any;
  createdAt: string;
  updatedAt: string;
}

export interface IProduct {
  id: number;
  name: string;
  price: number;
  commission: number;
  bonusPoint: number;
  discount: number;
  description: string;
  quantity: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  isFixedCommission: boolean;
  sold: number;
  bv: number;
  image: IProductImage[];
  product_cats: {
    data: [
      {
        id: number;
        attributes: {
          name: string;
          createdAt: string;
          updatedAt: string;
          publishedAt: string;
        };
      }
    ];
  };
  supplier: {
    data: {
      id: number;
      attributes: {
        name: string;
        createdAt: string;
        updatedAt: string;
        publishedAt: string;
      };
    };
  };

  product_tags: {
    data: [
      {
        id: number;
        attributes: {
          name: string;
          createdAt: string;
          updatedAt: string;
          publishedAt: string;
        };
      }
    ];
  };
}

export interface IPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface IProductSearchParam {
  categoryId: number | null;
  searchText: string | null;
  page?: number;
}

export interface IProductComment {
  id: number;
  rate: number;
  content: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  userName: string;
  userId: number;
}
export interface IProductGift {
  product: IProduct;
  quantity: number;
}

export interface IPromotion {
  id: number;
  startDate: string;
  endDate: string;
  description: string;
  title: string;
  productGift: IProductGift[];
}
