import React, { useEffect, useState } from "react";
import { Stack, Tab, Tabs, useTheme, Box, Container } from "@mui/material";
import { useSelector } from "react-redux";
import store, { RootState } from "../redux/store";
import { getOrderByOrderStatus } from "../redux/slices/order/orderSlice";
import { OrderStatusText, OrderStatus } from "../constants/Const";
import { IOrder } from "../types/order";
import { useNavigate } from "../utils/component-util";
import OrderItem from "./OrderItem";
import FrameContainerFull from "@/components/layout/ContainerFluid";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number | null;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

export default function Order() {
  const theme = useTheme();

  const category = Object.values(OrderStatus).map((status) => ({
    label: OrderStatusText[status],
    id: status,
  }));

  const navigator = useNavigate();

  const [tabIndex, setTabIndex] = useState<number | null>(0);
  const handleChange = (event: React.SyntheticEvent, orderId: number) => {
    setTabIndex(orderId);
  };
  const orderList: IOrder[] = useSelector(
    (state: RootState) => state.order.orderList
  );

  useEffect(() => {
    if (tabIndex !== null) {
      store.dispatch(getOrderByOrderStatus(category[tabIndex].id));
    }
  }, [tabIndex]);

  return (
    <FrameContainerFull title="Danh sách đơn hàng">
      <Tabs
        className="order-history"
        value={tabIndex}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons="auto"
        style={{
          background: "#fff",
          marginBottom: "20px",
        }}
      >
        {category.map((item, index) => (
          <Tab
            key={index}
            style={{ fontSize: 14, fontWeight: 400 }}
            label={item.label}
          />
        ))}
      </Tabs>
      <Container>
        {category.map((_, index) => (
          <CustomTabPanel key={index} value={tabIndex} index={index}>
            <Stack gap={1}>
              {orderList?.length > 0 ? (
                orderList.map((order) => (
                  <OrderItem order={order} key={String(order.id)} />
                ))
              ) : (
                <Stack direction="row" justifyContent={"center"} padding={2}>
                  Không có đơn hàng nào
                </Stack>
              )}
            </Stack>
          </CustomTabPanel>
        ))}
      </Container>
    </FrameContainerFull>
  );
}
