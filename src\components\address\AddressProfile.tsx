import {
  Box,
  Divider,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect } from "react";
import PopupAddAddress from "../UI/PopupAddAddress";
import store, { AppDispatch, RootState } from "../../redux/store";
import { getAddressList } from "../../redux/slices/address/addressSlice";
import { useDispatch, useSelector } from "react-redux";
import { updateUser } from "../../redux/slices/authen/authSlice";
import { useTheme } from "@mui/material/styles";
import { useLocation } from "react-router-dom";
import { useNavigate } from "../../utils/component-util";

export default function AddressProfile() {
  const { list } = useSelector((state: RootState) => state.address);
  const { user } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (!Array.isArray(list) || !list.length) {
      store.dispatch(getAddressList());
    }
  }, []);

  const renderAddress = (item) => (
    <Stack flex={1}>
      <Typography
        style={{
          fontSize: 14,
          fontWeight: 700,
          color: theme.palette.primary.main,
        }}
      >
        {item.userName} - {item.phone}
      </Typography>
      <Typography fontSize={12}>
        {item.address} - {item?.addressObj?.wardText} -{" "}
        {item?.addressObj?.districtText} -{item?.addressObj?.provinceText}
      </Typography>
    </Stack>
  );

  return (
    <Stack gap={2}>
      {Array.isArray(list) && list.length > 0 ? (
        <>
          <Box
            style={{
              marginBlock: 12,
              backgroundColor: "white",
              borderRadius: "10px",
              marginTop: "20px",
            }}
            p={2}
          >
            <RadioGroup
              row
              aria-labelledby="demo-form-control-label-placement"
              name="position"
              defaultValue="top"
            >
              {list?.map((item, index) => (
                <Stack p={1} gap={1} key={Number(index)} width={"100%"}>
                  <FormControlLabel
                    value={item.id.toString()}
                    control={<Radio />}
                    label={renderAddress(item)}
                    checked={user?.addressId === item.id}
                    labelPlacement="start"
                    sx={{ justifyContent: "space-between", ml: 0 }}
                    onChange={async (e: any) => {
                      if (user) {
                        await dispatch(
                          updateUser({
                            id: user.id,
                            addressId: e.target.value,
                          })
                        );
                        location.state?.shouldGoBack && navigate(-1);
                      }
                    }}
                  />
                  {index !== list.length - 1 && <Divider />}
                </Stack>
              ))}
            </RadioGroup>
          </Box>
        </>
      ) : null}
      <PopupAddAddress />
    </Stack>
  );
}
