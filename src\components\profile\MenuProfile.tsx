import { Box, Divider, <PERSON><PERSON><PERSON><PERSON>on, Stack, Typography } from "@mui/material";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import React from "react";
import { useNavigate } from "../../utils/component-util";
import { Icon } from "@/constants/Assets";
import { Router } from "@/constants/Route";
import { openChat } from "@/utils/openChat";
import PopupAddIconScreenApp from "../UI/PopupAddIconScreenApp";
export default function MenuProfile() {
  const navigate = useNavigate();
  const menu1 = [
    {
      icon: Icon.paymentIcon,
      title: "Thông tin thanh toán",
      subTitle: "Tài khoản nhận tiền hoa hồng",
      onClick: () => {
        navigate(Router.profile.payment);
      },
    },
    // {
    //   icon: Icon.policy,
    //   title: "Chính sách cộng tác viên",
    //   subTitle: "<PERSON><PERSON><PERSON> sách bán hàng CTV",
    //   onClick: () => {
    //     navigate(Router.profile.policy.index);
    //   },
    // },
    // {
    //   icon: Icon.collab,
    //   title: "Đăng ký trở thành cộng tác viên",
    //   subTitle: "Kinh doanh cùng STBE GROUP",
    //   onClick: () => {
    //     navigate(Router.collab.index + "/" + Router.collab.collabRegister);
    //   },
    // },
    // {
    //   icon: Icon.collab,
    //   title: "Kho Voucher",
    //   subTitle: "Các voucher khuyến mại",
    //   onClick: () => {
    //     navigate(Router.myVoucher.index);
    //   },
    // },
  ];
  const menu2 = [
    {
      icon: Icon.setting,
      title: "Thông tin tài khoản",
      subTitle: "Cập nhật thông tin định danh",
      onClick: () => {
        navigate(Router.profile.info);
      },
    },
    {
      icon: Icon.QA,
      title: "Điều khoản và hỏi đáp",
      subTitle: "Giải đáp các thắc mắc cơ bản",
      onClick: () => {
        navigate(Router.profile.term.index);
      },
    },
    {
      icon: Icon.instruction,
      title: "Hỗ trợ và hỏi đáp",
      subTitle: "Giải đáp các thắc mắc cơ bản",
      onClick: () => {
        openChat();
      },
    },
  ];
  const renderMenu = (menu, isHasAddAppIcon = false) => {
    return (
      <Box borderRadius={"10px"} marginBlock={"20px"} bgcolor={"#fff"}>
        {menu.map((item, index) => (
          <React.Fragment key={index + item.title}>
            <Stack
              key={index + item.title}
              direction="row"
              gap={1}
              padding={2}
              justifyContent="space-between"
              onClick={item.onClick}
            >
              <Stack direction="row" alignItems="center" gap={2}>
                <img width={20} height={20} src={item.icon} alt="icon" />
                <Stack>
                  <Typography fontSize={14} fontWeight={700}>
                    {item.title}
                  </Typography>
                  <Typography fontSize={14} color={"#969595"}>
                    {item.subTitle}
                  </Typography>
                </Stack>
              </Stack>
              <IconButton>
                <KeyboardArrowRightIcon />
              </IconButton>
            </Stack>
            {index !== menu.length - 1 && <Divider />}
          </React.Fragment>
        ))}
        {isHasAddAppIcon && (
          <>
            <Divider />
            <PopupAddIconScreenApp />
          </>
        )}
      </Box>
    );
  };
  return (
    <Box>
      {renderMenu(menu1, true)}
      {renderMenu(menu2)}
    </Box>
  );
}
