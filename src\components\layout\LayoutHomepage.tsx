import {
  <PERSON><PERSON>,
  <PERSON>,
  Container,
  IconButton,
  InputAdornment,
  Stack,
  Paper,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";
import { useTheme } from "@mui/material/styles";
import NotificationsActiveOutlinedIcon from "@mui/icons-material/NotificationsActiveOutlined";
import SearchIcon from "@mui/icons-material/Search";
import CategorySlider from "../category/CategorySlider";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { COLORS, theme } from "@/constants/themes";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { setPreviousPage } from "@/redux/slices/common/commonSlice";
import { useDispatch, useSelector } from "react-redux";
import UserProfile from "../user-profile/UserProfile";
import { RootState } from "@/redux/store";
import CloseIcon from "@mui/icons-material/Close";

export default function LayoutHomePage({ children }: any) {
  const navigate = useNavigate();
  const theme = useTheme();
  const { handleSubmit, register } = useForm();
  const dispatch = useDispatch();

  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );
  const APP_SLOGAN = useSelector(
    (state: RootState) => state.config.data.appSlogan ?? ""
  );

  const onSubmit = async (data) => {
    navigate(`/product?name=${data.valueSearch}`);
  };
  return (
    <Box style={{ position: "relative", paddingBottom: "100px" }}>
      <Box
        style={{
          background: COLORS.colorHeader,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: 160,
          zIndex: 0,
          borderBottomLeftRadius: 30,
          borderBottomRightRadius: 30,
        }}
      >
        <Box style={styles.container}>
          <Box style={{ paddingBlock: 24 }}>
            <Container maxWidth="lg" disableGutters>
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                paddingBottom="10px"
                paddingLeft="15px"
                paddingRight="15px"
              >
                {/* Logo + name */}
                <Stack direction="row" alignItems="center" gap={1}>
                  <Box
                    sx={{
                      width: 58,
                      height: 58,
                      borderRadius: "50%",
                      overflow: "hidden",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <img
                      src="/images/logo.png"
                      alt="Logo"
                      style={{ width: "100%", height: "100%", objectFit: "contain" }}
                    />
                  </Box>

                  <Typography sx={{ color: "#fff", fontWeight: 800, fontSize: '15px' }}>
                    LIBERTY HOLDINGS
                  </Typography>
                </Stack>

                {/* Actions */}
                <Stack direction="row" alignItems="center" gap={1}>
                  <IconButton>
                    {/* <Badge color="error" variant="dot"> */}
                      <img
                        src="/images/notification.png"
                        alt="Notification"
                        style={{ width: 45, height: 45 }}
                      />
                    {/* </Badge> */}
                  </IconButton>


                  <Paper
                    elevation={0}
                    sx={{
                      px: 0.5,
                      py: 0,
                      borderRadius: 20,
                      display: "flex",
                      alignItems: "center",
                      // border: "1px solid #ccc",
                    }}
                  >
                    <IconButton size="small">
                      <MoreVertIcon sx={{ color: COLORS.primary }} />
                    </IconButton>

                    <IconButton size="small">
                      <CloseIcon sx={{ color: COLORS.primary }} />
                    </IconButton>
                  </Paper>
                </Stack>
              </Stack>
            </Container>

            {children}
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  background: {
    background: theme.palette.primary.main,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: 180,
    zIndex: 0,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  container: {
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    backgroundPositionY: "center top",
    paddingBottom: 64,
    // paddingTop: 10,
  },
  sologanContainer: {
    position: "relative",
    zIndex: 1,
    display: "flex",
    alignItems: "center",
  },
};
