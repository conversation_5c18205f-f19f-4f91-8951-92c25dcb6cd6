import React, { useEffect, useState } from "react";
import FrameContainer from "../components/layout/Container";
import { <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material";
import Slider from "react-slick";
import { AppDispatch, RootState } from "../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { getRouteParams } from "zmp-sdk";
import { joinTeam } from "../redux/slices/team/team";
import {
  getMyParent,
  getUser,
  getUserZalo,
  register,
} from "../redux/slices/authen/authSlice";

import { APP_NAME } from "../constants/AppInfo";
import { mapError, showToast } from "../utils/common";
import { useAlert } from "../redux/slices/alert/useAlert";
import { Icon } from "../constants/Assets";
import { Box } from "zmp-ui";
import { LoadingButton } from "@mui/lab";
import { Router } from "../constants/Route";
import { useNavigate } from "../utils/component-util";
import { COLORS } from "../constants/themes";
import { useShareReferLink } from "../hooks/useShareReferLink";

interface IParent {
  id: number;
  name: string;
  referCode: string;
}

export default function ReferPage() {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const updatedReferCode = user?.updatedReferCode;
  const [fParent, setFParent] = useState<IParent>(null as unknown as IParent);
  const { refCode } = getRouteParams();
  const [refCodeTxt, setRefCodeTxt] = useState<string>(refCode);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const { showAlert } = useAlert();
  const { shareLink } = useShareReferLink();

  const getParent = async () => {
    try {
      const res = await dispatch(getMyParent());
      if (res?.payload?.data) {
        setFParent(res.payload.data);
      }
    } catch (e: any) {
      showToast({
        content: e.message ?? "Xuất hiện lỗi trong quá trình lấy dữ liệu",
        type: "error",
      });
    }
  };

  useEffect(() => {
    getParent();
  }, [user]);

  useEffect(() => {
    if (refCode) {
      setRefCodeTxt(refCode);
    }
  }, [refCode]);

  const handleJoinTeam = async () => {
    if (!refCodeTxt) {
      showToast({
        content: "Vui lòng nhập mã giới thiệu!",
        type: "error",
      });
      return;
    }
    try {
      const res: any = await dispatch(joinTeam(refCodeTxt)).unwrap();
      if (res.error) {
        showAlert({
          content: "Mã giới thiệu không hợp lệ!",
        });
        return;
      } else {
        showAlert({
          icon: Icon.check,
          title: "Bạn đã cập nhật mã giới thiệu thành công!",
          buttons: [
            {
              title: "OK",
              action: () => {
                dispatch(getUser());
              },
            },
          ],
        });
      }
    } catch (e: any) {
      showToast({
        content: mapError(e?.message),
        type: "error",
      });
    }
  };

  const navigateHome = () => {
    getParent();
    navigate(Router.homepage);
  };

  const handleRegister = async () => {
    if (!refCodeTxt) {
      return showToast({
        content: "Vui lòng nhập mã giới thiệu",
        type: "error",
      });
    }
    setLoading(true);
    const res = await dispatch(register(refCodeTxt)).unwrap();
    if (res.jwt) {
      showAlert({
        icon: Icon.check,
        title: "Kích hoạt tài khoản thành công",
        buttons: [{ title: "OK", action: navigateHome }],
      });
      await dispatch(getUserZalo());
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  /**
   * @description styles
   */
  const titleStyle: React.CSSProperties = {
    fontWeight: 700,
    fontSize: 16,
    marginBottom: 12,
    color: COLORS.blue,
  };
  const buttonContainerStyle: React.CSSProperties = {
    color: "#FFF",
    background: COLORS.primary,
    borderRadius: 99,
    padding: "4 0",
    width: "80%",
  };
  const inputContainerStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
    background: "#F4F4F4",
    padding: 4,
    borderRadius: "10px",
    marginBottom: 12,
    width: "100%",
  };
  const titleInputStyle: React.CSSProperties = {
    color: COLORS.blue,
  };
  const referCodeInput: React.CSSProperties = {
    border: "none",
    backgroundColor: "transparent",
    padding: 10,
    color: "#1D1D5E",
    fontWeight: 500,
    fontSize: 16,
    width: "50%",
    outline: "none",
  };
  const codeLabelStyle: React.CSSProperties = {
    color: "#1D1D5E",
    fontWeight: 500,
    fontSize: 16,
    width: 100,
  };

  //----------------------

  /**
   * @description render UI
   */

  const LineUI = () => (
    <Box
      style={{
        width: "1px",
        height: "38px",
        background: "#D9D9D9",
        margin: "0 5px",
      }}
    />
  );

  const renderNotUpdateReferCode = () => (
    <Stack
      style={{ background: "#fff" }}
      padding={2}
      borderRadius={4}
      marginBlock={1}
    >
      <Typography style={titleStyle}>
        Kích hoạt tài khoản Đối tác kinh doanh {APP_NAME} để gia tăng thu nhập
        thụ động bằng việc bán hàng online
      </Typography>

      <Box style={inputContainerStyle}>
        <Typography style={titleInputStyle} px={2}>
          Mã giới thiệu
        </Typography>
        <LineUI />
        <input
          placeholder="Nhập mã"
          value={refCodeTxt}
          onChange={(e) => setRefCodeTxt(e.target.value)}
          style={referCodeInput}
        />
      </Box>
      <LoadingButton
        size="large"
        style={buttonContainerStyle}
        loading={loading}
        disabled={loading}
        onClick={user ? handleJoinTeam : handleRegister}
      >
        {loading ? "" : user ? "Cập nhật" : "Kích hoạt tài khoản"}
      </LoadingButton>
      <Box style={{ paddingTop: 16 }}>
        <Box style={inputContainerStyle}>
          <Typography style={titleInputStyle} px={2}>
            Mã giới thiệu của bạn
          </Typography>
          <LineUI />
          <Typography style={codeLabelStyle}>{user?.referCode}</Typography>
        </Box>
        <Button size="large" style={buttonContainerStyle} onClick={shareLink}>
          Chia sẻ link giới thiệu
        </Button>
      </Box>
    </Stack>
  );

  const renderUpdatedReferCode = () => (
    <Stack
      style={{ background: "#fff" }}
      padding={2}
      borderRadius={4}
      marginBlock={1}
      alignItems={"center"}
    >
      <Box style={inputContainerStyle}>
        <Typography style={titleInputStyle} px={2}>
          Người giới thiệu
        </Typography>
        <LineUI />
        <Typography style={codeLabelStyle}>{fParent?.referCode}</Typography>
      </Box>

      <Box style={inputContainerStyle}>
        <Typography style={titleInputStyle} px={2}>
          Mã giới thiệu của bạn
        </Typography>
        <LineUI />
        <Typography style={codeLabelStyle}>{user?.referCode}</Typography>
      </Box>
      <Button size="large" style={buttonContainerStyle} onClick={shareLink}>
        Chia sẻ link giới thiệu
      </Button>
    </Stack>
  );

  return (
    <FrameContainer title="Giới thiệu cộng tác viên">
      {/* {renderUpdatedReferCode()} */}
      {updatedReferCode && fParent
        ? renderUpdatedReferCode()
        : renderNotUpdateReferCode()}
    </FrameContainer>
  );
}
