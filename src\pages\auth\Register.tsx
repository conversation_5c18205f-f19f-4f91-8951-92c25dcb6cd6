import {
  Box,
  Button,
  Container,
  DialogActions,
  DialogContent,
  Stack,
  TextField,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import Page from "zmp-ui/page";
import { AppDispatch } from "../../redux/store";
import { useDispatch } from "react-redux";
import * as Yup from "yup";
import { PhoneRegex } from "../../constants/Const";
import { yupResolver } from "@hookform/resolvers/yup";
import { registerForWeb } from "../../redux/slices/authen/authSlice";
import { showToast } from "../../utils/common";
import { Router } from "../../constants/Route";
import { useSearchParams } from "react-router-dom";
import { useNavigate } from "../../utils/component-util";
import { Icon } from "@/constants/Assets";

const TOP_NAV_HEIGHT = 64;

export default function Register() {
  const validationSchema = Yup.object().shape({
    phone: Yup.string()
      .required("<PERSON>ui lòng nhập số điện thoại")
      .min(10, "Tối thiểu 10 ký tự")
      .max(13, "Tối đa 13 ký tự")
      .matches(PhoneRegex, "Định dạng số điện thoại không đúng"),
    referCode: Yup.string(),
    password: Yup.string()
      .min(6, `Tối thiểu 6 ký tự`)
      .max(30, `Tối đa 30 ký tự`)
      .required(`Vui lòng nhập mật khẩu`),
    confirmPassword: Yup.string()
      .required("Vui lòng nhập lại mật khẩu")
      .oneOf([Yup.ref("password"), "aa"], "Mật khẩu không khớp"),
    acceptTerms: Yup.bool().oneOf([true], "Bạn cần đồng ý với điều khoản"),
  });
  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(validationSchema),
    mode: "all",
  });
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [params] = useSearchParams();

  useEffect(() => {
    const code = params.get("referCode");
    code && setValue("referCode", code);
  }, [params]);

  const onSubmit = async (data) => {
    if (Object.keys(errors).length > 0) return;
    const res = await dispatch(
      registerForWeb({
        phone: data.phone,
        password: data.password,
        referCode: data.referCode,
      })
    ).unwrap();
    if (res.user.id) {
      showToast({
        content: "Đăng ký tài khoản thành công",
        type: "success",
      });
      navigate(Router.login);
    }
  };

  return (
    <Page>
      <Box component="header">
        <Container maxWidth="lg">
          <Stack direction="row" spacing={2} sx={{ height: TOP_NAV_HEIGHT }}>
            <Stack
              alignItems="center"
              direction="row"
              display="inline-flex"
              spacing={1}
              sx={{ textDecoration: "none" }}
            >
              <Box
                sx={{
                  display: "inline-flex",
                  height: 24,
                  width: 24,
                }}
              >
                <Container>
                  <Stack direction="row" gap={2}>
                    <img
                      style={{ borderRadius: "50%" }}
                      width={150}
                      height={52}
                      src={Icon.logo}
                    />
                  </Stack>
                </Container>
              </Box>
            </Stack>
          </Stack>
          <Stack direction="row" gap={3} justifyContent="center" pt={4}>
            <Stack
              direction="row"
              alignItems="center"
              gap={3}
              justifyContent={"center"}
            >
              <Stack alignItems={"center"}>
                <span style={{ fontWeight: 500, fontSize: 24 }}>Đăng ký</span>
                <span style={{ color: "#969595" }}>
                  Bạn đã có tài khoản?{" "}
                  <Button onClick={() => navigate(Router.login)}>
                    Đăng nhập
                  </Button>
                </span>
              </Stack>
            </Stack>
          </Stack>
        </Container>
      </Box>

      <Box
        sx={{
          alignItems: "center",
          display: "flex",
          justifyContent: "center",
          flex: "1 1 auto",
        }}
      >
        <Container maxWidth="sm">
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogContent>
              <Stack gap={3}>
                <Controller
                  name="phone"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      autoFocus
                      id="standard-required"
                      label="Số điện thoại*"
                      variant="standard"
                      {...register("phone")}
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
                <Controller
                  name="password"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      {...register("password")}
                      fullWidth
                      type="password"
                      label="Mật khẩu *"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
                <Controller
                  name="confirmPassword"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      {...register("confirmPassword")}
                      fullWidth
                      type="password"
                      label="Nhập lại mật khẩu *"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
                <Controller
                  name="referCode"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      {...register("referCode")}
                      fullWidth
                      label="Mã giới thiệu"
                      variant="standard"
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
                <Controller
                  name="acceptTerms"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...register("acceptTerms")}
                          style={{
                            color: error ? "red" : undefined,
                          }}
                        />
                      }
                      label="Tôi đã đọc và đồng ý với điều khoản dịch vụ"
                    />
                  )}
                />
              </Stack>
            </DialogContent>

            <DialogActions>
              <Button
                type="submit"
                style={{
                  color: "#fff",
                  width: "100%",
                  height: 55,
                  fontSize: "16px",
                  fontWeight: 400,
                  lineHeight: "19.36px",
                  border: "1px solid transparent",
                  borderRadius: 10,
                  padding: "2px",
                }}
                color="primary"
                variant="contained"
              >
                Đăng ký
              </Button>
            </DialogActions>
          </form>
        </Container>
      </Box>
    </Page>
  );
}
