import React from "react";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import { Icon } from "../constants/Assets";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { Level, LevelIcon, LevelName } from "../constants/Const";
import ProfileHeader from "../pages/Profile/ProfileHeader";
import { formatPrice } from "../utils/formatPrice";
import { COLORS, commonStyle } from "../constants/themes";

export default function MemberShipItem() {
  const { user } = useSelector((state: RootState) => state.auth);
  const { teamReport } = useSelector((state: RootState) => state.team);

  if (!user)
    return (
      <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
        <CircularProgress />
      </Stack>
    );

  // const iconCurLevel = LevelIcon[user.level] ?? Icon.verify;
  // const iconNextLevel = LevelIcon[user.level + 1] ?? Icon.verify;
  // const strNextLevel = LevelName[user.level + 1] ?? "N/A";
  // const curLevelValue = parseInt(teamReport?.userLevelData.curLevelValue);
  // const nextLevelValue = parseInt(teamReport?.userLevelData.nextLevelValue);
  // const valueToNextLevel = nextLevelValue - curLevelValue;
  // const currentProcessPercent = nextLevelValue
  //   ? Math.round((curLevelValue / nextLevelValue) * 100)
  //   : 0;

  return (
    <Stack style={styles.container}>
      <ProfileHeader />
      <Stack direction="row" style={styles.blockContainer}>
        <Box
          sx={styles.blockItem}
          borderRadius={2}
          border={1}
          borderColor={COLORS.neutral9}
        >
          <Typography style={styles.blockTitle}>Tổng chi tiêu</Typography>
          <Typography style={styles.priceText}>
            {formatPrice(user.mySale || 0)}
          </Typography>
        </Box>
        <Box
          sx={styles.blockItem}
          borderRadius={2}
          border={1}
          borderColor={COLORS.neutral9}
        >
          <Typography style={styles.blockTitle}>Điểm thưởng</Typography>
          <Typography style={styles.priceText}>
            {user.bonusPoint || 0}
          </Typography>
        </Box>
      </Stack>
      {/* {user.level < Level.DiamondMember && (
        <Stack>
          <Stack
            direction="row"
            alignItems={"center"}
            gap={1}
            paddingTop={2}
            justifyContent="space-between"
          >
            <img src={iconCurLevel} alt="" width={40} />
            <Stack style={{ paddingBottom: 4 }}>
              <Typography
                style={{ textAlign: "center", whiteSpace: "pre-line" }}
              >
                Bạn cần tích luỹ thêm{"\n"}
                <b style={{ color: COLORS.accent1, fontWeight: 700 }}>
                  {!user.level ? "1 đơn hàng" : formatPrice(valueToNextLevel)}
                </b>{" "}
                để nâng hạng{"\n"}
                <b>{strNextLevel}</b>
              </Typography>
            </Stack>
            <img src={iconNextLevel} alt="" width={40} />
          </Stack>
          <Stack>
            <Box style={styles.processContainer}>
              <Box
                style={{
                  ...styles.currentProcess,
                  width: `${currentProcessPercent}%`,
                }}
              />
            </Box>
            <Stack direction="row" justifyContent={"space-between"}>
              <Typography>{formatPrice(0)}</Typography>
              <Typography>
                {!user.level
                  ? "1 đơn hàng"
                  : formatPrice(
                      parseInt(teamReport?.userLevelData.nextLevelValue)
                    )}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      )} */}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    display: "flex",
    borderRadius: 20,
    padding: 12,
    backgroundColor: "#FFFFFF",
  },
  smallTitle: {
    fontSize: 12,
    fontWeight: 700,
  },
  processContainer: {
    background: COLORS.neutral7,
    height: 7,
    width: "100%",
  },
  currentProcess: {
    background: COLORS.accent1,
    height: 7,
  },
  blockContainer: {
    alignItems: "center",
    justifyContent: "space-around",
    paddingTop: 16,
  },
  blockItem: {
    paddingBlock: 0.5,
    paddingInline: 2.5,
    alignItems: "center",
    justifyContent: "center",
  },
  blockTitle: {
    textAlign: "center",
    color: COLORS.neutral3,
    fontSize: 16,
  },
  priceText: {
    ...commonStyle.headline16,
    textAlign: "center",
    color: COLORS.primary1,
  },
};
