import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { Stack, Typography } from "@mui/material";
import { default as React, useEffect } from "react";
import FrameContainer from "../../../components/layout/Container";
import { useNavigate } from "../../../utils/component-util";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../redux/store";
import { getTermList } from "../../../redux/slices/term/termSlice";
import { ITerm } from "../../../types/term";
import { COLORS } from "../../../constants/themes";
import { Router } from "@/constants/Route";

export default function TermList() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { termList } = useSelector((state: RootState) => state.term);

  useEffect(() => {
    dispatch(getTermList());
  }, []);

  return (
    <FrameContainer title="Chính sách và hỏi đáp">
      <Stack style={styles.container}>
        {termList &&
          termList.map((term: ITerm, i) => (
            <Stack key={`policy-${i}`} style={styles.itemContainer}>
              <Stack
                direction="row"
                gap={3}
                sx={styles.content}
                onClick={() => {
                  navigate(`${Router.profile.term.index}/${term.id}`);
                }}
              >
                <Typography>{term?.attributes.title}</Typography>
                <KeyboardArrowRightIcon style={{ color: COLORS.neutral6 }} />
              </Stack>
            </Stack>
          ))}
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: 10,
    background: "#FFF",
    paddingBlock: 8,
  },
  content: {
    paddingInline: 3,
    paddingBlock: 1.2,
    justifyContent: "space-between",
  },
  iconContainer: {
    padding: 4,
    color: "#000",
  },
};
