import { IProduct } from "@/types/product";
import { convertStrapiDocuments } from "@/utils/common";
import { request } from "@/utils/request";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ProductGift {
  promotion: any;
  currentGift: {
    product: IProduct;
    quantity: number;
  } | null;
  isLoading: boolean;
}

const initialState: ProductGift = {
  promotion: null,
  isLoading: false,
  currentGift: null,
};

export const getPromotionByProduct = createAsyncThunk(
  "promotion/getPromotionByProduct",
  async (data: { productId: number; userId: number }) => {
    const response: any = await request("get", "/api/promotions", {
      populate: "users, products",
      filters: {
        products: {
          id: {
            $eq: data.productId,
          },
        },
        users: {
          $or: [{ id: { $null: true } }, { id: { $eq: data.userId } }],
        },
      },
    });
    return response;
  }
);
const promotionSlice = createSlice({
  name: "promotion",
  initialState,
  reducers: {
    setCurrentGift: (
      state,
      action: PayloadAction<{
        product: IProduct;
        quantity: number;
      } | null>
    ) => {
      state.currentGift = action?.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getPromotionByProduct.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getPromotionByProduct.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
          const { payload } = action;
          const promotions = convertStrapiDocuments(payload.data);
          state.promotion = promotions?.[0] || [];
        }
      )
      .addCase(getPromotionByProduct.rejected, (state) => {
        state.isLoading = false;
      });
  },
});

export const { setCurrentGift } = promotionSlice.actions;
export default promotionSlice.reducer;
