import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

export default function useKeepPositionScroll(pathname) {
  const location = useLocation();
  const [scrollTop, setScrollTop] = useState(0);
  const [isScroll, setIsScroll] = useState(false);
  useEffect(() => {
    let newScrollPosition = [];
    const scrollPosition =
      JSON.parse(sessionStorage.getItem("scroll-position")) || [];
    const id = scrollPosition.findIndex(
      (v) => v.pathname === location.pathname
    );
    if (id === -1) {
      newScrollPosition = [
        ...scrollPosition,
        {
          pathname: location.pathname,
          position: scrollTop,
        },
      ];
    } else {
      newScrollPosition = [...scrollPosition];
      newScrollPosition.splice(id, 1, {
        pathname: location.pathname,
        position: scrollTop,
      });
      if (!isScroll) {
        document.body.scrollTop = scrollPosition[id].position;
      }
    }
    sessionStorage.setItem(
      "scroll-position",
      JSON.stringify(newScrollPosition)
    );

  }, [scrollTop, isScroll, location.pathname]);

  const windowScrollTop = () => {
    const position = document.body.scrollTop;
    setIsScroll(true);
    setScrollTop(position);
  };

  useEffect(() => {
    document.body.addEventListener("scroll", windowScrollTop);
    return () => document.body.removeEventListener("scroll", windowScrollTop);
  }, []);

  return location;
}
