import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { Box, Container, Stack, Typography } from "@mui/material";
import { COLORS } from "@/constants/themes";
import { Level, LevelName } from "@/constants/Const";

const LineUI = () => (
  <Box style={{ width: "1px", height: "38px", background: "#D9D9D9" }} />
);

const inputContainerStyle: React.CSSProperties = {
  display: "flex",
  alignItems: "center",
  justifyContent: "space-around",
  background: "#F4F4F4",
  padding: 4,
  borderRadius: "10px",
  marginBottom: 12,
};

const titleInputStyle: React.CSSProperties = {
  color: COLORS.darkBlue,
  width: "40%",
};
const codeLabelStyle: React.CSSProperties = {
  color: "#29BB9C",
  fontWeight: 500,
  fontSize: 16,
  width: 100,
};

export default function PopupConfirmRegister({
  open,
  handleConfirm,
  handleClose,
  referUser,
}) {
  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        disableEscapeKeyDown={false}
        PaperProps={{
          style: {
            borderRadius: "10px",
            boxShadow: "none",
            margin: "8px",
          },
        }}
      >
        <DialogContent>
          <Container>
            <Stack justifyContent={"center"} alignItems={"center"} gap={2}>
              <Typography color={"#1D1D5E"} fontSize={18} fontWeight={700}>
                Thông tin người giới thiệu
              </Typography>
              <Box>
                <Box style={inputContainerStyle}>
                  <Typography style={titleInputStyle}>Mã giới thiệu</Typography>
                  <LineUI />
                  <Typography style={codeLabelStyle}>
                    {referUser?.referCode}
                  </Typography>
                </Box>

                <Box style={inputContainerStyle}>
                  <Typography style={titleInputStyle}>
                    Tên người giới thiệu
                  </Typography>
                  <LineUI />
                  <Typography style={codeLabelStyle}>
                    {referUser?.name}
                  </Typography>
                </Box>

                <Box style={inputContainerStyle}>
                  <Typography style={titleInputStyle}>
                    Rank người giới thiệu
                  </Typography>
                  <LineUI />
                  <Typography style={codeLabelStyle}>
                    {referUser?.level
                      ? LevelName[referUser?.level]
                      : LevelName[Level.Member]}
                  </Typography>
                </Box>
              </Box>
              <Stack direction={"row"} gap={2}>
                <Button
                  variant="contained"
                  disableElevation
                  style={{
                    color: "#fff",
                    height: 40,
                    fontSize: "16px",
                    fontWeight: 700,
                    lineHeight: "19.36px",
                    borderRadius: 40,
                    background: "#82C3FFF5",
                    minWidth: "150px",
                  }}
                  onClick={handleClose}
                >
                  Để sau
                </Button>
                <Button
                  variant="contained"
                  disableElevation
                  style={{
                    color: "#fff",
                    height: 40,
                    fontSize: "16px",
                    fontWeight: 700,
                    lineHeight: "19.36px",
                    borderRadius: 40,
                    background: COLORS.primary,
                    minWidth: "150px",
                  }}
                  onClick={handleConfirm}
                >
                  Đăng ký ngay
                </Button>
              </Stack>
            </Stack>
          </Container>
        </DialogContent>
      </Dialog>
    </>
  );
}
