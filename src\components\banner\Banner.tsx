import React, { memo } from "react";
import Slider from "react-slick";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { useNavigate } from "../../utils/component-util";
import { Box } from "@mui/material";
import { BannerType } from "@/types/news";
import { setPreviousPage } from "@/redux/slices/common/commonSlice";

const Banner = ({ type }: { type: BannerType }) => {
  const banner = useSelector((state: RootState) => state.news.banner);
  const filteredBanner = banner?.filter((item) => item.type === type) || [];

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const settings = {
    dots: filteredBanner.length > 1,
    infinite: filteredBanner.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: filteredBanner.length > 1,
    autoplaySpeed: 3000,
    arrows: false,
  };

  return (
    <Box>
      {filteredBanner.length > 0 ? (
        <Slider {...settings} className="slider-banner">
          {filteredBanner.map((item) => (
            <Box
              key={item.id}
              onClick={() => {
                navigate(`/posts/${item.id}`);
                dispatch(setPreviousPage(`/posts/${item.id}`));
              }}
            >
              <Box>
                <Box
                  style={{
                    display: "flex",
                    overflow: "hidden",
                    aspectRatio: 7 / 4,
                    width: "100%",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundImage: `url(${`${import.meta.env.VITE_API_URL}${
                      item?.image?.data?.attributes?.formats?.medium?.url
                    }`})`,
                    cursor: "pointer",
                  }}
                />
              </Box>
            </Box>
          ))}
        </Slider>
      ) : null}
    </Box>
  );
};

export default memo(Banner);
