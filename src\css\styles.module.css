.pageContent {
  padding-top: calc(var(--zaui-safe-area-inset-top, 0px) + 44px); /** 44 is header height*/
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px))
}

.toast {
  margin-top: calc(var(--zaui-safe-area-inset-top, 0px));
}

.topSafe {
  padding-top: calc(var(--zaui-safe-area-inset-top, 0px) + 44px);
}

.bottomSafe {
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px));
}

.bottomPayElement {
  max-width: 425px;
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px) + 12px);
  width: 100%;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  height: auto;
  border-top: 1px solid rgb(223, 223, 223);
  padding-top: 12px;
  background: rgb(255, 255, 255);
}

.bottomCartElement {
  max-width: 425px;
  padding-bottom: calc(var(--zaui-safe-area-inset-bottom, 16px));
  width: 100%;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  height: auto;
  border-top: 1px solid rgb(223, 223, 223);
  background: rgb(255, 255, 255);
}