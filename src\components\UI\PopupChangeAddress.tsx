import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Slide,
  Stack,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import React, { useEffect } from "react";
import { useState } from "react";
import { Icon } from "../../constants/Assets";
import { useDispatch, useSelector } from "react-redux";
import {
  getAddressList,
  getCurrentAddress,
  setCurrentAddress,
} from "../../redux/slices/address/addressSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { updateUser } from "../../redux/slices/authen/authSlice";
import { useTheme } from "@mui/material/styles";
import { Router } from "../../constants/Route";
import { useCheckLogin } from "../../hooks/useCheckLogin";
import { useNavigate } from "../../utils/component-util";
import PopupAddAddress from "./PopupAddAddress";
import GradientText from "./GradientTextProps";
import { COLORS } from "@/constants/themes";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function PopupChangeAddress() {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const theme = useTheme();
  const { user } = useSelector((state: RootState) => state.auth);
  const listAddress = useSelector((state: RootState) => state.address.list);
  const { currentAddress, province, district, ward } = useSelector(
    (state: RootState) => state.address
  );
  const { checkLogin } = useCheckLogin();
  const [open, setOpen] = React.useState(false);
  const handleClickOpen = () => {
    setOpen(true);
  };

  const [newAddressId, setNewAddressId] = useState<number | null>();

  useEffect(() => {
    if (!newAddressId && currentAddress?.id) {
      setNewAddressId(currentAddress.id);
    }
  }, [currentAddress]);

  useEffect(() => {
    if (!currentAddress && user?.addressId) {
      dispatch(getCurrentAddress(user.addressId));
    }
  }, [user, currentAddress]);

  useEffect(() => {
    if (!Array.isArray(currentAddress) || !currentAddress.length) {
      dispatch(getAddressList());
    }
  }, [user]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleClickFormAddressOpen = () => {
    handleClickOpen();
  };

  const handleChangeAddress = async () => {
    if (!Array.isArray(listAddress)) return;

    setOpen(false);
    if (user && newAddressId && user.addressId !== newAddressId) {
      const res = await dispatch(
        updateUser({
          id: user.id,
          addressId: newAddressId,
        })
      );

      if (res) {
        const newAddress: any = listAddress.find(
          (item) => item.id === newAddressId
        );

        dispatch(setCurrentAddress(newAddress));
      }
    }
  };

  const onClickAddress = () => {
    checkLogin(() => {
      navigate(Router.profile.address, { state: { shouldGoBack: true } });
    });
  };

  return (
    <>
      {currentAddress ? (
        <Stack direction="row" gap={3} onClick={handleClickOpen}>
          <img src={Icon.map} />
          <Stack>
            <span style={{ fontWeight: 700 }}>
              {currentAddress?.userName} - {currentAddress?.phone}
            </span>
            <span
              style={{
                color: "#969595",
                WebkitLineClamp: 1,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
                textAlign: "start",
                display: "-webkit-box",
              }}
            >
              {currentAddress?.address} - {currentAddress?.addressObj?.wardText}{" "}
              - {currentAddress?.addressObj?.districtText} -
              {currentAddress?.addressObj?.provinceText}
            </span>
          </Stack>
          <IconButton>
            <KeyboardArrowRightIcon />
          </IconButton>
        </Stack>
      ) : (
        <Stack direction="row" gap={3} onClick={onClickAddress}>
          <img src={Icon.map} />
          <Stack>
            <span style={{ color: "#F96968", fontWeight: 700 }}>
              Bạn chưa có thông tin địa chỉ
            </span>
            <span style={{ color: "#969595" }}>
              Bấm vào đây để thêm đia chỉ nhé
            </span>
          </Stack>
          <IconButton>
            <KeyboardArrowRightIcon />
          </IconButton>
        </Stack>
      )}

      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogContent id="alert-dialog-slide-description">
          <Stack direction="row" gap={2} style={{ fontSize: 16 }}>
            <FormControl style={{ width: "100%" }}>
              <Stack gap={2}>
                <RadioGroup
                  row
                  aria-labelledby="demo-form-control-label-placement"
                  name="position"
                  defaultValue="top"
                >
                  <Stack gap={2} width={"100%"}>
                    <FormControl>
                      <RadioGroup
                        aria-labelledby="demo-radio-buttons-group-label"
                        defaultValue={user?.addressId}
                        name="radio-buttons-group"
                      >
                        {listAddress?.map((item, index) => (
                          <Stack key={`address-${index}`}>
                            <Stack
                              width={"100%"}
                              direction="row"
                              justifyContent="space-between"
                              onClick={() => setNewAddressId(item.id)}
                            >
                              <Stack direction={"row"} alignItems={"center"}>
                                <img height={24} width={24} src={Icon.map} />
                                <Stack ml={3}>
                                  <span
                                    style={{
                                      fontWeight: 700,
                                    }}
                                  >
                                    {item?.userName} - {item?.phone}
                                  </span>
                                  <span
                                    style={{
                                      WebkitLineClamp: 2,
                                      WebkitBoxOrient: "vertical",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      textAlign: "start",
                                      display: "-webkit-box",
                                    }}
                                  >
                                    {item?.address} -{" "}
                                    {item?.addressObj?.wardText} -{" "}
                                    {item?.addressObj?.districtText} -{" "}
                                    {item?.addressObj?.provinceText}
                                  </span>
                                </Stack>
                              </Stack>
                              <FormControlLabel
                                value={item.id.toString()}
                                control={<Radio />}
                                labelPlacement="start"
                                label=""
                                sx={{
                                  ".Mui-checked": {
                                    color: COLORS.primary2,
                                  },
                                }}
                                checked={newAddressId == item.id}
                                onChange={async (e: any) => {
                                  setNewAddressId(e.target.value);
                                }}
                              />
                            </Stack>
                            {index !== listAddress.length - 1 && (
                              <Divider sx={{ margin: "10px 0" }} />
                            )}
                          </Stack>
                        ))}
                      </RadioGroup>
                    </FormControl>
                  </Stack>
                </RadioGroup>
              </Stack>
            </FormControl>
          </Stack>
          <Box marginTop={2}>
            <PopupAddAddress size="small" />
          </Box>
        </DialogContent>
        <DialogActions
          sx={{ justifyContent: "center", gap: 2, marginBlock: 2 }}
        >
          <Button
            style={{
              background: COLORS.primary1,
              color: "#1D1D5E",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
            }}
            // href={"/cart"}
            onClick={() => setOpen(false)}
            startIcon={<CloseIcon />}
          >
            Hủy bỏ
          </Button>
          <Button
            style={{
              background: COLORS.primary,
              color: "#fff",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
            }}
            startIcon={<CheckCircleOutlineIcon />}
            onClick={() => handleChangeAddress()}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
