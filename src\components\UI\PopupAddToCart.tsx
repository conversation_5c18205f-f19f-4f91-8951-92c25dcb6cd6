import {
  Box,
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogContentText,
  IconButton,
  RadioGroup,
  Slide,
  Stack,
  Typography,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import React, { useState } from "react";
import HorizontalRuleIcon from "@mui/icons-material/HorizontalRule";
import AddIcon from "@mui/icons-material/Add";
import { Icon } from "../../constants/Assets";
import { useCart } from "../../hooks/useCart";
import { IProduct } from "../../types/product";
import { formatPrice } from "../../utils/formatPrice";
import { useTheme } from "@mui/material/styles";
import { showToast } from "../../utils/common";
import { useNavigate } from "../../utils/component-util";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import GradientText from "./GradientTextProps";
import { round } from "lodash";
import { COLORS } from "@/constants/themes";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const PopupAddToCart: React.FC<{
  product: IProduct;
}> = ({ product }) => {
  const nav = useNavigate();
  const currentGift = useSelector(
    (state: RootState) => state.promotion.currentGift
  );
  const { addProductToCart } = useCart();
  const theme = useTheme();

  const [quantity, setQuantity] = useState(1);
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const reduce = () => {
    setQuantity((quantity) => (quantity > 0 ? quantity - 1 : quantity));
  };
  const increase = () => {
    setQuantity(quantity + 1);
  };

  const discountPercent = product.discount
    ? round((product.discount / product.price) * 100)
    : null;
  return (
    <>
      <Button
        style={{
          fontSize: 14,
          display: "flex",
          gap: 4,
          fontWeight: 700,
          margin: 0,
          alignItems: "center",
        }}
        onClick={handleClickOpen}
      >
        <img src={Icon.cart} style={{ width: 20 }} />
        <GradientText text="Thêm vào giỏ hàng" />
      </Button>
      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogContent>
          <Stack direction="row" gap={2} style={{ fontSize: 16 }}>
            <img width={80} height={80} src={product?.image?.[0]?.url} />
            <Stack>
              <Typography style={{ fontWeight: 700 }}>
                {product?.name}
              </Typography>
              <Box sx={{ display: "flex", alignItems: "baseline" }}>
                <Box
                  sx={{
                    display: "flex",
                    color: "#1D1D5E",
                    fontWeight: 700,
                    fontStyle: "italic",
                    fontSize: 16,
                  }}
                  gap={1}
                  alignItems="center"
                >
                  <GradientText
                    text={formatPrice(product.price - product.discount)}
                    style={{ paddingInline: 1 }}
                  />
                  {product.discount > 0 && (
                    <GradientText text={`(-${discountPercent}%)`} />
                  )}
                </Box>
              </Box>
              <Stack marginBlock={2}>
                <Button
                  style={{
                    background: "transparent",
                    border: "1px solid",
                    borderImage: COLORS.primary,
                    borderImageSlice: 1,
                    color: "#1D1D5E",
                    fontSize: 16,
                    fontWeight: 700,
                  }}
                  color="secondary"
                  variant="outlined"
                  onClick={handleClickOpen}
                >
                  <GradientText text="Mã giảm giá" />
                </Button>
              </Stack>
            </Stack>
          </Stack>
          <Stack
            direction="row"
            width={"100%"}
            style={{
              border: "1px solid #D9D9D9",
              borderRadius: 99,
              padding: "6px 0px",
            }}
            justifyContent="space-between"
            alignItems={"center"}
            mt={2}
          >
            <IconButton
              style={{
                marginInline: 8,
                background: "#EDF7FE",
                borderRadius: "50%",
              }}
              aria-label="fingerprint"
              color="secondary"
              onClick={reduce}
            >
              <HorizontalRuleIcon />
            </IconButton>
            <Stack direction="row" alignItems={"center"} gap={2}>
              <Typography>Số lượng</Typography>
              <input
                style={{
                  width: 60,
                  height: 30,
                  borderRadius: 20,
                  border: "1px solid #D9D9D9",
                  textAlign: "center",
                }}
                onChange={(e) => {
                  setQuantity(Number(e.target.value));
                }}
                value={quantity}
              />
            </Stack>
            <IconButton
              style={{
                marginInline: 8,
                background: "#EDF7FE",
                borderRadius: "50%",
              }}
              aria-label="fingerprint"
              color="secondary"
              onClick={increase}
            >
              <AddIcon />
            </IconButton>
          </Stack>
          {currentGift ? (
            <Stack>
              <Typography
                fontSize={"16px"}
                fontWeight={700}
                color={theme.palette.info.main}
                mt={2}
                mb={1}
              >
                Quà tặng
              </Typography>
              <Stack gap={1} direction="row" alignItems={"center"}>
                <Box
                  mt={"8px"}
                  mb={"8px"}
                  ml={"24px"}
                  width="60px"
                  height="60px"
                >
                  <img
                    style={{ width: "100%", height: "100%" }}
                    src={`${import.meta.env.VITE_API_URL}${
                      currentGift?.product?.image?.[0]?.url
                    }`}
                    alt={"images"}
                  />
                </Box>
                <Box>
                  <Typography fontSize={14}>
                    {currentGift?.product.name}
                    <span
                      style={{
                        color: theme.palette.info.main,
                        fontWeight: "700",
                        paddingLeft: "5px",
                      }}
                    >
                      x{currentGift?.quantity}
                    </span>
                  </Typography>
                  <Typography fontSize={14}>
                    Trị giá
                    <span
                      style={{
                        color: theme.palette.primary.main,
                        fontWeight: "700",
                        paddingLeft: "10px",
                        fontSize: "16px",
                      }}
                    >
                      {currentGift?.product?.price}đ
                    </span>
                  </Typography>
                </Box>
              </Stack>
            </Stack>
          ) : null}
        </DialogContent>
        <DialogActions
          sx={{ justifyContent: "center", gap: 2, marginBlock: 2 }}
        >
          <Button
            style={{
              background: "#EDF7FE",
              color: "#1D1D5E",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 14,
              display: "flex",
              gap: "8px",
            }}
            onClick={() => {
              nav("/cart");
            }}
          >
            <img src={Icon.cart} />
            <GradientText text="Xem giỏ hàng" />
          </Button>
          <Button
            style={{
              background: COLORS.primary,
              color: "#fff",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
              display: "flex",
              gap: "8px",
            }}
            onClick={() => {
              const result = addProductToCart(
                product,
                quantity,
                currentGift || {}
              );
              if (result) {
                showToast({
                  content: "Thêm vào giỏ thành công",
                  type: "success",
                });
                handleClose();
              }
            }}
          >
            <img src={Icon.confirm} />
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PopupAddToCart;
