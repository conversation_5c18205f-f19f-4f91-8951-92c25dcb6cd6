import React, { useEffect, useState } from "react";
import FrameContainerFull from "../components/layout/ContainerFluid";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { Stack, Typography } from "@mui/material";
import MemberShipItem from "../components/MemberShipItem";
import { LevelIcon, LevelName } from "../constants/Const";
import { commonStyle } from "../constants/themes";

export default function Point() {
  const { user } = useSelector((state: RootState) => state.auth);
  const [indexSelected, setIndex] = useState(0);
  const numLevel = Object.values(LevelIcon).length;

  useEffect(() => {
    setIndex(user?.level || 0);
  }, []);

  return (
    <FrameContainerFull title="Tích điểm" overrideStyle={styles.container}>
      <MemberShipItem />
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: "transparent",
    padding: 16,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
  selectionContainer: {
    width: "100%",
    alignItems: "center",
    justifyContent: "space-around",
    paddingBlock: 12,
    marginBlock: 8,
  },
};
