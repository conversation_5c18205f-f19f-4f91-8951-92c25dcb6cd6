import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { genPass } from "../../../genpass";
import { request } from "../../../utils/request";
import { clearItem, getItem, setItem } from "../../../utils/storage";
import { getAccessToken, getPhoneNumber, getUserInfo } from "zmp-sdk";
import { IUser, UserZalo } from "../../../types/user";
import { MockPhone, ColabStatus } from "../../../constants/Const";
import { ZMP_API_INFO } from "../../../constants/AppInfo";
import axios from "axios";
import { showToast, trackError, trackEvent } from "../../../utils/common";
import { StorageKeys } from "@/constants/StorageKeys";
import { AppEnv } from "@/config";

interface AuthState {
  user?: IUser;
  userZalo?: UserZalo;
  token?: string;
  status: "idle" | "loading" | "failed";
}

const initialState: AuthState = {
  user: undefined,
  userZalo: undefined,
  token: undefined,
  status: "idle",
};

const NullHeader = {
  headers: {
    Authorization: null,
  },
};

export const loginWithPhone = createAsyncThunk(
  "auth/authenticate",
  async (userPhoneZalo: string, { dispatch }) => {
    try {
      const password = genPass(userPhoneZalo);
      const response: any = await request(
        "post",
        `/api/auth/local`,
        {
          identifier: userPhoneZalo,
          password,
        },
        NullHeader
      );

      await setItem(StorageKeys.AccessToken, response.jwt);
      return response;
    } catch (error) {
      dispatch(register());
    }
  }
);

export const loginForWeb = createAsyncThunk(
  "auth/loginForWeb",
  async (
    data: {
      userPhoneZalo: string;
      password: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const { userPhoneZalo, password } = data;
      const response: any = await request(
        "post",
        `/api/auth/local`,
        {
          identifier: userPhoneZalo,
          password,
        },
        NullHeader
      );
      await setItem(StorageKeys.AccessToken, response.jwt);
      return response;
    } catch (error: any) {
      if (error.message === "Invalid identifier or password") {
        showToast({
          content: "Số điện thoại hoặc mật khẩu không đúng",
          type: "error",
        });
      }
      rejectWithValue(error);
    }
  }
);

export const loginWithZaloId = createAsyncThunk(
  "auth/loginWithZaloId",
  async (zaloId: string, { rejectWithValue }) => {
    try {
      const response: any = await request(
        "post",
        `/api/auth/local`,
        {
          zaloId,
          identifier: zaloId,
          password: zaloId,
        },
        NullHeader
      );
      await setItem(StorageKeys.AccessToken, response.jwt);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const fetchZaloPhone = async () => {
  const tokenPhone = await getPhoneNumber();
  const accessToken = await getAccessToken();
  const headers: any = {
    access_token: accessToken,
    code: tokenPhone.token,
    secret_key: import.meta.env.VITE_ZMP_SECRET_KEY,
  };
  if (AppEnv === "dev") {
    return MockPhone;
  }
  try {
    const response: any = await axios.get(ZMP_API_INFO, { headers });
    return response.data.data?.number;
  } catch (error) {
    console.log("🚀 ~ fetchZaloPhone ~ error:", error);
  }
};

export const register = createAsyncThunk(
  "auth/register",
  async (referCode?: string) => {
    try {
      const phone = await fetchZaloPhone();
      if (!phone) return;
      const password = genPass(phone);
      trackEvent("register info", {
        phone,
        password,
        referCode,
      });
      const response: any = await request(
        "post",
        `/api/auth/local/register`,
        {
          phone,
          password,
          referCode,
        },
        NullHeader
      );
      trackEvent("register", response);
      await setItem(StorageKeys.AccessToken, response.jwt);
      return response;
    } catch (error: any) {
      console.log("🚀 ~ register ~ error:", error);
      trackError("register", error);
      return { error: error.message };
    }
  }
);

export const registerForWeb = createAsyncThunk(
  "auth/registerForWeb",
  async (data: { phone: string; password: string; referCode: string }) => {
    try {
      const { phone, password, referCode } = data;

      const response: any = await request(
        "post",
        `/api/auth/local/register`,
        {
          phone,
          password,
          referCode: referCode.toLocaleUpperCase(),
        },
        NullHeader
      );
      return response;
    } catch (error: any) {
      if (error.message === "Phone is already taken") {
        showToast({
          content: "Số điện thoại đã được sử dụng",
          type: "error",
        });
      }
      if (error.message === "Email are already taken") {
        showToast({
          content: "Email đã được sử dụng",
          type: "error",
        });
      }
      if (error.message === "Refer code is invalid") {
        showToast({
          content: "Mã giới thiệu không hợp lệ",
          type: "error",
        });
      }
      // rejectWithValue(error);
    }
  }
);

export const getUser = createAsyncThunk("auth/getUser", async () => {
  const response: any = await request(
    "get",
    "/api/users/me?populate=fChildren&populate=fParent"
  );
  return response;
});

export const updateUser = createAsyncThunk(
  "auth/updateUser",
  async (data: any) => {
    const response: any = await request("put", `/api/users/me`, data);
    return response;
  }
);

export const updateMe = createAsyncThunk("auth/updateMe", async (data: any) => {
  const response: any = await request("put", `/api/users/me`, data);
  return response;
});

export const registerCollabration = createAsyncThunk(
  "users/registerCollabration",
  async (data: any) => {
    const response: any = await request(
      "put",
      `/api/users/register-collaborator`,
      {
        ...data,
        referCode: data?.referCode?.toLocaleUpperCase(),
      }
    );
    return response;
  }
);

export const loadTokenFromStorage = createAsyncThunk(
  "auth/loadTokenFromStorage",
  async () => {
    const token = await getItem(StorageKeys.AccessToken);
    return token;
  }
);

export const getMyParent = createAsyncThunk("auth/getMyParent", async () => {
  const response: any = await request("get", "/api/my-parent/");
  return response;
});

export const getUserZalo = createAsyncThunk("auth/getUserZalo", async () => {
  const response = await getUserInfo();
  return response.userInfo;
});

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      clearItem();
      sessionStorage.removeItem("userInfo");
      state.token = undefined;
      state.user = undefined;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginWithPhone.fulfilled, (state, action) => {
        state.token = action.payload.jwt;
        state.user = action.payload.user;
      })
      .addCase(loginWithZaloId.fulfilled, (state, action) => {
        console.log("🚀 ~ .addCase ~ action1:", action);
        state.token = action.payload.jwt;
        state.user = action.payload.user;
      })
      .addCase(loginForWeb.fulfilled, (state, action) => {
        console.log("🚀 ~ .addCase ~ action2:", action);
        state.token = action.payload?.jwt;
        state.user = action.payload?.user;
      })
      .addCase(getUserZalo.fulfilled, (state, action) => {
        state.userZalo = action.payload;
      })
      .addCase(register.fulfilled, (state, action) => {
        console.log("🚀 ~ .addCase ~ action3:", action);
        state.token = action.payload.jwt;
        state.user = action.payload.user;
      })
      .addCase(getUser.fulfilled, (state, action) => {
        state.user = action.payload;
        sessionStorage.setItem("userInfo", JSON.stringify(action.payload));
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.user = action.payload;
      })
      .addCase(loadTokenFromStorage.fulfilled, (state, action) => {
        state.token = action.payload;
      });
  },
});

export const { logout } = authSlice.actions;
export default authSlice.reducer;
