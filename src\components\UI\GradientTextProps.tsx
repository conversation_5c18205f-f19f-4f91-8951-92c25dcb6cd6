import { COLORS } from "@/constants/themes";
import React, { CSSProperties } from "react";

interface GradientTextProps {
  text: string;
  style?: CSSProperties;
}

const GradientText: React.FC<GradientTextProps> = ({ text, style = {} }) => {
  return (
    <span
      style={{
        background: "#000",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        WebkitTextStroke: ".2px #007AFF",
        ...style,
      }}
    >
      {text}
    </span>
  );
};

export default GradientText;
