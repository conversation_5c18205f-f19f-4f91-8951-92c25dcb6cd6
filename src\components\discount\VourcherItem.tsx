import React, { useEffect, useState } from "react";
import { IVoucher } from "../../types/voucher";
import {
  Box,
  Button,
  FormControlLabel,
  Radio,
  Stack,
  Typography,
} from "@mui/material";
import { VoucherTypeDesc, VoucherTypeLabel } from "./checkVoucherType";
import { COLORS } from "../../constants/themes";
import { formatPrice } from "../../utils/formatPrice";
import dayjs from "dayjs";
import { Icon } from "../../constants/Assets";
import { Router } from "../../constants/Route";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../redux/store";
import { setCurVoucher } from "../../redux/slices/voucher/voucherSlice";

export default function VoucherItem({
  item,
  onSelectVoucher,
  isShowMatchPoint,
  onNavigateToDetail,
  isChecked,
  isDetail,
  isMyVoucher,
  onApplyClick,
  myVoucherList,
  isChooseVoucherCart,
}: {
  item: IVoucher;
  onSelectVoucher?: () => void;
  isShowMatchPoint?: boolean;
  onNavigateToDetail?: () => void;
  isChecked?: boolean;
  isDetail?: boolean;
  isMyVoucher?: boolean;
  onApplyClick?: (item) => void;
  myVoucherList?: IVoucher[];
  isChooseVoucherCart?: boolean;
}) {
  const dispatch = useDispatch<AppDispatch>();
  const [isItemChecked, setItemChecked] = useState(isChecked || false);
  const navigate = useNavigate();

  useEffect(() => {
    setItemChecked(isChecked || false);
  }, [isChecked]);

  const onItemAction = () => {
    if (onNavigateToDetail) {
      onNavigateToDetail();
    } else if (onSelectVoucher) {
      onSelectVoucher();
    }
  };

  const onClickVoucher = (event) => {
    event.stopPropagation();
    if (isChooseVoucherCart) {
      onSelectVoucher?.();
    } else {
      if (!isDetail) {
        if (!isMyVoucher) {
          if (isHaveInMyVoucher) return;
          if (onApplyClick) {
            onApplyClick(item);
          }
        } else {
          dispatch(setCurVoucher(item));
          navigate(`${Router.product}`);
        }
      }
    }
  };

  const isHaveInMyVoucher = myVoucherList?.find(
    (voucher) => voucher.id === item.id
  );

  return (
    <Box className="wrap" key={item.id} onClick={onItemAction}>
      <Box className="coupon">
        <Stack sx={styles.leftContainer} p={1} className="coupon-left">
          <Stack className="content" sx={styles.leftContent}>
            <Typography style={styles.zaloText}>Zalo</Typography>
            <VoucherTypeLabel {...item} />
          </Stack>
        </Stack>
        <Stack className="coupon-con" direction="row" p={"3%"}>
          <Stack
            className="content"
            sx={styles.rightContainer}
            direction="row"
            justifyContent={"space-between"}
            gap={2}
          >
            <Stack sx={styles.rightTop} direction="column">
              <Box style={styles.typeText}>{item.code}</Box>

              <Box>
                <VoucherTypeDesc {...item} />
                <Typography style={{ fontSize: 12 }}>
                  {item.minPrice
                    ? "Đơn tối thiểu: " + formatPrice(item.minPrice)
                    : ""}
                </Typography>
                <Typography style={{ fontSize: 12 }}>
                  HSD:{" "}
                  {item.expiredDate
                    ? dayjs(item.expiredDate).format("DD/MM/YYYY")
                    : "Không giới hạn"}
                </Typography>
                {isShowMatchPoint && (
                  <Typography style={{ fontSize: 12 }}>
                    Điểm đổi: {item.matchPoint} điểm
                  </Typography>
                )}
              </Box>
            </Stack>
            <Stack
              direction={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              gap={"10px"}
            >
              {!isDetail && !isChooseVoucherCart && (
                <Box>
                  {!isMyVoucher ? (
                    <Button
                      variant="contained"
                      size="small"
                      onClick={onClickVoucher}
                      className={`btn-voucher ${
                        isHaveInMyVoucher ? "disabled" : ""
                      }`}
                      style={{
                        ...styles.buttonVoucher,
                        background: isHaveInMyVoucher
                          ? COLORS.accent2
                          : COLORS.primary,
                        color: isHaveInMyVoucher ? COLORS.primary1 : "#fff",
                      }}
                    >
                      {isHaveInMyVoucher
                        ? "Đã lưu"
                        : item.matchPoint
                        ? "Đổi Voucher"
                        : "Lưu"}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      onClick={onClickVoucher}
                      style={{
                        ...styles.buttonVoucher,
                        background: COLORS.primary1,
                        color: "white",
                      }}
                    >
                      Dùng ngay
                    </Button>
                  )}
                </Box>
              )}
              {isChooseVoucherCart && (
                <FormControlLabel
                  value={item.id.toString()}
                  control={
                    <Radio
                      checkedIcon={<img src={Icon.small_check} width={24} />}
                    />
                  }
                  label=""
                  labelPlacement="start"
                  onClick={onClickVoucher}
                  checked={isItemChecked}
                />
              )}
            </Stack>
          </Stack>
        </Stack>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  leftContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  leftContent: {
    textAlign: "center",
    justifyContent: "center",
    alignItems: "center",
    color: "white",
    gap: 1,
    fontSize: 10,
    height: "100%",
    width: "100%",
    background: COLORS.primary,
    borderRadius: 2,
  },
  zaloText: {
    width: 40,
    height: 40,
    background: "white",
    color: COLORS.primary1,
    display: "flex",
    alignItems: "center",
    borderRadius: "50%",
    justifyContent: "center",
    margin: "0px auto",
    fontSize: 10,
  },
  rightContainer: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    fontSize: 12,
  },
  rightTop: {
    width: "100%",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  typeText: {
    padding: "4px 8px",
    background: COLORS.primary2,
    textAlign: "center",
    color: "white",
    borderRadius: 4,
    fontSize: 12,
    marginBottom: 5,
  },
  buttonVoucher: {
    fontSize: 13,
    fontWeight: 500,
    lineHeight: "16px",
    borderRadius: 4,
    padding: "4px 8px",
    height: 50,
    width: 70,
    boxShadow: "none",
  },
};
