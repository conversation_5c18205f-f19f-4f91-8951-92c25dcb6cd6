import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";

import { Controller, useForm } from "react-hook-form";
import store, { AppDispatch, RootState } from "../../redux/store";
import {
  createAddress,
  getAddressList,
  getDistrict,
  getProvince,
  getWard,
} from "../../redux/slices/address/addressSlice";
import { useDispatch, useSelector } from "react-redux";
import { COLORS } from "@/constants/themes";

const defaultValues = {
  userName: "",
  phone: "",
  province: "",
  district: "",
  ward: "",
  address: "",
};
const PopupAddAddress: React.FC<{ size?: "small" | "medium" | "large" }> = ({
  size,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { province } = useSelector((state: RootState) => state.address);

  const [open, setOpen] = React.useState(false);
  const [ward, setWard] = React.useState([]);
  const [district, setDistrict] = React.useState([]);

  const { control, handleSubmit, register, watch, reset } = useForm({
    defaultValues,
  });

  const watchDistrict = watch("district");

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    reset(defaultValues);
  };

  const onSubmit = async (data) => {
    const res = await store.dispatch(createAddress(data));
    if (res) {
      await store.dispatch(getAddressList());
      handleClose();
    }
  };

  return (
    <>
      <Stack direction={"row"} justifyContent={"center"}>
        <Button
          onClick={handleClickOpen}
          style={{
            background: COLORS.primary,
            color: "#fff",
            borderRadius: 99,
            width: "fit-content",
            padding: size === "small" ? "5px 20px" : "10px 60px",
          }}
          size={size ? size : "large"}
          startIcon={<ControlPointIcon />}
          variant="contained"
        >
          Thêm địa chỉ
        </Button>
      </Stack>
      <Dialog
        fullWidth
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogTitle id="alert-dialog-title">Địa chỉ</DialogTitle>
          <DialogContent>
            <Stack gap={3}>
              <Controller
                name="userName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    {...register("userName")}
                    fullWidth
                    required
                    id="standard-required"
                    label="Họ và tên"
                    variant="standard"
                  />
                )}
              ></Controller>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    required
                    label="Số điện thoại"
                    variant="standard"
                  />
                )}
              ></Controller>

              <Stack direction={"row"} gap={2}>
                <Controller
                  name="province"
                  control={control}
                  render={({ field }) => (
                    <FormControl variant="standard" fullWidth>
                      <InputLabel id="province-label">Tỉnh</InputLabel>
                      <Select
                        {...register("province")}
                        fullWidth
                        required
                        labelId="province-label"
                        id="province"
                        label="Tỉnh"
                        onFocus={() => {
                          dispatch(getProvince());
                        }}
                      >
                        {province?.map((item: any, index) => (
                          <MenuItem key={`province-${index}`} value={item.code}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                ></Controller>

                <Controller
                  name="district"
                  control={control}
                  render={({ field }) => (
                    <FormControl variant="standard" fullWidth>
                      <InputLabel id="district-label">Quận (Huyện)</InputLabel>
                      <Select
                        {...field}
                        {...register("district")}
                        fullWidth
                        required
                        labelId="district-label"
                        id="district"
                        label="Quận (Huyện)"
                        onFocus={async () => {
                          if (!watch("province")) return;
                          const res = await dispatch(
                            getDistrict({ parent_code: watch("province") })
                          );
                          if (
                            Array.isArray(res.payload) &&
                            res.payload.length > 0
                          ) {
                            setDistrict(res.payload as any);
                          }
                        }}
                      >
                        {district?.map((item: any, index) => (
                          <MenuItem key={`district-${index}`} value={item.code}>
                            {item.name_with_type}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                ></Controller>
              </Stack>
              <Controller
                name="ward"
                control={control}
                render={({ field }) => (
                  <FormControl variant="standard" fullWidth>
                    <InputLabel id="ward-label">Xã (Phường)</InputLabel>
                    <Select
                      {...field}
                      {...register("ward")}
                      fullWidth
                      required
                      labelId="ward-label"
                      id="ward"
                      label="Xã (Phường)"
                      onFocus={async () => {
                        if (!watchDistrict) return;
                        const res = await dispatch(
                          getWard({ parent_code: watchDistrict })
                        );
                        if (
                          Array.isArray(res.payload) &&
                          res.payload.length > 0
                        ) {
                          setWard(res.payload as any);
                        }
                      }}
                    >
                      {ward?.map((item: any, index) => (
                        <MenuItem key={`ward-${index}`} value={item.code}>
                          {item.name_with_type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              ></Controller>

              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    required
                    id="standard-required"
                    label="Số nhà, đường"
                    defaultValue=""
                    variant="standard"
                  />
                )}
              ></Controller>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Hủy bỏ</Button>
            <Button type="submit" autoFocus>
              Thêm địa chỉ
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};
export default PopupAddAddress;
