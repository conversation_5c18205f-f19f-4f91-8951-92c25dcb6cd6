import React, { useEffect, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { getUser, updateMe } from "../../redux/slices/authen/authSlice";
import { showToast } from "../../utils/common";
import { Box, Stack, TextField } from "@mui/material";
import { COLORS } from "../../constants/themes";
import { getBank } from "../../redux/slices/address/addressSlice";

type FormData = {
  cccd?: string;
};

export default function PopupCccd() {
  const [open, setOpen] = useState(false);
  const user = useSelector((state: RootState) => state.auth.user);
  const [data, setData] = useState<FormData>({
    cccd: user?.cccd,
  });

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    dispatch(getBank());
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setData((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };

  function checkValues() {
    let errorMess = "";
    if (!data.cccd) {
      errorMess = "Bạn cần nhập số căn cước công dân";
    }
    if (errorMess) {
      showToast({
        content: errorMess,
        type: "error",
      });
      return false;
    }
    return true;
  }

  const onSubmit = async () => {
    const validated = checkValues();
    if (!validated) return;

    const res = await dispatch(updateMe({ cccd: data.cccd })).unwrap();
    if (res) {
      setOpen(false);
      await dispatch(getUser());
      showToast({
        content: "Cập nhật thông tin thành công",
        type: "success",
      });
    } else {
      showToast({
        content: "Quá trình cập nhật lỗi. Vui lòng thử lại",
        type: "error",
      });
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  return (
    <Box>
      <Button onClick={handleClickOpen} style={styles.editBtn}>
        {user?.cccd ? "Sửa số căn cước công dân" : "Thêm số căn cước công dân"}
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent>
          {open && (
            <Stack gap={4}>
              <TextField
                id="outlined-required"
                label="Số căn cước công dân"
                onChange={handleInputChange}
                name="cccd"
                value={data?.cccd || ""}
              />
            </Stack>
          )}
        </DialogContent>
        <DialogActions style={{ justifyContent: "center" }}>
          <Button type="submit" style={styles.editBtn} onClick={onSubmit}>
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  editBtn: {
    background: COLORS.primary,
    fontWeight: 400,
    color: "#fff",
    minWidth: "220px",
    marginBlock: 8,
  },
};
