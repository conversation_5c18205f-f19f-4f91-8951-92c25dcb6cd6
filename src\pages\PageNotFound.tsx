import LayoutAccountPage from "@/components/layout/LayoutAccountPage";
import { APP_NAME } from "@/constants/AppInfo";
import { Icon } from "@/constants/Assets";
import { DEFAULT_REFER_CODE } from "@/constants/Const";
import { COLORS } from "@/constants/themes";
import { useAlert } from "@/redux/slices/alert/useAlert";
import {
  getUserZalo,
  register,
  registerCollabration,
} from "@/redux/slices/authen/authSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { mapError } from "@/utils/common";
import {
  Button,
  CircularProgress,
  Container,
  Stack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getRouteParams } from "zmp-sdk";

const PageNotFound = () => {
  const dispatch = useDispatch<AppDispatch>();
  const previousPage = useSelector(
    (state: RootState) => state.common.previousPage
  );
  const [loading, setLoading] = useState(false);
  const { showAlert } = useAlert();
  const { refCode } = getRouteParams();
  const navigate = useNavigate();

  const onClickRegister = async () => {
    setLoading(true);
    const referCode = refCode || DEFAULT_REFER_CODE;
    const res = await dispatch(register(referCode)).unwrap();
    if (res.jwt) {
      showAlert({
        icon: Icon.check,
        title: "Kích hoạt tài khoản thành công",
      });
      // await dispatch(
      //   registerCollabration({
      //     referCode: referCode,
      //   })
      // );
      await dispatch(getUserZalo());
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
    if (previousPage) {
      navigate(previousPage);
    }
    navigate(-1);
  };

  return (
    <LayoutAccountPage>
      <Container>
        <Stack
          justifyContent={"center"}
          alignItems={"center"}
          padding={2}
          style={{
            borderRadius: 20,
            background: "#fff",
            padding: "15px",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
          }}
          height={500}
        >
          {loading ? (
            <CircularProgress />
          ) : (
            <>
              <Button
                style={{
                  background: COLORS.primary,
                  fontWeight: 700,
                }}
                variant="contained"
                onClick={onClickRegister}
              >
                Kích hoạt tài khoản
              </Button>
              <Typography variant="body2" textAlign={"center"} pt={2}>
                Bằng việc bấm "Kích hoạt tài khoản", chúng tôi hiểu rằng bạn đã
                đồng ý với điều khoản của {APP_NAME}.
              </Typography>
            </>
          )}
        </Stack>
      </Container>
    </LayoutAccountPage>
  );
};

export default PageNotFound;
