import { Icon } from "@/constants/Assets";
import styles from "../css/styles.module.css";
import React, { useMemo } from "react";
import { Button, Stack, Box } from "@mui/material";
import { ICart } from "@/types/cart";
import { IAddress } from "@/types/address";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { showToast } from "@/utils/common";
import { formatPrice } from "@/utils/formatPrice";
import { COLORS } from "@/constants/themes";

interface IProps {
  numOfProducts: number;
  currentAddress?: IAddress;
  finalPrice: number;
  handlerCreateOrder: () => void;
}

export default function BottomVerify({
  numOfProducts,
  currentAddress,
  finalPrice,
  handlerCreateOrder,
}: IProps) {
  const { showAlert } = useAlert();

  const onClickOrderButton = () => {
    showAlert({
      icon: Icon.check,
      title: "<PERSON><PERSON><PERSON> nhận đặt hàng",
      content: "Bạn có chắc muốn đặt hàng không?",
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: () => {
            if (!currentAddress) {
              showToast({
                content: "Bạn chưa có thông tin địa chỉ",
                type: "error",
              });
              return;
            }
            handlerCreateOrder();
          },
        },
      ],
    });
  };

  return (
    <Box className={styles.bottomPayElement}>
      <Stack
        sx={{ minWidth: "100%" }}
        style={{ fontSize: 14 }}
        direction="row"
        alignItems="center"
        justifyContent="space-around"
      >
        <Stack gap={0.5}>
          <span>{numOfProducts} sản phẩm</span>
          <span style={{ color: "#555555", fontWeight: 700 }}>
            Tổng thanh toán
          </span>
          <span style={{ color: "#1D1D5E", fontWeight: 700, fontSize: 20 }}>
            {formatPrice(finalPrice || 0, "đ")}
          </span>
        </Stack>
        <Button
          style={{
            background: COLORS.primary,
            color: "#fff",
            paddingInline: 18,
            borderRadius: 99,
            fontSize: 16,
          }}
          onClick={onClickOrderButton}
        >
          Đặt hàng
        </Button>
      </Stack>
    </Box>
  );
}
