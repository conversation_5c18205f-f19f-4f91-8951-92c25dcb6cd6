import { COLORS } from "@/constants/themes";
import { RootState } from "@/redux/store";
import { Box, Button } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

const Receiver = {
  name: "MEGAGROUP",
  accountNumber: "**********",
  bank: "TCB",
};

function PopupQrTransfer({ amount, handleContinue }) {
  const [value, setValue] = useState(amount);
  const [qrUrl, setQrUrl] = useState("");
  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );

  useEffect(() => {
    setValue(amount);
  }, [amount]);

  useEffect(() => {
    if (value) {
      const transferMessage = `${APP_NAME} đặt cọc ${value}`;
      setQrUrl(
        `https://apiqr.web2m.com/api/generate/${Receiver.bank}/${
          Receiver.accountNumber
        }/${Receiver.name.replace(
          / /g,
          "%20"
        )}?amount=${value}&memo=${transferMessage}&is_mask=0&bg=0`
      );
    }
  }, [value]);

  return (
    <Box>
      <img src={qrUrl} width={"100%"} height="auto" />
      <Box
        sx={{
          justifyContent: "center",
          display: "flex",
          marginTop: 2,
          marginBottom: 2,
        }}
      >
        <Button
          onClick={() => {
            handleContinue();
          }}
          type="submit"
          style={{
            background: COLORS.primary,
            color: "#fff",
            paddingInline: 18,
            fontSize: 12,
          }}
        >
          Tiếp tục
        </Button>
      </Box>
    </Box>
  );
}

export default PopupQrTransfer;
