import { getStorage, setStorage, clearStorage } from "zmp-sdk/apis";
import { Platform } from "../config";
import { parseSafe } from "./common";

export const setItem = (key: string, value: any) => {
  return Platform === "web" ? setItemWeb(key, value) : setItemZalo(key, value);
};

export const getItem = async (key: string) => {
  return Platform === "web" ? getItemWeb(key) : getItemZalo(key);
};

export const clearItem = async () => {
  return Platform === "web" ? clearItemWeb() : clearItemZalo();
};

///////////////
const setItemZalo = (key: string, value: any) => {
  return setStorage({ data: { [key]: value } });
};

const getItemZalo = async (key: string) => {
  return (await getStorage({ keys: [key] }))[key];
};

const clearItemZalo = async () => {
  return await clearStorage({});
};

////////////////
const setItemWeb = (key: string, value: any) => {
  return localStorage.setItem(
    key,
    typeof value === "object" ? JSON.stringify(value) : value
  );
};

const getItemWeb = async (key: string) => {
  return parseSafe(localStorage.getItem(key));
};

const clearItemWeb = async () => {
  return localStorage.clear();
};
