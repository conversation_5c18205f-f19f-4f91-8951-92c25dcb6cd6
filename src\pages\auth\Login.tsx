import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>ack,
  TextField,
} from "@mui/material";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import Page from "zmp-ui/page";
import { AppDispatch, RootState } from "../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { loginForWeb } from "../../redux/slices/authen/authSlice";
import { useNavigate } from "../../utils/component-util";
import { Router } from "../../constants/Route";
import { Icon } from "@/constants/Assets";
const TOP_NAV_HEIGHT = 64;

export default function Login() {
  const { control, handleSubmit, register, watch, reset } = useForm({});
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { token } = useSelector((state: RootState) => state.auth);

  if (token) navigate("/");

  const onSubmit = async (data) => {
    const res = await dispatch(
      loginForWeb({
        userPhoneZalo: data.userName,
        password: data.phone,
      })
    ).unwrap();
    if (res) {
      navigate("/");
    }
  };
  return (
    <Page>
      <Box component="header">
        <Container maxWidth="lg">
          <Stack direction="row" spacing={2} sx={{ height: TOP_NAV_HEIGHT }}>
            <Stack
              alignItems="center"
              direction="row"
              display="inline-flex"
              spacing={1}
              sx={{ textDecoration: "none" }}
            >
              <Box
                sx={{
                  display: "inline-flex",
                  height: 24,
                  width: 24,
                }}
              >
                <Container>
                  <Stack direction="row" gap={2}>
                    <img
                      style={{ borderRadius: "50%" }}
                      width={150}
                      height={52}
                      src={Icon.logo}
                    />
                  </Stack>
                </Container>
              </Box>
            </Stack>
          </Stack>
          <Stack direction="row" gap={3} justifyContent="center" pt={4}>
            <Stack
              direction="row"
              alignItems="center"
              gap={3}
              justifyContent={"center"}
            >
              <Stack alignItems={"center"}>
                <span style={{ fontWeight: 500, fontSize: 24 }}>Đăng nhập</span>
                <span style={{ color: "#969595" }}>
                  Bạn chưa có tài khoản?{" "}
                  <Button onClick={() => navigate(Router.register)}>
                    Đăng ký
                  </Button>
                </span>
              </Stack>
            </Stack>
          </Stack>
        </Container>
      </Box>

      <Box
        sx={{
          alignItems: "center",
          display: "flex",
          justifyContent: "center",
          flex: "1 1 auto",
        }}
      >
        <Container maxWidth="sm">
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogContent>
              <Stack gap={3}>
                <Controller
                  name="userName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      {...register("userName")}
                      fullWidth
                      required
                      id="standard-required"
                      label="Số điện thoại"
                      variant="standard"
                    />
                  )}
                ></Controller>
                <Controller
                  name="phone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      {...register("phone")}
                      type="password"
                      fullWidth
                      required
                      label="Mật khẩu"
                      variant="standard"
                    />
                  )}
                ></Controller>
              </Stack>
            </DialogContent>

            <DialogActions>
              <Button
                type="submit"
                style={{
                  color: "#fff",
                  width: "100%",
                  height: 55,
                  fontSize: "16px",
                  fontWeight: 400,
                  lineHeight: "19.36px",
                  border: "1px solid transparent",
                  borderRadius: 10,
                  padding: "2px",
                }}
                color="primary"
                variant="contained"
              >
                Đăng nhập
              </Button>
            </DialogActions>
          </form>
          {/* <Stack alignItems="center">Quên mật khẩu?</Stack> */}
        </Container>
      </Box>
    </Page>
  );
}
