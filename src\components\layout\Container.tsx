import { Box, Container, Grid, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "zmp-ui";
import styles from "../../css/styles.module.css";
import { COLORS } from "@/constants/themes";

export default function FrameContainer({
  children,
  title,
  style,
}: {
  children: ReactNode;
  title: string;
  style?: React.CSSProperties;
}) {
  const [positionCss, setPositionCss] = useState({});
  const handleScroll = (e) => {
    const bottom =
      e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };

  return (
    <Page style={{ backgroundColor: "#F2F3F5" }}>
      <Header
        style={{
          background: COLORS.colorHeader,
        }}
        textColor="#fff"
        title={title}
      />
      <Box className={styles.pageContent}>
        <Container
          onScroll={handleScroll}
          style={{ paddingBottom: 64, backgroundColor: "#F2F3F5", ...style }}
        >
          <Stack paddingBlock={2}>{children}</Stack>
        </Container>
      </Box>
    </Page>
  );
}
