import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProduct, IProductSearchParam } from "../../../types/product";
import {
  convertListPoductStrapi,
  convertStrapiDocuments,
} from "../../../utils/common";

interface productState {
  productList: IProduct[];
  productBestSellerList: IProduct[];
  productRecommendList: IProduct[];
  newProductList: IProduct[];
  isLoading: boolean;
  error: string | null;
}

const initialState: productState = {
  productBestSellerList: [],
  productRecommendList: [],
  newProductList: [],
  productList: [],
  isLoading: true,
  error: null,
};

export const getProductList = createAsyncThunk(
  "productList/getProductList",
  async () => {
    const response: any = await request("get", "/api/products", {
      populate: ["image", "product_cats", "supplier"],
    });
    return response;
  }
);

export const getProductRecommendList = createAsyncThunk(
  "productList/getProductRecommendList",
  async () => {
    const response: any = await request("get", "/api/products", {
      populate: ["image", "product_cats", "supplier"],
      filters: {
        isRecommend: true,
      },
    });
    return response;
  }
);

export const getProductBestSellerList = createAsyncThunk(
  "productList/getProductBestSellerList",
  async () => {
    const response: any = await request("get", "/api/products", {
      populate: ["image", "product_cats", "supplier"],
      filters: {
        isBestSeller: true,
      },
    });
    return response;
  }
);

export const getNewProductList = createAsyncThunk(
  "productList/getNewProductList",
  async () => {
    const response: any = await request("get", "/api/products?populate=image", {
      populate: ["image", "product_cats", "supplier"],
      pagination: {
        limit: 10,
      },
      sort: ["id:desc"],
    });
    return response;
  }
);

export const getProductListByCategory = createAsyncThunk(
  "productList/getProductListByCategory",
  async (payloadSearchProduct: IProductSearchParam) => {
    let url = "/api/list-product?";
    if (payloadSearchProduct.categoryId) {
      url += `&filters[category]=${payloadSearchProduct.categoryId}`;
    }
    if (payloadSearchProduct.searchText) {
      url += `&filters[search]=${payloadSearchProduct.searchText}`;
    }
    const response: any = await request("get", url);

    return response;
  }
);

export const getProductListLazy = createAsyncThunk(
  "productList/getProductListLazy",
  async (payloadSearchProduct: IProductSearchParam) => {
    let url = "/api/list-product?";
    if (payloadSearchProduct.categoryId) {
      url += `&filters[category]=${payloadSearchProduct.categoryId}`;
    }
    if (payloadSearchProduct.searchText) {
      url += `&filters[search]=${payloadSearchProduct.searchText}`;
    }
    if (payloadSearchProduct.page) {
      url += `&pagination[page]=${payloadSearchProduct.page}`;
    }
    const response: any = await request("get", url);

    return response;
  }
);

export const getProductDetail = createAsyncThunk(
  "productList/getProductDetail",
  async (productId: string) => {
    const response: any = await request("get", `/api/products/${productId}`, {
      populate: ["image", "product_cats", "supplier"],
    });
    return response.data;
  }
);

const productSlice = createSlice({
  name: "productList",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getProductList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productList = convertStrapiDocuments(payload.data);
          state.isLoading = false;
        }
      )
      .addCase(getProductList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProductListByCategory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductListByCategory.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productList = convertListPoductStrapi(payload.data);
          state.isLoading = false;
        }
      )
      .addCase(getProductListByCategory.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProductRecommendList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductRecommendList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productRecommendList = convertStrapiDocuments(payload.data);
          state.isLoading = false;
        }
      )
      .addCase(getProductRecommendList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProductBestSellerList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductBestSellerList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productBestSellerList = convertStrapiDocuments(payload.data);
          state.isLoading = false;
        }
      )
      .addCase(getProductBestSellerList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getNewProductList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getNewProductList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.newProductList = convertStrapiDocuments(payload.data);
          state.isLoading = false;
        }
      )
      .addCase(getNewProductList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProductListLazy.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductListLazy.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productList.push(...convertListPoductStrapi(payload.data));
          state.isLoading = false;
        }
      )
      .addCase(getProductListLazy.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});
export default productSlice.reducer;
