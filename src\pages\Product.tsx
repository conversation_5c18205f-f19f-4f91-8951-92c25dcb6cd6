import { Box, CircularProgress, Stack } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import Categories from "../components/menu/Categories";
import ProductTwoColumn from "../components/products/ProductTwoColumn";
import { useSearchParams } from "react-router-dom";
import LayoutProductPage from "@/components/layout/LayoutProductPage";
import { debounce } from "lodash";
import store, { RootState } from "@/redux/store";
import {
  getProductListByCategory,
  getProductListLazy,
} from "@/redux/slices/product/productListSlice";
import { IProductSearchParam } from "@/types/product";
import { showToast } from "@/utils/common";
import InfiniteScroll from "react-infinite-scroll-component";
import { useSelector } from "react-redux";
import { Platform } from "@/config";

export default function Product() {
  const [searchParams] = useSearchParams();
  const categoryIdStr = searchParams.get("categoryId");
  const categoryIdParam = categoryIdStr ? parseInt(categoryIdStr) : null;
  const name = searchParams.get("name");

  const { productList } = useSelector((state: RootState) => state.productList);

  const [isLoading, setIsLoading] = useState(true);
  const [valueSearchText, setValueSearchText] = useState<string | null>(name);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(
    categoryIdParam
  );
  const [page, setPage] = useState(2);
  const [hasMore, setHasMore] = useState(true);

  const handleCategoryChange = (categoryId: number): void => {
    setSelectedCategoryId(categoryId);
    setIsLoading(true);
    getListProduct(valueSearchText, categoryId);
  };

  const getListProduct = async (valueSearchText, categoryId) => {
    setPage(2);
    const payload: IProductSearchParam = {
      categoryId,
      searchText: valueSearchText,
    };
    try {
      const response = await store.dispatch(getProductListByCategory(payload));
      setHasMore(
        response?.payload?.meta?.total > response?.payload?.data?.length
      );
    } catch (error) {
      showToast({
        content: error.message ?? "Xuất hiện lỗi trong quá trình lấy dữ liệu",
        type: "error",
      });
    }
    setIsLoading(false);
  };

  const debounceFn = useCallback(debounce(getListProduct, 1000), []);

  const changeValueSearch = (value: string) => {
    setValueSearchText(value);
    setIsLoading(true);
    debounceFn(value, selectedCategoryId);
  };

  useEffect(() => {
    getListProduct(valueSearchText, selectedCategoryId);
  }, []);

  const getListProductLazy = async () => {
    setPage((prevPage) => prevPage + 1);
    const payload: IProductSearchParam = {
      categoryId: selectedCategoryId,
      searchText: valueSearchText,
      page: page,
    };
    try {
      const response = await store.dispatch(getProductListLazy(payload));
      setHasMore(response?.payload?.data?.length > 0);
    } catch (error) {
      showToast({
        content: error.message ?? "Xuất hiện lỗi trong quá trình lấy dữ liệu",
        type: "error",
      });
    }
    setIsLoading(false);
  };

  return (
    <LayoutProductPage
      valueSearchText={valueSearchText}
      onChangeValueSearch={changeValueSearch}
    >
      <Categories
        onChange={handleCategoryChange}
        categoryId={selectedCategoryId}
      />
      {isLoading ? (
        <Stack
          marginBlock={"18px"}
          justifyContent={"center"}
          alignItems={"center"}
        >
          <CircularProgress />
        </Stack>
      ) : (
        <InfiniteScroll
          dataLength={productList?.length}
          next={getListProductLazy}
          loader={<CircularProgress />}
          hasMore={hasMore}
          height={Platform === "web" ? "75vh" : "69vh"}
          style={{ paddingInline: "10px" }}
        >
          <ProductTwoColumn productList={productList} />
        </InfiniteScroll>
        // <Box style={{ marginBlock: 18, paddingBottom: 64 }}>
        //   <ProductTwoColumn categoryId={selectedCategoryId} />
        // </Box>
      )}
    </LayoutProductPage>
  );
}
