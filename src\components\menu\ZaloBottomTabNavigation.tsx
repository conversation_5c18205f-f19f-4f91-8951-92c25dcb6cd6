import { Badge, Stack } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { BottomNavigation } from "zmp-ui";
import { useCart } from "../../hooks/useCart";
import { COLORS } from "../../constants/themes";
import { NO_BOTTOM_NAVIGATION_PAGES } from "../../constants/Const";
import { BottomTabs } from "./BottomTabs";
import { openChat } from "@/utils/openChat";

export default function ZaloBottomTabNavigation() {
  const { cart } = useCart();
  const [activeTab, setActiveTab] = useState("chat");
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    setActiveTab(location.pathname);
  }, [location]);

  const totalProduct = useMemo(() => {
    if (!Array.isArray(cart?.items) || !cart?.items.length) return 0;
    return cart?.items.reduce((acc, item) => acc + item.quantity, 0);
  }, [cart]);

  const noBottomNav = useMemo(() => {
    return NO_BOTTOM_NAVIGATION_PAGES.some((path) =>
      location.pathname.includes(path)
    );
  }, [location]);

  if (noBottomNav) {
    return <></>;
  }

  return (
    <Stack style={{ marginBottom: 49, display: "initial" }}>
      <BottomNavigation
        fixed
        activeKey={activeTab}
        // onChange={(key) => setActiveTab(key)}
      >
        {BottomTabs.map((item) => (
          <BottomNavigation.Item
            key={item.value}
            label={item.label}
            icon={
              item.value === "/cart" && totalProduct ? (
                <Badge
                  badgeContent={totalProduct}
                  color="error"
                  sx={{
                    span: {
                      height: "18px",
                      minWidth: "18px",
                    },
                  }}
                >
                  {item.icon(COLORS.default)}
                </Badge>
              ) : (
                item.icon(COLORS.default)
              )
            }
            style={
              activeTab === item.value
                ? { color: COLORS.primaryActive }
                : { color: COLORS.default }
            }
            activeIcon={item.icon(COLORS.primaryActive)}
            onClick={(e) => {
              if (item.value === "contact") {
                e.preventDefault();
                openChat();
              } else {
                navigate(item.value);
              }
            }}
          />
        ))}
      </BottomNavigation>
    </Stack>
  );
}
