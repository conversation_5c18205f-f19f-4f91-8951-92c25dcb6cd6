import React from "react";
import { Icon } from "@/constants/Assets";
import { Router } from "@/constants/Route";
import { COLORS, commonStyle } from "@/constants/themes";
import { useNavigate } from "@/utils/component-util";
import { Box, Grid, Stack, Typography } from "@mui/material";

export default function CollabMenuTool() {
  const navigate = useNavigate();
  const ToolItems = [
    // {
    //   icon: Icon.icon_hh,
    //   title: "Hoa hồng",
    //   link: Router.collab.index + "/" + Router.collab.commission,
    // },
    {
      icon: Icon.icon_dh,
      title: "Đ<PERSON>n hàng",
      link: Router.collab.index + "/" + Router.collab.collabhistory.index,
    },
    {
      icon: Icon.icon_ht,
      title: "Đội nhóm",
      link: Router.collab.index + "/" + Router.collab.members,
    },
    {
      icon: Icon.icon_dstv,
      title: "<PERSON><PERSON><PERSON> sách",
      link: Router.profile.index + "/" + Router.profile.policy.index,
    },
    // {
    //   icon: Icon.icon_withdraw,
    //   title: "L<PERSON>ch sử",
    //   link: Router.collab.index + "/" + Router.collab.withdrawal,
    // },
    // {
    //   icon: Icon.icon_share,
    //   title: "Link chia sẻ",
    //   link: Router.refer.index,
    // },
  ];
  return (
    <Box
      backgroundColor={"#fff"}
      marginBlock={"15px"}
      padding={"15px"}
      style={{ ...commonStyle.shadowBorder }}
    >
      <Typography fontSize={16} color={COLORS.blue} fontWeight={700}>
        Công cụ
      </Typography>
      <Grid
        container
        display={"flex"}
        justifyContent={"space-between"}
        marginTop={"5px"}
        spacing={2}
      >
        {ToolItems.map((tool, index) => (
          <Grid item key={String(index)} xs={4}>
            <Stack
              item
              direction="column"
              gap={1}
              alignItems={"center"}
              onClick={() => {
                navigate(tool.link);
              }}
            >
              <img width={40} height={40} src={tool.icon} alt="" />
              <Typography
                textAlign={"center"}
                fontSize={14}
                // color={"#1D1D5E"}
                fontWeight={700}
              >
                {tool.title}
              </Typography>
            </Stack>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
