import React, { useEffect, useState } from "react";
import { Box, Button, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";

import { PaymentMethods } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { Icon } from "@/constants/Assets";
import { useCart } from "@/hooks/useCart";
import { Link, useNavigate } from "react-router-dom";

import { IUser } from "@/types/user";
import { IProduct } from "@/types/product";
import { formatPrice } from "@/utils/formatPrice";
import { AppDispatch, RootState } from "@/redux/store";
import FrameContainer from "@/components/layout/Container";
import { convertStrapiDocument, showToast } from "@/utils/common";
import { request } from "@/utils/request";

interface ILicensePackage {
  finalPrice: number;
  paymentMethod: number;
  orderData: {
    items: Array<{ quantity: number; product: IProduct }>;
    ship: number;
  };
  receiver: IUser | undefined;
}

export default function UpgradeAccount() {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [licensePackage, setLicensePackage] = useState<ILicensePackage>();
  const user = useSelector((state: RootState) => state.auth.user);
  const { createOrder, recalculateCart } = useCart();

  const { FIRST_COMBO_NAME } = useSelector(
    (state: RootState) => state.config.data
  );

  // useHandlePayment();

  useEffect(() => {
    getLicensePackage();
  }, [FIRST_COMBO_NAME]);

  const cannotFindLicensePackageAlert = async () => {
    showToast({
      content:
        "Không tìm thấy thông tin gói đại sự, liên hệ với admin để biết thêm chi tiết",
      type: "error",
    });
  };

  const getProductInfo = async () => {
    if (!FIRST_COMBO_NAME) return;
    const catRes: any = await request(
      "get",
      `/api/product-cats?filters[name][$containsi]=${FIRST_COMBO_NAME}`
    );

    const res: any = await request("get", `/api/products`, {
      filters: {
        product_cats: catRes.data[0].id,
      },
      populate: ["image", "product_cats", "supplier"],
    });

    if (!res?.data?.length) {
      cannotFindLicensePackageAlert();
      return;
    }

    return res.data;
  };

  const getLicensePackage = async () => {
    const products = await getProductInfo();
    if (!products?.length) {
      return;
    }

    // const licensePackage = products.map((product) => {
    //   return {
    //     product: convertStrapiDocument(product),
    //     quantity: 1,
    //   };
    // });
    const licensePackage = [
      {
        product: convertStrapiDocument(products[0]),
        quantity: 1,
      },
    ];

    const orderData = {
      finalPrice: recalculateCart(licensePackage).finalPrice,
      paymentMethod: PaymentMethods.Bank,
      orderData: {
        items: licensePackage,
        ship: 0,
      },
      receiver: user,
    };

    setLicensePackage(orderData);
  };

  const processOrder = async () => {
    if (!licensePackage?.orderData?.items?.[0]?.product?.id) {
      cannotFindLicensePackageAlert();
      return;
    }
    navigate(
      `/product?categoryId=${licensePackage?.orderData?.items?.[0]?.product?.product_cats.data[0].id}`
    );
    // await createOrder(licensePackage);
    // navigate(
    //   Router.productDetail.replace(
    //     ":id",
    //     `${licensePackage.orderData.items[0].product.id}`
    //   )
    // );
  };

  /**
   * @description styles
   */
  const buttonContainerStyle: React.CSSProperties = {
    color: "#FFF",
    background: "#29BB9C",
    borderRadius: 99,
    padding: "4 0",
    width: "100%",
    marginTop: "50px",
  };

  return (
    <FrameContainer title="Nâng cấp tài khoản đại lý">
      <Stack
        className="ref-upgrade-account"
        style={{ background: "#fff" }}
        padding={2}
        borderRadius={4}
        marginBlock={1}
      >
        <Box className="ref-wrapper">
          {/* <Box style={{ marginTop: 12 }} className="ref-banner flex">
            <Box className="ref-content">
              <h3>{APP_SLOGAN}</h3>
              <p>
                Không lo vốn nhập hàng, không cần giao hàng Bạn chỉ cần bán hàng
                Khởi nghiệp cùng {APP_NAME} ngay!
              </p>
            </Box>
            <Box className="flex relative">
              <img src={Icon.logo_text_white} className="absolute logo" />
              <img src={Icon.community} />
            </Box>
          </Box> */}
          <Box style={{ marginTop: 12 }} className="ref-banner flex info">
            <Box className="ref-content">
              <Box className="ref-upgrade-title">
                <h3>Đại lý</h3>
              </Box>
              <h1>{formatPrice(licensePackage?.finalPrice || 0)}</h1>
              <Typography>Để hưởng những quyền lợi của đại lý</Typography>
            </Box>
          </Box>
          <Button
            size="large"
            style={buttonContainerStyle}
            onClick={processOrder}
          >
            Tiếp tục đăng ký
          </Button>
          <Box style={{ textAlign: "center", marginTop: 15 }}>
            <Link to={Router.homepage}>Bỏ qua</Link>
          </Box>
        </Box>
      </Stack>
    </FrameContainer>
  );
}
