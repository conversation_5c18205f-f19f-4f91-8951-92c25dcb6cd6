import { GetUserInfoReturns } from "zmp-sdk";

export interface IUserInfo {
  id: number;
  name: string;
  avatar?: string;
}

export interface IUserReferCommission {
  direct: number;
  indirect: number;
}

export interface IUser {
  addressId: number;
  avatarUrl: string;
  zaloId?: string;
  balance: string | null;
  blocked: boolean;
  confirmed: boolean;
  createdAt: string;
  district: string | null;
  dob: string | null;
  email: string | null;
  emailAddress: string | null;
  fParent: number;
  firstOrderAt: string | null;
  firstOrderCreatedAt: string | null;
  id: number;
  level: number;
  mySale: string | null;
  myTeamSale: string | null;
  name: string;
  numberOfSubAccount: number | null;
  phone: string;
  provider: string;
  province: string | null;
  referCode: string;
  sChildren: string | null;
  status: string | null;
  subBalance: string | null;
  taxCode: string | null;
  updatedAt: string;
  updatedReferCode: boolean;
  verified: boolean;
  ward: string | null;
  address: string | null;
  extra?: { showPopupNewLevel?: boolean };
  colabStatus: string;
  warehouse: number | null;
  commission: {
    sale: IUserReferCommission;
    refer: IUserReferCommission;
    totalCommission: number;
    withdrawalCommission: number;
    managerCommission: number;
    sharedCommission: number;
    pendingCommission: number;
  };
  isZaloOA: boolean;
  bonusPoint: number | null;
  bankInfo?: {
    owner: string;
    bank: string;
    accountNumber: string;
  };
  cccd: string;
}

export type UserZalo = GetUserInfoReturns["userInfo"];
