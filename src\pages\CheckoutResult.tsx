import FrameContainerNoIcon from "../components/layout/ContainerNoIconHeader";
import { Router } from "../constants/Route";
import { useNavigate } from "../utils/component-util";
import { But<PERSON>, Stack, Typography, Box, Fade } from "@mui/material";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useCart } from "../hooks/useCart";
import { AppEnv } from "../config";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { getUser } from "@/redux/slices/authen/authSlice";
import { APP_NAME } from "@/constants/AppInfo";
import { Icon } from "@/constants/Assets";
import { COLORS } from "@/constants/themes";

export default function CheckoutResult() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { state } = useLocation();
  const { clearCart } = useCart();

  const urlParams = new URLSearchParams(state?.path);
  const success =
    urlParams.get("vnp_ResponseCode") === "00" ||
    (state?.resultCode === 1 && state?.status === "SUCCESS") ||
    state?.path.includes("appTransID");

  useEffect(() => {
    if (success) {
      clearCart();
      dispatch(getUser());
    }
  }, []);

  const renderContent = () => {
    if (success) {
      return (
        <Fade in timeout={800}>
          <Stack spacing={3} alignItems="center">
            <Box
              sx={{
                width: 120,
                height: 120,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                animation: "bounce 1s ease",
                backgroundColor: "rgba(25, 118, 210, 0.04)",
                borderRadius: "50%",
                padding: 2,
              }}
            >
              <img
                src={Icon.check}
                alt="success"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "contain",
                }}
              />
            </Box>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: COLORS.darkBlue,
                textAlign: "center",
              }}
            >
              Thanh toán thành công
            </Typography>
            <Typography
              sx={{
                textAlign: "center",
                color: "text.secondary",
                maxWidth: 400,
                px: 2,
              }}
            >
              Cảm ơn bạn đã đặt hàng tại {APP_NAME}. Chúng tôi sẽ xử lý đơn hàng
              của bạn trong thời gian sớm nhất.
            </Typography>
            <Stack direction="row" spacing={2} mt={2}>
              <Button
                variant="contained"
                onClick={() => navigate(Router.order.index, { replace: true })}
                sx={{
                  minWidth: 150,
                  color: "#FFF",
                  backgroundColor: COLORS.primaryActive,
                  "&:hover": {
                    backgroundColor: COLORS.primaryActive,
                    transform: "translateY(-2px)",
                    transition: "all 0.2s",
                  },
                }}
              >
                Xem đơn hàng
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate(Router.homepage, { replace: true })}
                sx={{
                  minWidth: 150,
                  borderColor: COLORS.primaryActive,
                  color: COLORS.primaryActive,
                  "&:hover": {
                    borderColor: COLORS.primaryActive,
                    backgroundColor: "rgba(29, 29, 94, 0.04)",
                    transform: "translateY(-2px)",
                    transition: "all 0.2s",
                  },
                }}
              >
                Trang chủ
              </Button>
            </Stack>
          </Stack>
        </Fade>
      );
    }
    return (
      <Fade in timeout={800}>
        <Stack spacing={3} alignItems="center">
          <Box
            sx={{
              width: 120,
              height: 120,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              animation: "shake 0.5s ease",
              backgroundColor: "rgba(220, 31, 24, 0.04)",
              borderRadius: "50%",
              padding: 2,
            }}
          >
            <img
              src={Icon.warning}
              alt="error"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "contain",
              }}
            />
          </Box>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: "error.main",
              textAlign: "center",
            }}
          >
            Thanh toán không thành công
          </Typography>
          <Typography
            sx={{
              textAlign: "center",
              color: "text.secondary",
              maxWidth: 400,
              px: 2,
            }}
          >
            Vui lòng kiểm tra lại thông tin thẻ và thực hiện thanh toán lại
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate(Router.homepage, { replace: true })}
            sx={{
              minWidth: 200,
              color: "#FFF",
              backgroundColor: COLORS.primary,
              "&:hover": {
                backgroundColor: COLORS.primary,
                transform: "translateY(-2px)",
                transition: "all 0.2s",
              },
            }}
          >
            Trở về trang chủ
          </Button>
        </Stack>
      </Fade>
    );
  };

  return (
    <FrameContainerNoIcon title="Kết quả thanh toán">
      <Box
        sx={{
          "@keyframes bounce": {
            "0%, 20%, 50%, 80%, 100%": {
              transform: "translateY(0)",
            },
            "40%": {
              transform: "translateY(-20px)",
            },
            "60%": {
              transform: "translateY(-10px)",
            },
          },
          "@keyframes shake": {
            "0%, 100%": {
              transform: "translateX(0)",
            },
            "10%, 30%, 50%, 70%, 90%": {
              transform: "translateX(-5px)",
            },
            "20%, 40%, 60%, 80%": {
              transform: "translateX(5px)",
            },
          },
        }}
      >
        <Stack
          px={4}
          py={8}
          alignItems="center"
          borderRadius={3}
          sx={{
            backgroundColor: "white",
            boxShadow: "0 4px 20px rgba(0,0,0,0.05)",
          }}
        >
          {renderContent()}
        </Stack>
      </Box>
    </FrameContainerNoIcon>
  );
}

/*
- trường hợp vào vnpay xong huỷ thanh toán
  state = {
    amount: 568000
    appId: "1666682950662020156"
    createAt: *************
    err: 0
    extradata: "{"storeName":"Kho tổng","storeId":"1","orderId":91,"notes":null}"
    isCustom: false
    method: "VNPAY_SANDBOX"
    msg: undefined
    orderId: "221720729763810016918725488_1717549624996"
    path: "/checkout-result"
    resultCode: 0
    status: "PENDING"
    transId: "240605_0807049962773438459351224118349"
    transTime: 0
    updateAt: *************
    zmpOrderId: "221720729763810016918725488_1717549624996"
  }

  - trường hợp thanh toán VNPAY thành công:
  state={
    path: "/checkout-result?env=DEVELOPMENT
      &version=zdev-ab0db573
      &appTransID=240605_0839083352773438459351224118266
      &vnp_Amount=********
      &vnp_BankCode=NCB
      &vnp_BankTranNo=VNP********
      &vnp_CardType=ATM
      &vnp_OrderInfo=thanh+toan+568+000d
      &vnp_PayDate=**************
      &vnp_ResponseCode=00
      &vnp_TmnCode=NUWA0001
      &vnp_TransactionNo=********
      &vnp_TransactionStatus=00
      &vnp_TxnRef=240605_0839083352773438459351224118266
      &vnp_SecureHash=7ea08ab3214db3e444cf7806fd75e666105516799a80b1bc083da958e12198e13f290586eb6f5b561e4308e90d7b5692465b4a812dc53c115f728be83099d89b"}

  - trường hợp thanh toán vi VNPAY thanh cong:
  state = {
    amount: 1000
    appId: "1666682950662020156"
    createAt: *************
    err: 0
    extradata: "{"storeName":"Kho tổng","storeId":"1","orderId":121,"notes":null}"
    isCustom: false
    method: "VNPAY"
    msg: "Giao dịch thành công"
    orderId: "221720729763810016918725488_1718155970222"
    path: "/checkout-result"
    resultCode: 1
    resultMessage: "Giao dịch thành công"
    status: "SUCCESS"
    transId: "240612_0832502222773438459351224118384"
    transTime: 1718155998000
    updateAt: 1718155999188
    zmpOrderId: "221720729763810016918725488_1718155970222"
  }
*/
