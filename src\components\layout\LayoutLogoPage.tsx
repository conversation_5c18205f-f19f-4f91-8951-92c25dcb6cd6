import { Box, Container, Stack, Typography } from "@mui/material";
import React from "react";

import { COLORS } from "@/constants/themes";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
export default function LayoutLogoPage({ children }: any) {
  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );
  const APP_SLOGAN = useSelector(
    (state: RootState) => state.config.data.appSlogan ?? ""
  );

  return (
    <Box
      style={{
        background: "#F9F9F9",
        position: "relative",
        paddingBottom: "100px",
      }}
    >
      <Box
        style={{
          background: COLORS.colorHeader,
          paddingTop: "50px",
          paddingBottom: "15px",
        }}
      >
        <Container>
          <Stack direction="row" gap={2} alignItems={"center"}>
            <img
              src="/images/logo.png"
              alt="Logo"
              style={{
                width: "120px",
                height: "100%",
                objectFit: "contain",
              }}
            />
            <Box sx={{ color: COLORS.white }}>
              <Typography fontWeight={700} fontSize={16}>
                {APP_NAME}
              </Typography>
              <Typography fontSize={12} marginLeft={0.2}>
                {APP_SLOGAN}
              </Typography>
            </Box>
          </Stack>
        </Container>
      </Box>
      {children}
    </Box>
  );
}
