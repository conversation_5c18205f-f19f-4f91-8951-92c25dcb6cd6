import React, { memo, useEffect } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { InputAdornment, Stack, TextField, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { COLORS, theme } from "@/constants/themes";
import { WithdrawalRanger, WithdrawalRangerWarning } from "@/constants/Const";
import { RootState } from "@/redux/store";

type FormData = {
  amount: number | null;
};

interface IPopupInputWithdrawal {
  openWithdrawal: boolean;
  setOpenWithdrawal: (status: boolean) => void;
  submitWithdrawal: (values: any) => Promise<void>;
}

const PopupInputWithdrawal = memo(
  ({
    openWithdrawal,
    setOpenWithdrawal,
    submitWithdrawal,
  }: IPopupInputWithdrawal) => {
    const { user } = useSelector((state: RootState) => state.auth);
    const maxWithdrawal = Math.min(
      parseInt(user?.balance ?? "0"),
      WithdrawalRanger.Max
    );
    const validationSchema = Yup.object().shape({
      amount: Yup.number()
        .required("Vui lòng nhập số tiền muốn rút")
        .transform((value) => (Number.isNaN(value) ? null : value))
        .nullable()
        .max(
          maxWithdrawal,
          maxWithdrawal < WithdrawalRanger.Max
            ? "Vượt quá số tiền khả dụng trong ví"
            : WithdrawalRangerWarning[WithdrawalRanger.Max]
        )
        .min(
          WithdrawalRanger.Min,
          WithdrawalRangerWarning[WithdrawalRanger.Min]
        ),
    });

    const formOptions = { resolver: yupResolver(validationSchema) };

    const { handleSubmit, register, formState, reset } =
      useForm<FormData>(formOptions);
    const { errors } = formState;

    const handleClosePopup = () => {
      setOpenWithdrawal(false);
      reset();
    };

    useEffect(() => {
      reset();
    }, []);

    return (
      <Dialog
        open={openWithdrawal}
        onClose={handleClosePopup}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <form onSubmit={handleSubmit(submitWithdrawal)}>
          <DialogContent>
            <Stack gap={2}>
              <Typography style={styles.titleStyle}>Rút tiền</Typography>
              <TextField
                style={{ width: 200 }}
                type="Number"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">đ</InputAdornment>
                  ),
                }}
                inputProps={{ inputMode: "numeric" }}
                required
                id="outlined-required"
                label="Nhập số tiền rút"
                {...register("amount")}
              />
              {errors.amount && (
                <Typography
                  style={{ width: 200, color: theme.palette.error.main }}
                >
                  {errors.amount.message}
                </Typography>
              )}
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              style={styles.firstBtnSyle}
              onClick={handleClosePopup}
              variant="contained"
            >
              Huỷ
            </Button>
            <Button type="submit" style={styles.secondBtnSyle}>
              Xác nhận
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    );
  }
);
PopupInputWithdrawal.displayName = "PopupInputWithdrawal";
export default memo(PopupInputWithdrawal);

const styles: Record<string, React.CSSProperties> = {
  submitBtn: {
    background: COLORS.primary,
    color: "#fff",
    width: "120px",
  },
  titleStyle: {
    color: theme.palette.primary.main,
    fontWeight: 700,
    marginBottom: 2,
    textAlign: "center",
  },
  firstBtnSyle: {
    background: "#E8E8EA",
    color: "#666666",
    boxShadow: "none",
    width: "50%",
    marginInline: 8,
    marginBottom: 8,
  },
  secondBtnSyle: {
    background: COLORS.darkBlue,
    color: "#FFFFFF",
    boxShadow: "none",
    width: "50%",
    marginInline: 8,
    marginBottom: 8,
  },
};
