import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { openShareSheet } from "../utils/openShare";
import { useCheckLogin } from "./useCheckLogin";
import { getReferLink } from "../utils/common";

export const useShareReferLink = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const { checkLogin } = useCheckLogin();

  const shareLink = () => {
    checkLogin(() => {
      const referLink = getReferLink(user?.referCode);
      openShareSheet(referLink);
    });
  };

  return { shareLink };
};
