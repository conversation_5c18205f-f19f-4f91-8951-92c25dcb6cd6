import {
  Box,
  Container,
  Rating,
  Stack,
  Typography,
  useTheme,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import { formatPrice } from "@/utils/formatPrice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import React, { useEffect, useState } from "react";
import { IProduct, IProductComment } from "@/types/product";
import { COLORS } from "@/constants/themes";
import {
  setCurrentGift,
  getPromotionByProduct,
} from "@/redux/slices/promotion/promotionSilice";
import { isBefore, isAfter, parseISO } from "date-fns";
import ShareView from "../UI/ShareView";
import { APP_NAME } from "@/constants/AppInfo";
import GradientText from "../UI/GradientTextProps";
import { round } from "lodash";
export default function ProductDetailContent({
  product,
  rateList,
  rateStat,
}: {
  product: IProduct;
  rateList: IProductComment[];
  rateStat: number;
}) {
  const user = useSelector((state: RootState) => state.auth.user);
  const promotion = useSelector(
    (state: RootState) => state.promotion.promotion
  );

  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  const currentDate = new Date();

  const startDateStr = promotion?.startDate;
  const endDateStr = promotion?.endDate;
  const startDate = startDateStr ? parseISO(startDateStr) : null;
  const endDate = endDateStr ? parseISO(endDateStr) : null;
  const isExpiredPromotion =
    startDate &&
    endDate &&
    isBefore(startDate, currentDate) &&
    isAfter(endDate, currentDate);

  const [selectedGift, setSelectedGift] = useState<any>(null);

  const handleChange = (event) => {
    const selected = promotion.productGift.find(
      (item) => item.product.id == event.target.value
    );
    setSelectedGift(selected);
  };

  const investInfo = [
    {
      label: "Hoa hồng trực tiếp (đ)",
      valueKey: "commission",
    },
  ];

  useEffect(() => {
    if (selectedGift) {
      dispatch(setCurrentGift(selectedGift));
    }
  }, [selectedGift]);
  useEffect(() => {
    if (promotion?.productGift) {
      const defaultGift = promotion.productGift[0];
      setSelectedGift(defaultGift);
    }
  }, [promotion]);

  useEffect(() => {
    return () => {
      dispatch(setCurrentGift(null));
    };
  }, []);

  useEffect(() => {
    if (product?.id && user) {
      dispatch(
        getPromotionByProduct({ productId: product.id, userId: user!.id })
      );
    }
  }, [product?.id, dispatch, user]);

  return (
    <>
      <Box
        style={{
          background: "#fff",
          paddingBottom: 20,
          paddingTop: 12,
          paddingInline: 10,
        }}
      >
        <Typography
          fontSize={"16px"}
          fontWeight={700}
          color={theme.palette.info.main}
        >
          {product.name}
        </Typography>
        <Box sx={{ display: "flex" }}>
          <Box display={"flex"} alignItems="baseline">
            <GradientText
              text={formatPrice(product.price - product.discount)}
              style={{ fontSize: 20, fontWeight: 700 }}
            />
            {product.discount > 0 && (
              <Typography
                color={"#B9BDC1"}
                fontSize={14}
                fontWeight={400}
                marginInline={"10px"}
                style={{ textDecoration: "line-through" }}
              >
                {formatPrice(product.price)}
              </Typography>
            )}
          </Box>
        </Box>
        {product.bv && (
          <Box marginTop={0.5}>
            <GradientText
              text={`BV: ${formatPrice(
                round((product.bv * (product.price - product.discount)) / 100)
              )}`}
              style={{ fontSize: 20, fontWeight: 700 }}
            />
          </Box>
        )}
        {/* <Box
            sx={{
              fontWeight: 400,
              fontSize: 14,
              marginTop: "10px",
            }}
          >
            <Box>
              {product?.product_cats?.data
                .map((tag) => tag.attributes.name)
                .join(", ")}
            </Box>
            <Box>
              Thương hiệu:
              <span
                style={{
                  color: theme.palette.primary.main,
                  marginInline: "5px",
                }}
              >
                {product?.supplier?.data?.attributes.name}
              </span>
            </Box>
          </Box> */}
        <Stack
          direction="row"
          alignItems={"center"}
          gap={2}
          mt={2}
          mb={2}
          fontSize={14}
          style={{ color: "#1D1D5E" }}
        >
          {rateList.length > 0 ? (
            <Box display={"flex"} alignItems={"center"}>
              <Rating
                name="half-rating-read"
                size="small"
                value={rateStat}
                precision={0.5}
                readOnly
              />
              <Box>({rateStat} sao)</Box>
            </Box>
          ) : (
            <GradientText text="Chưa có đánh giá" />
          )}

          <Box>
            <GradientText text={`${rateList.length} đánh giá`} />
          </Box>
          <Box>
            <GradientText text={`${product.sold || 0} lượt bán`} />
          </Box>
        </Stack>
        {isExpiredPromotion && (
          <Stack>
            <Typography
              fontSize={"16px"}
              fontWeight={700}
              color={theme.palette.info.main}
              mt={2}
              mb={1}
            >
              Quà tặng
            </Typography>
            {selectedGift && (
              <FormControl>
                <RadioGroup
                  aria-labelledby="demo-form-control-label-placement"
                  name="position"
                  defaultValue={selectedGift?.product?.id}
                  onChange={handleChange}
                  style={{
                    marginLeft: "-17px",
                    paddingRight: "10px",
                  }}
                >
                  {promotion.productGift.length > 0 &&
                    promotion.productGift.map((gift, index) => {
                      return (
                        <FormControlLabel
                          key={index}
                          sx={{
                            backgroundColor: COLORS.grayLight,
                            borderRadius: "8px",
                            paddingRight: "12px",
                            mb: "5px",
                            flex: 1,
                            display: "flex",
                            justifyContent: "space-between",
                          }}
                          value={gift.product.id}
                          control={<Radio />}
                          label={
                            <Stack
                              gap={1}
                              direction="row"
                              alignItems={"center"}
                            >
                              <Box
                                mt={"8px"}
                                mb={"8px"}
                                ml={"24px"}
                                width="60px"
                                height="60px"
                              >
                                <img
                                  style={{ width: "100%", height: "100%" }}
                                  src={`${import.meta.env.VITE_API_URL}${
                                    gift?.product?.image?.[0]?.url
                                  }`}
                                  alt={"images"}
                                />
                              </Box>
                              <Box>
                                <Typography fontSize={14}>
                                  {gift?.product?.name}
                                  <span
                                    style={{
                                      color: theme.palette.info.main,
                                      fontWeight: "700",
                                      paddingLeft: "5px",
                                    }}
                                  >
                                    x{gift.quantity}
                                  </span>
                                </Typography>
                                <Typography fontSize={14}>
                                  Trị giá
                                  <span
                                    style={{
                                      color: theme.palette.primary.main,
                                      fontWeight: "700",
                                      paddingLeft: "10px",
                                      fontSize: "16px",
                                    }}
                                  >
                                    {gift?.product?.price}đ
                                  </span>
                                </Typography>
                              </Box>
                            </Stack>
                          }
                          labelPlacement="start"
                        />
                      );
                    })}
                </RadioGroup>
              </FormControl>
            )}
          </Stack>
        )}
        <ShareView
          title={product?.name || `Sản phẩm của ${APP_NAME}`}
          description={product?.name || `Sản phẩm của ${APP_NAME}`}
          thumbnail={product?.image?.[0]?.url}
          path={
            import.meta.env.DEV
              ? `?env=TESTING&page=product-detail&id=${product?.id}`
              : `?page=product-detail&id=${product?.id}`
          }
          overrideStyles={{ marginBlock: 0.5 }}
        />
      </Box>
      {product.isFixedCommission && (
        <Stack
          style={{ background: COLORS.primary, marginBlock: 12, fontSize: 14 }}
          py={2}
        >
          <Container>
            <Typography fontWeight={700} fontSize={16}>
              Thông tin hoa hồng
            </Typography>
            <Typography fontSize={16}>
              Đây là sản phẩm hoa hồng cố định không tính vào doanh số
            </Typography>
            <Stack direction={"column"} gap={1} mt={1}>
              {investInfo.map((info, index) => {
                return (
                  <Box
                    key={index}
                    display={"flex"}
                    justifyContent={"space-between"}
                  >
                    <Typography fontSize={16}>{info.label}</Typography>
                    <Typography fontWeight={700} fontSize={16}>
                      {(info.valueKey === "point"
                        ? product[info.valueKey]
                        : formatPrice(product[info.valueKey])) || 0}
                    </Typography>
                  </Box>
                );
              })}
            </Stack>
          </Container>
        </Stack>
      )}

      {/* description */}
      <Stack
        style={{ background: "#fff", marginBlock: 12, fontSize: 14 }}
        py={2}
      >
        <Container>
          <Box
            style={{ color: "#555555" }}
            dangerouslySetInnerHTML={{
              __html: product.description,
            }}
          />
        </Container>
      </Stack>
    </>
  );
}
