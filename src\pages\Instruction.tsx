import { Box, Container, Divider, Stack, Typography } from "@mui/material";
import { default as React, useEffect } from "react";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import FrameContainer from "@/components/layout/Container";

export default function Instruction() {
  const steps = [
    {
      content: "Chọn 'sản phẩm' cần đặt hàng cho Khách hàng trong mục Sản phẩm",
    },
    {
      content: "Điền thông tin 'Khách hàng' của Bạn để Nuwa gửi hàng",
    },
    {
      content: "Điều chỉnh số lượng, sản phẩm nếu muốn",
    },
    {
      content: "Bấm vào nút 'Đặt hàng ngay' để tiến hành đặt hàng cho Khách hàng",
    },
    {
      content: "'Gửi thông tin' và theo dõi đơn hàng",
    },
  ];
  return (
    <FrameContainerFull title="Hướng dẫn sử dụng"   overrideStyle={{ background: "#fff",height:"100vh" }}>
      <Box>
        <img
          src="/images/huong-dan-banner.png"
          width={"100%"}
          height={"100%"}
          alt="Flex"
        />
        <Container>
          <Box>
            <Typography fontWeight={700}>Tạo đơn hàng - Nhận hoa hồng</Typography>
            <Stack gap={1} marginTop={2}>
              {steps.map((item, index) => (
                <Box key={index}>
                  <Typography>
                    <span style={{ fontWeight: 700 }}>Bước {index + 1}: </span>{" "}
                    {item.content.split('\'').map((char, index) => (
                      <span
                        key={index}
                        style={index === 1 ? { color: "#1D1D5E", fontWeight: 700 } : {}}
                      >
                        {char}
                      </span>
                    ))}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Box>
        </Container>
      </Box>
    </FrameContainerFull>
  );
}
