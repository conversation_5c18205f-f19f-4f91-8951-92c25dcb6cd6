import React from "react";
import { Box, Grid } from "@mui/material";
import { useNavigate } from "../../utils/component-util";

export default function NewsItem({ item }) {
  const navigate = useNavigate();
  return (
    <Box
      display={"flex"}
      flexDirection={"column"}
      paddingRight={"10px"}
      onClick={() => {
        navigate(`/posts/${item.id}`);
      }}
      style={{
        cursor: "pointer",
        width: "100%",
      }}
    >
      <img
        width="100%"
        style={{
          border: "1px solid #E9EBED",
          borderRadius: "15px",
          aspectRatio: "1.75 / 1",
          objectFit: "cover",
        }}
        src={`${import.meta.env.VITE_API_URL}${
          item?.image?.data?.attributes?.formats?.medium?.url
        }`}
        alt={item.title}
      />
      <Box
        style={{
          marginTop: "5px",
          color: "#252627",
          lineHeight: "20px",
          overflow: "hidden",
          textOverflow: "ellipsis",
          display: "-webkit-box",
          WebkitBoxOrient: "vertical",
          WebkitLineClamp: 2,
          fontWeight: 400,
        }}
      >
        {item.title}
      </Box>
    </Box>
  );
}
