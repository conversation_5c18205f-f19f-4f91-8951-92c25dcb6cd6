import { createTheme } from "@mui/material";

export const theme = createTheme({
  typography: {
    fontFamily:
      '"Be Vietnam Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    button: {
      textTransform: "none",
      fontWeight: 700,
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    h1: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    h2: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    h3: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    h4: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    h5: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    h6: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    body1: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
    body2: {
      fontFamily: '"Be Vietnam Pro", sans-serif',
    },
  },
  palette: {
    primary: {
      main: "#1D1D5E",
    },
    secondary: {
      main: "#1D1D5E",
    },
    error: {
      main: "#dc1f18",
    },
    warning: {
      main: "#FFB42A",
    },
    info: {
      main: "#1F396C",
    },
    success: {
      main: "#1D1D5E",
    },
  },
});

export const COLORS = {
  gray: "#969595",
  grayLight: "#E9EBED",
  darkBlue: "#1D1D5E",
  primary: "#007AFF",
  primary1: "#F4ECDC",
  primary2: "#CA8F3E",
  default: "#767A7F",
  primaryActive: "rgb(128, 197, 67)",
  blue: "#143374",
  colorHeader: "#007AFF",
  infoText: "#1D1D5E",
  accent1: "#00316C",
  accent2: "#FEF6E9",
  accent3: "#FFF9ED",
  accent4: "#EDF7FE",
  neutral1: "#000000",
  neutral2: "#414141",
  neutral3: "#646464",
  neutral4: "#787878",
  neutral5: "#8C8C8C",
  neutral6: "#A0A0A0",
  neutral7: "#B3B3B3",
  neutral9: "#C6C6C6",
  neutral10: "#F5F5F5",
  white: "#FFFFFF",
};

export const commonStyle: Record<string, React.CSSProperties> = {
  shadowBorder: {
    borderRadius: "5px",
    backgroundColor: "#ffffff",
    boxShadow: "0px 0px 3px rgba(0, 0, 0, 0.25)",
    fontFamily: '"Be Vietnam Pro", sans-serif',
  },
  headline12: {
    fontSize: 12,
    fontWeight: 700,
    fontFamily: '"Be Vietnam Pro", sans-serif',
  },
  headline14: {
    fontSize: 14,
    fontWeight: 700,
    fontFamily: '"Be Vietnam Pro", sans-serif',
  },
  headline16: {
    fontSize: 16,
    fontWeight: 700,
    fontFamily: '"Be Vietnam Pro", sans-serif',
  },
  headline18: {
    fontSize: 18,
    fontWeight: 700,
    fontFamily: '"Be Vietnam Pro", sans-serif',
  },
  headline20: {
    fontSize: 20,
    fontWeight: 700,
    fontFamily: '"Be Vietnam Pro", sans-serif',
  },
};
