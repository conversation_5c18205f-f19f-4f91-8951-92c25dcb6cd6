import { Backdrop, ThemeProvider } from "@mui/material";
import React from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { RecoilRoot } from "recoil";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";
import { App, SnackbarProvider } from "zmp-ui";
import { Router } from "../constants/Route";
import { theme } from "../constants/themes";
import Cart from "../pages/Cart";
import Order from "../pages/Order";
import OrderDetail from "../pages/Order/OrderDetail";
import PostDetail from "../pages/Post/PostDetail";
import Posts from "../pages/Post/Posts";
import ProductDetail from "../pages/ProductDetail";
import Address from "../pages/Profile/Address";
import Info from "../pages/Profile/Info";
import MemberShip from "../pages/Profile/MemberShip";
import PolicyList from "../pages/Profile/policy/Policy";
import PolicyDetail from "../pages/Profile/policy/PolicyDetail";
import Profile from "../pages/Profile/Profile";
import Voucher from "../pages/Profile/voucher/Voucher";
import VoucherDetail from "../pages/Profile/voucher/VoucherDetail";
import Search from "../pages/Search";
import ReferPage from "../pages/Refer";
import EnterCode from "../pages/EnterCode";
import Notification from "../pages/Notification";
import TeamListPage from "../pages/TeamList";
import PaymentInfomation from "../pages/PaymentInfomation";
import HomePage from "../pages/Home";
import { Provider, useSelector } from "react-redux";
import store, { RootState, persistor } from "../redux/store";
import initData from "../hooks/initData";
import BankTransfer from "../pages/BankTransfer";
import { Toaster } from "react-hot-toast";
import CustomAlert from "./UI/CustomAlert";
import Login from "../pages/auth/Login";
import Register from "../pages/auth/Register";
import { AppRouter, BottomNavigation } from "../utils/component-util";
import { Platform } from "../config";
import { ScrollToTop } from "./base/ScrollToTop";
import { PersistGate } from "redux-persist/integration/react";
import Product from "@/pages/Product";
import Collab from "@/pages/Collab/Collab";
import CollabHistoryPage from "@/pages/Collab/CollabHistoryPage";
import CollabRegister from "@/pages/Collab/CollabRegister";
import Instruction from "@/pages/Instruction";
import CollabReportPage from "@/pages/Collab/CollabReportPage";
import AuthPage from "@/pages/AuthPage";
import WithdrawalHistory from "@/pages/Collab/WithdrawalHistory";
import CollabCommissionPage from "@/pages/Collab/CollabCommissionPage";
import { ColabStatus, Level } from "@/constants/Const";
import PageNotFound from "@/pages/PageNotFound";
import TermList from "@/pages/Profile/policy/TermList";
import Point from "@/pages/Point";
import MyVoucher from "@/pages/Profile/voucher/MyVoucher";
import PaymentInfo from "@/pages/Profile/Payment";
import CollabMembers from "@/pages/Collab/CollabMembers";
import UpgradeAccount from "@/pages/UpgradeAccount";
import LoadingSpinner from "./UI/LoadingScreen";
import CheckoutResult from "@/pages/CheckoutResult";

const Root = () => {
  initData();
  const user = useSelector((state: RootState) => state.auth?.user);

  function PrivateRouteCollab({ children }: { children: React.ReactNode }) {
    const isCollaborator =
      user && user?.level >= Level.Member && user?.level <= Level.CEO;
    return isCollaborator ? (
      <>{children}</>
    ) : (
      <Navigate to={Router.collab.index} />
    );
  }

  function PrivateRouteRegisterCollab({
    children,
  }: {
    children: React.ReactNode;
  }) {
    return user?.colabStatus === ColabStatus.Not_Waiting ? (
      <>{children}</>
    ) : (
      <Navigate to={Router.collab.index} />
    );
  }

  const handleGetRoute = () => {
    let userInfo: string | null = null;
    try {
      userInfo = sessionStorage.getItem("userInfo");
    } catch (error) {
      console.log("error", error);
    }
    if (!user && !userInfo && Platform === "web") {
      return (
        <>
          {/* <Route path={Router.auth} element={<AuthPage />} /> */}
          <Route path={Router.login} element={<Login />} />
          <Route path={Router.register} element={<Register />} />
          <Route path="*" element={<PageNotFound />} />
        </>
      );
    }
    if (!user && !userInfo && Platform !== "web") {
      return (
        <>
          <Route path={Router.homepage} element={<HomePage />} />
          <Route path={Router.profile.index}>
            <Route index element={<Profile />} />
          </Route>
          <Route path={Router.collab.index}>
            <Route index element={<Collab />} />
          </Route>
          <Route path={Router.order.index} element={<Order />} />
          <Route path={Router.order.detail} element={<OrderDetail />} />
          <Route path={Router.product} element={<Product />} />
          <Route path={Router.productDetail} element={<ProductDetail />} />
          <Route path={Router.search} element={<Search />} />
          <Route path={Router.cart} element={<Cart />} />
          <Route path={Router.payment.index} element={<PaymentInfomation />} />
          <Route path={Router.bankTransfer.index} element={<BankTransfer />} />
          <Route path={Router.post.index} element={<Posts />} />
          <Route path={Router.post.detail} element={<PostDetail />} />
          <Route path="*" element={<PageNotFound />} />
        </>
      );
    }
    return (
      <>
        <Route path={Router.homepage} element={<HomePage />} />
        {/* Profile tab */}
        <Route path={Router.profile.index}>
          <Route index element={<Profile />} />
          <Route path={Router.profile.payment} element={<PaymentInfo />} />
          <Route path={Router.profile.info} element={<Info />} />
          <Route path={Router.profile.memberShip} element={<MemberShip />} />
          <Route path={Router.profile.address} element={<Address />} />
          <Route path={Router.profile.voucher.index}>
            <Route index element={<Voucher />} />
            <Route
              path={Router.profile.voucher.detail}
              element={<VoucherDetail />}
            />
          </Route>
          <Route path={Router.profile.policy.index}>
            <Route index element={<PolicyList />} />
            <Route
              path={Router.profile.policy.detail}
              element={<PolicyDetail />}
            />
          </Route>
          <Route path={Router.profile.term.index}>
            <Route index element={<TermList />} />
            <Route
              path={Router.profile.term.detail}
              element={<PolicyDetail />}
            />
          </Route>
          <Route
            path={Router.profile.teamList.index}
            index
            element={<TeamListPage />}
          />
        </Route>

        <Route path={Router.voucher.index}>
          <Route index element={<Voucher />} />
          <Route path={Router.voucher.detail} element={<VoucherDetail />} />
        </Route>

        <Route path={Router.myVoucher.index}>
          <Route index element={<MyVoucher />} />
          <Route path={Router.myVoucher.detail} element={<VoucherDetail />} />
        </Route>

        <Route path={Router.order.index} element={<Order />} />
        <Route path={Router.order.detail} element={<OrderDetail />} />
        <Route path={Router.post.detail} element={<PostDetail />} />
        <Route path={Router.notification} element={<Notification />} />

        <Route path={Router.refer.index}>
          <Route
            index
            element={
              <PrivateRouteCollab>
                <ReferPage />
              </PrivateRouteCollab>
            }
          />
          <Route path={Router.refer.upgrade} element={<UpgradeAccount />} />
        </Route>

        <Route path={Router.collab.index}>
          <Route index element={<Collab />} />
          <Route
            path={Router.collab.collabhistory.index}
            element={
              <PrivateRouteCollab>
                <CollabHistoryPage />
              </PrivateRouteCollab>
            }
          />
          <Route
            path={Router.collab.collabRegister}
            element={
              <PrivateRouteRegisterCollab>
                <CollabRegister />
              </PrivateRouteRegisterCollab>
            }
          />
          <Route
            path={Router.collab.members}
            element={
              <PrivateRouteRegisterCollab>
                <CollabMembers />
              </PrivateRouteRegisterCollab>
            }
          />
          <Route
            path={Router.collab.withdrawal}
            element={
              <PrivateRouteCollab>
                <WithdrawalHistory />
              </PrivateRouteCollab>
            }
          />
          <Route
            path={Router.collab.commission}
            element={
              <PrivateRouteCollab>
                <CollabCommissionPage />
              </PrivateRouteCollab>
            }
          />

          <Route path={Router.collab.instruction} element={<Instruction />} />
          <Route
            path={Router.collab.collabRepport}
            element={
              <PrivateRouteCollab>
                <CollabReportPage />
              </PrivateRouteCollab>
            }
          />
        </Route>

        <Route path={Router.post.index} element={<Posts />} />
        <Route path={Router.post.detail} element={<PostDetail />} />

        <Route path={Router.product} element={<Product />} />
        <Route path={Router.productDetail} element={<ProductDetail />} />
        <Route path={Router.search} element={<Search />} />
        <Route path={Router.cart} element={<Cart />} />
        <Route path={Router.payment.index} element={<PaymentInfomation />} />
        <Route path={Router.bankTransfer.index} element={<BankTransfer />} />
        <Route path={Router.point} element={<Point />} />
        <Route path={Router.checkoutResult} element={<CheckoutResult />} />
      </>
    );
  };

  return (
    <>
      <LoadingSpinner />
      <Routes>{handleGetRoute()}</Routes>
      <CustomAlert />
      <BottomNavigation />
    </>
  );
};

const MyApp = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <RecoilRoot>
          <ThemeProvider theme={theme}>
            <App>
              <SnackbarProvider>
                <AppRouter>
                  <ScrollToTop />
                  <Root />
                </AppRouter>
              </SnackbarProvider>
              <Toaster />
            </App>
          </ThemeProvider>
        </RecoilRoot>
      </PersistGate>
    </Provider>
  );
};
export default MyApp;
