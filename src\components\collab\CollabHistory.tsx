import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b, <PERSON><PERSON>, <PERSON>rid, Typography } from "@mui/material";
import { Box } from "@mui/system";
import DropdownCollabHistory from "../dropdown/DropdownCollabHistory";
import { useTheme } from "@mui/material/styles";
import {
  DefaultFilter,
  OrderStatus,
  OrderStatusText,
} from "../../constants/Const";
import { useDispatch, useSelector } from "react-redux";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import { AppDispatch, RootState } from "@/redux/store";
import { getTeamOrder } from "@/redux/slices/team/team";
import ReportCard from "../report/ReportCard";
import OrderItem from "@/pages/OrderItem";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ marginBlock: 2 }}>{children}</Box>}
    </Box>
  );
}

export default function CollabHistoryPage() {
  const [value, setValue] = useState(0);
  const navigator = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { teamOrder } = useSelector((state: RootState) => state.team);
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
  const [orderStatus, setOrderStatus] = useState(0);
  const [selectedRange, setSelectedRange] = useState([]);

  const theme = useTheme();

  const categories = [
    {
      label: "Lịch sử đơn hàng",
      icon: "",
    },
    {
      label: "Lịch sử rút tiền",
      icon: "",
    },
  ];

  useEffect(() => {
    const filters: any = {};
    if (orderStatus) {
      filters.orderStatus = orderStatus;
    }
    const [start, end] = selectedRange;
    if (start && end) {
      filters.createdAt = {
        $between: [
          moment(start).startOf("day").format(),
          moment(end).endOf("day").format(),
        ],
      };
    }
    dispatch(getTeamOrder({ filters }));
  }, [orderStatus, selectedRange]);

  const OrderStatuses = [
    DefaultFilter,
    ...Object.keys(OrderStatusText)
      .map((key) => ({
        id: Number(key),
        name: OrderStatusText[key],
      }))
      .filter((item) => item.id !== OrderStatus.OutOfStock),
  ];

  return (
    <>
      <ReportCard />
      <Box
        style={{
          display: "inline-block",
          justifyContent: "center",
          paddingTop: "15px",
        }}
      >
        <Tabs
          className="collab-history"
          value={value}
          onChange={handleChange}
          variant="scrollable"
          scrollButtons="auto"
          TabIndicatorProps={{
            style: {
              backgroundColor: "#1D1D5E",
              padding: "0 40px",
            },
          }}
        >
          {categories.map((item, index) => (
            <Tab
              key={index}
              style={{
                fontSize: 14,
                fontWeight: 400,
                marginRight: index === categories.length - 1 ? 0 : "25px",
              }}
              label={item.label}
            />
          ))}
        </Tabs>
      </Box>
      {/* dropdown sort */}
      <Grid
        container
        justifyContent="center"
        spacing={2}
        style={{ paddingTop: "15px" }}
      >
        <Grid item xs={12} sm={6}>
          {/* <DropdownCollabHistory
            label=""
            options={["Toàn thời gian", "Nửa ngày", "Cả ngày"]}
            defaultValue={sortBy}
            onChange={handleSortByChange}
          /> */}
          <DatePicker
            selected={selectedRange[0]}
            onChange={setSelectedRange}
            startDate={selectedRange[0]}
            placeholderText="Khoảng ngày"
            endDate={selectedRange[1]}
            selectsRange
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <DropdownCollabHistory
            label=""
            options={OrderStatuses}
            defaultValue={orderStatus}
            onChange={setOrderStatus}
          />
        </Grid>
      </Grid>
      <CustomTabPanel value={value} index={0}>
        <Stack gap={1}>
          {teamOrder?.length > 0 ? (
            teamOrder.map((order) => (
              <OrderItem order={order} key={String(order.id)} />
            ))
          ) : (
            <Stack direction="row" justifyContent={"center"}>
              <Typography
                style={{
                  fontSize: 16,
                  fontWeight: 400,
                  color: "#666666",
                  textAlign: "center",
                }}
                p={4}
              >
                Không có đơn hàng nào
              </Typography>
            </Stack>
          )}
        </Stack>
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        <Typography
          style={{
            fontSize: 16,
            fontWeight: 400,
            color: "#666666",
            textAlign: "center",
          }}
          p={4}
        >
          Không có lịch sử rút tiền
        </Typography>
      </CustomTabPanel>
    </>
  );
}
