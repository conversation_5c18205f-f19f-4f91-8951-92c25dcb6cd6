import { openChat as openChatZalo } from "zmp-sdk/apis";
import { Platform } from "../config";

export const openChat = () => {
  if (Platform === "zalo") {
    openChatZalo({
      type: "oa",
      id: import.meta.env.VITE_ZMP_OA_ID as string,
      message: "<PERSON><PERSON>",
    });
  } else {
    window
      ?.open(
        `http://zalo.me/${import.meta.env.VITE_ZMP_OA_ID}?src=qr`,
        "_blank"
      )
      ?.focus();
  }
};
