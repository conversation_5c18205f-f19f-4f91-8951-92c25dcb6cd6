import React from "react";
import { Select, MenuItem, FormControl, InputLabel } from "@mui/material";

function DropdownCollabHistory({ label, options, defaultValue, onChange }) {
  return (
    <FormControl
      sx={{
        width: "100%",
        ".MuiOutlinedInput-notchedOutline": {
          border: "1px soild #D9D9D9 !important",
          borderRadius: "8px",
        },
        "&.Mui-focused": {
          border: "none",
        },
      }}
    >
      <InputLabel>{label}</InputLabel>
      <Select
        value={defaultValue}
        onChange={(e) => onChange(e.target.value)}
        sx={{
          backgroundColor: "#FFFFFF", // Keep the background white even when focused
          height: "40px",
          borderRadius: "8px",
          fontSize: "16px",
        }}
      >
        {options.map((option, index) => (
          <MenuItem
            key={index}
            value={option?.id}
            sx={{
              fontSize: "16px",
              fontWeight: 400,
              lineHeight: "19.36px",
              textAlign: "left",
            }}
          >
            {option.name || option}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}

export default DropdownCollabHistory;
