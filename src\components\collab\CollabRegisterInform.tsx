import React from "react";
import { APP_NAME } from "@/constants/AppInfo";
import { Router } from "@/constants/Route";
import { COLORS } from "@/constants/themes";
import { useNavigate } from "@/utils/component-util";
import { Box, Button, Stack, Typography } from "@mui/material";

export default function CollabRegisterInform({ style = {} }) {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate([Router.collab.index, Router.collab.collabRegister].join("/"));
  };
  return (
    <Box
      style={{
        display: "flex",
        background: COLORS.darkBlue,
        padding: "15px 25px",
        color: "#fff",
        borderRadius: 5,
        marginBottom: 15,
        ...style,
      }}
    >
      <Box style={{ flex: "1 1" }}>
        <Typography style={{ fontWeight: "bold", fontSize: 16 }}>
          <PERSON><PERSON><PERSON> ho<PERSON>t cộng tác viên
        </Typography>
        <Typography style={{ fontSize: 12 }}>
          Nh<PERSON>n nhiều ưu đãi đến từ <b>{APP_NAME.toUpperCase()}</b>
        </Typography>
      </Box>
      <Box
        style={{
          display: "flex",
          alignItems: "center",
          margin: "0 0 0 auto",
        }}
      >
        <Button
          style={{ background: "#fff", color: COLORS.darkBlue, height: 30 }}
          onClick={handleClick}
        >
          Kích hoạt ngay
        </Button>
      </Box>
    </Box>
  );
}
