import {
  Avatar,
  Badge,
  Box,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListSubheader,
  <PERSON>ack,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";
import { useSelector } from "react-redux";
import FrameContainer from "../../components/layout/Container";
import PopupEditInfo from "../../components/UI/PopupEditInfo";
import PopupView from "../../components/UI/PopupView";
import { RootState } from "../../redux/store";
import { Icon } from "@/constants/Assets";
import dayjs from "dayjs";
import { Level, LevelName } from "@/constants/Const";

export default function Info() {
  const { user, userZalo } = useSelector((state: RootState) => state.auth);

  const listInfo = [
    {
      label: "<PERSON><PERSON><PERSON> tháng năm sinh",
      value: user?.dob,
    },
    {
      label: "<PERSON><PERSON> điện thoại",
      value: user?.phone,
    },
    {
      label: "Đ<PERSON>a chỉ",
      value: user?.address,
    },
    {
      label: "Email",
      value: user?.email,
    },
  ];

  const listJobs = [
    {
      label: "Ngày gia nhập",
      value: dayjs(user?.createdAt).format("DD/MM/YYYY"),
    },
    {
      label: "Vị trí công việc",
      value: user?.level ? LevelName[user?.level] : "Cộng tác viên",
    },
  ];
  const renderList = (list, title) => {
    return (
      <Stack
        style={{
          background: "#fff",
          padding: 15,
          marginBlock: 12,
          borderRadius: 10,
        }}
        gap={1}
      >
        <Typography fontWeight={700}>{title}</Typography>
        <Divider />
        <Stack gap={1}>
          {list?.map((item, index) => (
            <React.Fragment key={index}>
              <Stack width="100%" gap={1}>
                <Stack direction="row" justifyContent="space-between">
                  <Typography color={"#969595"}>{item.label}</Typography>
                  <Typography>{item.value || "Chưa cập nhật"}</Typography>
                </Stack>
              </Stack>
              {index !== list.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </Stack>
      </Stack>
    );
  };
  return (
    <FrameContainer title="Chi tiết cá nhân">
      <Stack justifyContent={"center"} alignItems={"center"} gap={1}>
        <Avatar
          sx={{ width: "80px", height: "80px" }}
          src={userZalo?.avatar || user?.avatarUrl || Icon.avatarDefault}
        />

        <Typography fontSize={14} fontWeight={700}>
          {userZalo?.name || user?.name}
        </Typography>
      </Stack>
      {renderList(listInfo, "Thông tin cá nhân")}
      {renderList(listJobs, "Thông tin công việc")}
      <Stack direction="row" justifyContent="center">
        <PopupEditInfo />
      </Stack>
    </FrameContainer>
  );
}
