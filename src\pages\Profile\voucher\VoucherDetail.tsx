import { <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material";
import React, { useEffect } from "react";
import FrameContainerFull from "../../../components/layout/ContainerFluid";
import { useLocation, useNavigate } from "react-router-dom";
import VoucherItem from "../../../components/discount/VourcherItem";
import { COLORS } from "../../../constants/themes";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../redux/store";
import {
  getMyVoucherList,
  redeemPointToVoucher,
  setCurVoucher,
} from "../../../redux/slices/voucher/voucherSlice";
import { ERROR_MESSAGE } from "../../../constants/Const";
import { Icon } from "../../../constants/Assets";
import { getUser } from "../../../redux/slices/authen/authSlice";
import { useAlert } from "../../../redux/slices/alert/useAlert";
import { showToast } from "../../../utils/common";
import { Router } from "../../../constants/Route";

export default function VoucherDetail() {
  const { state } = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { myVoucherList } = useSelector((state: RootState) => state.vouchers);
  const voucher = state?.voucher;
  const { showAlert } = useAlert();
  const bannerUri =
    voucher.banner?.formats?.thumbnails?.url ||
    voucher.banner?.formats?.small?.url ||
    voucher.banner?.url;

  useEffect(() => {
    dispatch(getMyVoucherList());
  }, []);

  const isHaveInMyVoucher = myVoucherList?.find(
    (item) => item.id === voucher.id
  );

  const onChangePointToVoucher = async () => {
    const res = await dispatch(
      redeemPointToVoucher({
        voucherId: voucher.id,
      })
    ).unwrap();
    if (res.data) {
      if (res.message) {
        showAlert({
          title: ERROR_MESSAGE[res.message],
          content: "Xem lại tại kho voucher",
          icon: Icon.warning,
        });
      } else {
        dispatch(getUser());
        dispatch(getMyVoucherList());
        showAlert({
          title: voucher?.matchPoint
            ? `Bạn đã đổi ${voucher?.matchPoint} điểm lấy voucher thành công`
            : "Bạn đã lưu voucher thành công",
          content: "Xem lại tại kho voucher của mình",
        });
      }
    } else {
      showToast({
        content:
          ERROR_MESSAGE[res.message] ||
          "Đổi voucher không thành công, vui lòng thử lại sau",
        type: "error",
      });
    }
  };

  const onApplyClick = () => {
    showAlert({
      title: voucher?.matchPoint ? "Đổi điểm lấy Voucher" : "Lưu Voucher",
      content: voucher?.matchPoint
        ? `Bạn có muốn dùng ${voucher?.matchPoint} điểm để đổi voucher này không?`
        : "Bạn có chắc chắn muốn lưu voucher này không?",
      icon: Icon.check,
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: onChangePointToVoucher,
        },
      ],
    });
  };

  return (
    <FrameContainerFull
      title="Thông tin voucher"
      overrideStyle={{
        background: COLORS.neutral10,
        height: "100vh",
      }}
    >
      {voucher.banner ? (
        voucher.banner?.formats ? (
          <img
            style={styles.banner}
            src={
              bannerUri
                ? `${import.meta.env.VITE_API_URL}${bannerUri}`
                : "/images/bg2.png"
            }
          />
        ) : (
          <video muted loop controls style={{ width: "100%", aspectRatio: 1 }}>
            <source
              src={`${import.meta.env.VITE_API_URL}${voucher?.banner?.url}`}
            />
          </video>
        )
      ) : (
        <img style={styles.banner} src={"/images/bg2.png"} />
      )}
      <Stack margin={2}>
        {voucher && (
          <Stack className="voucher-profile">
            <VoucherItem item={voucher} isDetail={true} />
          </Stack>
        )}
        <Stack bgcolor={"#fff"} p={2} marginBlock={2} marginBottom={8}>
          <Typography style={{ color: COLORS.primary2, fontWeight: 700 }}>
            Thông tin ưu đãi
          </Typography>
          <Typography>{voucher?.content}</Typography>
        </Stack>
      </Stack>
      <Stack className="bottomElementPreventScroll" style={styles.bottomBtn}>
        <Button
          style={{
            marginLeft: 10,
            width: "95%",
            padding: 12,
            borderRadius: 99,
            background: isHaveInMyVoucher ? COLORS.primary1 : COLORS.primary,
            color: "#fff",
          }}
          onClick={() => {
            if (isHaveInMyVoucher) {
              dispatch(setCurVoucher(voucher));
              navigate(`${Router.product}`);
            } else {
              onApplyClick();
            }
          }}
        >
          {isHaveInMyVoucher
            ? "Dùng ngay"
            : voucher?.matchPoint
            ? "Đổi điểm lấy Voucher"
            : "Lưu"}
        </Button>
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  banner: {
    marginBottom: -80,
    objectFit: "cover",
    width: "100%",
  },
  bottomBtn: {
    position: "fixed",
    bottom: 72,
    width: "100%",
    zIndex: 100,
  },
};
