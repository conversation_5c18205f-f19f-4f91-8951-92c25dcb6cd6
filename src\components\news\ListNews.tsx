import React, { memo } from "react";
import { Grid } from "@mui/material";

import { INews } from "../../types/news";
import NewsItem from "./NewsItem";

const ListNews = ({ list = [] }: { list: INews[] }) => {
  if (!list?.length) return null;

  return (
    <Grid container spacing={1} paddingBlock={1}>
      {list.map((news: INews, index) => (
        <NewsItem item={news} key={String(index)} />
      ))}
    </Grid>
  );
};

export default memo(ListNews);
