import React, { useEffect } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { Container, Stack, Typography } from "@mui/material";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";
import { ColabStatus, Level } from "@/constants/Const";
import { APP_NAME } from "@/constants/AppInfo";
import { COLORS } from "@/constants/themes";

export default function PopupNotificationCollabNeedRegister() {
  const [open, setOpen] = React.useState(false);
  const user = useSelector((state: RootState) => state.auth.user);
  const navigate = useNavigate();
  useEffect(() => {
    if (user?.colabStatus === ColabStatus.Not_Waiting) {
      setOpen(true);
    }
  }, [user]);
  const handleClose = async (event, reason) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };
  const closeModal = () => {
    setOpen(false);
  };
  const handleBack = () => {
    closeModal();
    navigate(Router.homepage);
  };
  const handleRegister = () => {
    closeModal();
    navigate(Router.collab.collabRegister);
  };
  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        disableEscapeKeyDown={false}
        style={{ borderRadius: 10 }}
        PaperProps={{
          style: {
            width: "100%",
            position: "absolute",
            bottom: 0,
            margin: 0,
            borderRadius: "20px 20px 0px 0px",
            boxShadow: "none",
          },
        }}
      >
        <DialogContent>
          <Container>
            <Stack justifyContent={"center"} alignItems={"center"} gap={2}>
              <img width={"60%"} src="/images/big-logo.png" alt="" />
              <Typography
                textAlign={"center"}
                color={"#1D1D5E"}
                fontSize={18}
                fontWeight={700}
              >
                Tính năng cần kích hoạt tài khoản
              </Typography>
              <Typography textAlign={"center"} fontSize={13} color={"#666666"}>
                Để sử dụng tính năng này bạn cần kích hoạt tài khoản cộng tác
                viên để trở thành đối tác bán hàng của {APP_NAME.toUpperCase()}
              </Typography>
              <Stack direction={"row"} gap={2}>
                <Button
                  variant="contained"
                  disableElevation
                  style={{
                    color: "#fff",
                    height: 40,
                    fontSize: "16px",
                    fontWeight: 700,
                    lineHeight: "19.36px",
                    borderRadius: 40,
                    background: "#82C3FFF5",
                    minWidth: "150px",
                  }}
                  onClick={handleBack}
                >
                  Để sau
                </Button>
                <Button
                  variant="contained"
                  disableElevation
                  style={{
                    color: "#fff",
                    height: 40,
                    fontSize: "16px",
                    fontWeight: 700,
                    lineHeight: "19.36px",
                    borderRadius: 40,
                    background: COLORS.primary,
                    minWidth: "150px",
                  }}
                  onClick={handleRegister}
                >
                  Kích hoạt ngay
                </Button>
              </Stack>
            </Stack>
          </Container>
        </DialogContent>
      </Dialog>
    </>
  );
}
