import { COLORS, commonStyle, theme } from "@/constants/themes";
import ProfileHeader from "@/pages/Profile/ProfileHeader";
import { useNavigate } from "@/utils/component-util";
import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { HomeTabs, HomeTabType } from "@/constants/Const";
import HomeTabItem from "./HomeTabItem";
import { Router } from "@/constants/Route";
import { showToast } from "@/utils/common";
import { openChat } from "@/utils/openChat";
import { clearItem } from "@/utils/storage";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { Icon } from "@/constants/Assets";

export default function UserProfile() {
  const navigate = useNavigate();
  const { showAlert } = useAlert();
  const { user } = useSelector((state: RootState) => state.auth);

  const onClickToHomeTabItem = ({ id }: { id: number }) => {
    switch (id) {
      case HomeTabType.Academy:
        showAlert({
          icon: Icon.check,
          title: "Thông báo",
          content: `Tính năng đang được phát triển!`,
          buttons: [
            {
              title: "OK",
              action: () => {},
            },
          ],
        });
        break;
      case HomeTabType.Ai:
        showAlert({
          icon: Icon.check,
          title: "Thông báo",
          content: `Tính năng đang được phát triển!`,
          buttons: [
            {
              title: "OK",
              action: () => {},
            },
          ],
        });
        break;
      case HomeTabType.AccumulatePoint:
        navigate(Router.point);
        break;
      case HomeTabType.OrderHistory:
        navigate(Router.order.index);
        break;
      case HomeTabType.Contact:
        openChat();
        break;
    }
  };

  const handleClearStorage = () => {
    clearItem();
    showToast({
      content: "clear all storage",
      type: "success",
    });
  };
  return (
    <>
      <Box
        sx={{
          width: "100%",
          border: '1px solid #007AFF',
        }}
        style={{
          ...commonStyle.shadowBorder,
          ...styles.container,
          marginBottom: "10px",
        }}
      >
        {user && <ProfileHeader />}
        {/* {import.meta.env.DEV || import.meta.env.TEST ? (
          <Button
            style={{ background: "#969595" }}
            onClick={handleClearStorage}
          >
            <Typography style={{ color: "black" }}> clearStorage</Typography>
          </Button>
        ) : null} */}
        <Box sx={styles.homeTabContainerTop}>
          {HomeTabs.First.map((item) => (
            <Button
              style={{ width: "25%" }}
              key={item.id}
              onClick={() => onClickToHomeTabItem(item)}
            >
              <HomeTabItem {...item} />
            </Button>
          ))}
        </Box>
      </Box>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: 10,
    padding: 12,
    position: "relative",
  },
  devText: {
    position: "absolute",
    top: 60,
    right: 10,
    backgroundColor: "red",
  },
  homeTabContainerTop: {
    display: "flex",
    paddingTop: 1,
    paddingBottom: 1.1,
    alignItems: "center",
    justifyContent: "space-between",
  },
  homeTabContainerBottom: {
    display: "flex",
    paddingTop: 2,
    paddingInline: 1,
    alignItems: "flex-start",
  },
  activeCollabContainer: {
    display: "flex",
    background: theme.palette.primary.main,
    fontWeight: 700,
    gap: 1,
    borderRadius: 1,
    padding: 2,
    marginTop: 3,
    alignItems: "center",
    justifyItems: "baseline",
    justifyContent: "space-around",
  },
  activeBtn: {
    background: "#FFF",
    color: theme.palette.primary.main,
    borderRadius: 5,
    fontSize: 12,
    minWidth: 120,
  },
  OAContainer: {
    background: "#FFF9ED",
    fontWeight: 700,
    gap: 1,
    padding: 2,
    marginTop: 2,
    alignItems: "center",
    justifyItems: "baseline",
    justifyContent: "space-around",
    border: 1,
    borderRadius: 1,
    borderColor: theme.palette.primary.main,
  },
  OAContent: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 1,
  },
  OABtn: {
    background: theme.palette.primary.main,
    color: "#FFF",
    borderRadius: 5,
    fontSize: 12,
    width: 100,
  },
  cateItemContainer: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  productionTitleContainer: {
    display: "flex",
    paddingBlock: 1,
    marginTop: 1,
    justifyContent: "space-between",
    alignItems: "center",
  },
  seeMoreBtn: {
    display: "flex",
    alignItems: "center",
    color: COLORS.neutral2,
    background: COLORS.neutral9,
    fontSize: 12,
    fontWeight: 400,
  },
  notificationContainer: {
    background: "#FFF9ED",
    display: "flex",
    gap: 1,
    borderRadius: 20,
    paddingBlock: 1,
    paddingInline: 2,
    marginBlock: 3,
    alignItems: "center",
    justifyItems: "baseline",
    justifyContent: "space-between",
    border: 1,
    borderColor: theme.palette.primary.main,
  },
};
