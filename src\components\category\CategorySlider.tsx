import React from "react";
import Slider from "react-slick";
import CategoryItem from "./CategoryItem";
import { Icon } from "@/constants/Assets";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { COLORS, commonStyle } from "@/constants/themes";
import { Box, Typography } from "@mui/material";
import GradientText from "../UI/GradientTextProps";

export default function CategorySlider() {
  const { productCategoryList: productCategoryList } = useSelector(
    (state: RootState) => state.product
  );

  const sliderListing = {
    dots: false,
    infinite: false,
    speed: 1000,
    slidesToShow: 4,
    slidesToScroll: 4,
    initialSlide: 0,
    arrows: false,
  };
  if (productCategoryList?.length <= 4) {
    return (
      <Box
        sx={{
          background: COLORS.white,
          paddingInline: 1,
          paddingBlock: 1,
          marginBottom: 1,
        }}
      >
        <GradientText
          text="Danh mục sản phẩm"
          style={{
            fontWeight: 700,
            fontSize: "18px",
            marginBottom: "20px",
          }}
        />
        <div
          style={{
            display: "flex",
            gap: 20,
          }}
        >
          {productCategoryList?.map((category) => (
            <CategoryItem key={category.id} item={category} />
          ))}
        </div>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        background: COLORS.white,
        paddingInline: 2,
        paddingBlock: 1,
        marginBottom: 1,
      }}
    >
      <GradientText
        text="Danh mục sản phẩm"
        style={{
          fontWeight: 700,
          fontSize: "18px",
          marginBottom: "10px",
        }}
      />
      <Slider
        {...sliderListing}
        style={{
          borderRadius: "10px",
          marginTop: "15px",
        }}
      >
        {productCategoryList?.map((category) => (
          <CategoryItem key={category.id} item={category} />
        ))}
      </Slider>
    </Box>
  );
}
