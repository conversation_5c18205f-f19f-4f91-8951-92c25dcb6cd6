import React, { useState } from "react";
import FrameContainer from "../components/layout/Container";
import { <PERSON><PERSON>, Stack, Box } from "@mui/material";
import { joinTeam } from "../redux/slices/team/team";
import { useTheme } from "@mui/material/styles";
import { getUser } from "../redux/slices/authen/authSlice";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../redux/store";
import { showToast } from "../utils/common";
import { useNavigate } from "../utils/component-util";
import { COLORS } from "@/constants/themes";

const EnterCode = () => {
  const [inputValue, setInputValue] = useState<string>("");
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const handleConfirmOferCode = async () => {
    try {
      const res: any = await dispatch(joinTeam(inputValue));
      console.log("joinTeam", res);

      if (res.error) {
        showToast({
          content:
            res.error?.message ?? "Quá trình cập nhật lỗi. Vui lòng thử lại",
          type: "error",
        });
        return;
      }
      showToast({
        content: "Bạn đã cập nhật mã giới thiệu thành công",
        type: "success",
      });
      dispatch(getUser());
      navigate(-1);
    } catch (e: any) {
      showToast({
        content: e?.message ?? "Quá trình cập nhật lỗi. Vui lòng thử lại",
        type: "error",
      });
    }
  };

  return (
    <FrameContainer title="Nhập mã giới thiệu" style={{ paddingTop: "5px" }}>
      <Stack
        style={{ background: "#fff" }}
        padding={2}
        borderRadius={4}
        marginBlock={1}
      >
        <span
          style={{
            fontWeight: 700,
            fontSize: 16,
            marginBottom: 12,
            color: theme.palette.primary.main,
          }}
        >
          Vui lòng nhập mã giới thiệu của bạn:
        </span>
        <form action="">
          <input
            placeholder="Nhập mã giới thiệu"
            style={{
              border: "none",
              borderRadius: "10px",
              background: "#F4F4F4",
              color: "#959595",
              padding: "20px",
              marginTop: 12,
              marginBottom: 12,
              fontSize: 16,
              fontWeight: 500,
            }}
            value={inputValue}
            onChange={handleChange}
          />
          <Button
            size="large"
            style={{
              color: "#FFF",
              background: COLORS.primary,
              borderRadius: "99px",
              margin: "auto",
              width: "fit-content",
              paddingLeft: "40px",
              paddingRight: "40px",
            }}
            onClick={handleConfirmOferCode}
          >
            Xác nhận
          </Button>

          {showSuccessPopup && (
            <Box className="popup">
              <Box className="popup-content">
                <p>Nhập mã giới thiệu thành công!</p>
                <button onClick={() => setShowSuccessPopup(false)}>
                  Xác nhận
                </button>
              </Box>
            </Box>
          )}

          {showErrorPopup && (
            <Box className="popup">
              <Box className="popup-content">
                <p>Có lỗi xảy ra, vui lòng thử lại sau.</p>
                <button onClick={() => setShowErrorPopup(false)}>
                  Xác nhận
                </button>
              </Box>
            </Box>
          )}
        </form>
      </Stack>
    </FrameContainer>
  );
};

export default EnterCode;
