import { Box, Container, Grid, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { Header } from "zmp-ui";
import styles from "../../css/styles.module.css";
import { COLORS } from "@/constants/themes";

export default function FrameContainerFull({
  children,
  title,
  overrideStyle,
}: {
  children: ReactNode;
  title: string;
  overrideStyle?: React.CSSProperties;
}) {
  const handleScroll = (e) => {
    const bottom =
      e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };
  return (
    <Box style={{ backgroundColor: "#F2F3F5", ...overrideStyle }}>
      <Header
        textColor="#fff"
        style={{
          background: COLORS.colorHeader,
        }}
        title={title}
      />
      <Box className={styles.pageContent}>
        <Box
          onScroll={handleScroll}
          style={{ paddingInline: 0, paddingBottom: 64 }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
}
