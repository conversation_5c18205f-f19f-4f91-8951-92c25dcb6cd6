import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IAddress } from "../../../types/address";

interface AuthState {
  list: IAddress[] | null;
  currentAddress: IAddress | null;
  isLoading: boolean;
  province: [];
  district: [];
  ward: [];
  bank: [];
}

const initialState: AuthState = {
  list: null,
  currentAddress: null,
  isLoading: true,
  province: [],
  district: [],
  ward: [],
  bank: [],
};

export const getAddressList = createAsyncThunk(
  "address/getAddressList",
  async () => {
    const response: any = await request("get", "/api/my/address");
    return response;
  }
);

export const createAddress = createAsyncThunk(
  "address/createAddress",
  async (data: any) => {
    const response: any = await request("post", "/api/my/address", {
      data,
    });

    return response;
  }
);

export const getCurrentAddress = createAsyncThunk(
  "address/getCurrentAddress",
  async (id: any) => {
    const response: any = await request("get", `/api/my/address/${id}`);

    return response;
  }
);

export const getProvince = createAsyncThunk(
  "address/getProvince",
  async (code?: string) => {
    let query = "";
    if (code) {
      query = `?code=${code}`;
    }
    const response: any = await request(
      "get",
      `/api/address-config/province${query}`
    );
    return response;
  }
);

export const getDistrict = createAsyncThunk(
  "address/getDistrict",
  async (data?: { code?: string; parent_code: string }) => {
    let query = "";
    if (data?.code) {
      query = `?code=${data.code}`;
    }
    if (data?.parent_code) {
      query = `?parent_code=${data.parent_code}`;
    }
    const response: any = await request(
      "get",
      `/api/address-config/district${query}`
    );
    return response;
  }
);

export const getWard = createAsyncThunk(
  "address/getWard",
  async (data?: { code?: string; parent_code: string }) => {
    let query = "";
    if (data?.code) {
      query = `?code=${data.code}`;
    }
    if (data?.parent_code) {
      query = `?parent_code=${data.parent_code}`;
    }
    const response: any = await request(
      "get",
      `/api/address-config/ward${query}`
    );
    return response;
  }
);

export const getBank = createAsyncThunk(
  "address/getBank",
  async (shortName?: string) => {
    let query = "";
    if (shortName) {
      query = `?shortName=${shortName}`;
    }
    const response: any = await request(
      "get",
      `/api/address-config/bank${query}`
    );
    return response;
  }
);

const addressSlice = createSlice({
  name: "address",
  initialState,
  reducers: {
    setCurrentAddress: (state, action: PayloadAction<IAddress>) => {
      state.currentAddress = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAddressList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getAddressList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          state.list = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getAddressList.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(createAddress.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        createAddress.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
        }
      )
      .addCase(createAddress.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(getCurrentAddress.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getCurrentAddress.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.isLoading = false;
          if (!payload?.data?.id) {
            return;
          }

          state.currentAddress = {
            id: payload.data.id,
            ...payload.data,
          };
        }
      )
      .addCase(getCurrentAddress.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProvince.fulfilled, (state, action) => {
        const { payload } = action;

        state.province = payload;
      })
      .addCase(getDistrict.fulfilled, (state, action) => {
        const { payload } = action;
        state.district = payload;
      })
      .addCase(getWard.fulfilled, (state, action) => {
        const { payload } = action;
        state.ward = payload;
      })
      .addCase(getBank.fulfilled, (state, action) => {
        const { payload } = action;
        state.bank = payload;
      });
  },
});

export const { setCurrentAddress } = addressSlice.actions;
export default addressSlice.reducer;
