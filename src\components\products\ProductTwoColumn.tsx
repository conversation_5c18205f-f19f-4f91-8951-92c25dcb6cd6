import React, { useEffect } from "react";
import { Grid, Stack } from "@mui/material";
import { IProduct } from "../../types/product";
import ProductItem from "./ProductItem";

const ProductTwoColumn: React.FC<any> = ({ productList }) => {
  return (
    <Grid container spacing={1}>
      {productList && productList.length > 0 ? (
        productList.map((item: IProduct) => (
          <Grid item xs={6} key={item.id} style={{ textAlign: "left" }}>
            <ProductItem item={item} isShowAdd={true} />
          </Grid>
        ))
      ) : (
        <Stack direction="row" justifyContent={"center"} padding={2}>
          Không có sản phẩm nào
        </Stack>
      )}
    </Grid>
  );
};

export default ProductTwoColumn;
