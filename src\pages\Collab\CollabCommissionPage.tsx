import FrameContainer from "@/components/layout/Container";
import React, { useEffect } from "react";
import { Stack, Typography } from "@mui/material";
import { COLORS } from "@/constants/themes";
import { useSelector } from "react-redux";
import store, { RootState } from "@/redux/store";
import CollapseCommission from "@/components/collab/Commision/CollapseCommission";
import CommissionItem from "@/components/collab/Commision/CommissionItem";
import InfoUser from "@/components/InfoUser";
import { ICommissionSale } from "@/types/team";
import { formatPrice } from "@/utils/formatPrice";
import { getReport } from "@/redux/slices/team/team";

export default function CollabCommissionPage() {
  const { user } = useSelector((state: RootState) => state.auth);
  const { teamReport } = useSelector((state: RootState) => state.team);
  const totalMyCommission =
    parseInt(teamReport?.totalCommissionValue?.totalMyCommission) || 0;
  const totalManagementCommissionValue =
    parseInt(
      teamReport?.totalCommissionValue?.totalManagementCommissionValue
    ) || 0;
  const totalFixedCommission =
    parseInt(teamReport?.totalCommissionValue?.totalFixedCommission) || 0;
  const quarterCommission = parseInt(user?.commission?.quarterCommission) || 0;
  const sharedCommission = parseInt(user?.commission?.sharedCommission) || 0;
  const dataTotalCommission: ICommissionSale = {
    totalCommissionF1: {
      commission: totalMyCommission,
      sales: parseInt(user?.mySale) || 0,
    },
    totalCommissionF2: {
      commission: user?.commission.totalCommission,
      sales: parseInt(user?.myTeamSale) || 0,
    },
    totalFixedCommission: totalFixedCommission,
  };

  const totalCommission = user?.commission.totalCommission;
  useEffect(() => {
    store.dispatch(getReport());
  }, []);
  return (
    <FrameContainer
      title="Hoa hồng cộng tác viên"
      style={{ background: "#FFF" }}
    >
      <Stack
        style={{
          borderRadius: 10,
          marginBlock: 12,
          background: "#fff",
          padding: 12,
          boxShadow: "0px 0px 4px 0px #00000040",
        }}
      >
        <InfoUser />
      </Stack>

      <Stack style={styles.saleContainer}>
        <Typography style={{ ...styles.whiteText, fontSize: 18 }}>
          Tổng thu nhập
        </Typography>
        <Typography style={{ ...styles.whiteText, fontSize: 30 }}>
          {formatPrice(totalCommission)}
        </Typography>
      </Stack>
      <CollapseCommission
        title="Hoa hồng trực tiếp/gián tiếp"
        isFirstOpen
        data={dataTotalCommission}
      />
      <CommissionItem title="Hoa hồng thưởng" value={quarterCommission} />
      <CommissionItem title="Hoa hồng chia" value={sharedCommission} />
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: 10,
    background: "#fff",
    padding: 12,
    boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
  },
  saleContainer: {
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 10,
    background: COLORS.primary,
    padding: 12,
    boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
    alignItems: "center",
    justifyContent: "center",
  },
  whiteText: {
    color: "#FFF",
    fontWeight: 700,
  },
};
