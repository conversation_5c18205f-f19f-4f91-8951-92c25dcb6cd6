import React from "react";
import { Env, OrderStatusText } from "../constants/Const";
import { theme } from "../constants/themes";
import { formatPrice } from "../utils/formatPrice";
import { Box, Button, Stack, Typography } from "@mui/material";
import { useNavigate } from "../utils/component-util";
import { Router } from "../constants/Route";
import dayjs from "dayjs";
import moment from "moment/min/moment-with-locales";
import GradientText from "@/components/UI/GradientTextProps";

export default function OrderItem({ order }) {
  moment.locale("vi");
  const navigate = useNavigate();
  const firstProduct = order.orderData.items[0];
  const url =
    firstProduct.product?.image?.[0]?.url ||
    Env.BackendUrl + firstProduct.product?.image?.data?.[0]?.attributes?.url;

  return (
    <Stack
      key={order.id}
      style={{ background: "#fff" }}
      padding={2}
      borderRadius={4}
      marginBlock={1}
      onClick={() => {
        navigate(`${Router.order.index}/${order.id}`);
      }}
    >
      <Stack
        direction="row"
        justifyContent={"space-between"}
        alignItems={"center"}
      >
        <GradientText
          style={{
            fontWeight: 700,
            fontSize: 16,
          }}
          text={order.receiver.userName}
        />
        <Typography fontSize={12} fontWeight={400}>
          {moment(order?.createdAt).fromNow()}
        </Typography>
      </Stack>
      <Stack
        direction="row"
        gap={2}
        mt={2}
        alignItems={"center"}
        key={firstProduct.product.id}
      >
        <img
          width={60}
          height={60}
          style={{ objectFit: "contain" }}
          src={url}
          alt=""
        />
        <Stack width={"100%"} gap={1}>
          <Typography fontSize={"14px"} fontWeight={400} color={"#252627"}>
            {firstProduct.product.name}
          </Typography>
          {/* {firstProduct?.promotion ? (
            <Box>
              <Typography
                fontSize={"12px"}
                fontWeight={700}
                color={theme.palette.primary.main}
                mb={0.2}
              >
                Quà tặng
              </Typography>
              <Typography fontSize={12}>
                {firstProduct?.promotion?.product?.name}
                <span
                  style={{
                    color: theme.palette.primary.main,
                    fontWeight: "700",
                    paddingLeft: "5px",
                    fontSize: "12px",
                  }}
                >
                  x
                  {Number(firstProduct?.promotion.quantity) *
                    Number(firstProduct.quantity)}
                </span>
              </Typography>
            </Box>
          ) : null} */}

          <Stack
            direction={"row"}
            alignItems={"center"}
            justifyContent={"space-between"}
          >
            <GradientText
              style={{
                fontWeight: 700,
                fontSize: 16,
              }}
              text={`${formatPrice(
                firstProduct.product.price - firstProduct.product?.discount || 0
              )} x ${firstProduct.quantity}`}
            />
            <Typography fontSize={12} color={"#26C9CE"} fontWeight={700}>
              {OrderStatusText[order.orderStatus]}
            </Typography>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  );
}
