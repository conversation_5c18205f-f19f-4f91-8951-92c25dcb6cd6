import CryptoJS from "crypto-js";
import { PASSWORD_GENERATE_SECRET_KEY } from "./constants/AppInfo";

// Khởi tạo khóa và IV dùng để mã hóa
const key = CryptoJS.enc.Utf8.parse(PASSWORD_GENERATE_SECRET_KEY);
const iv = CryptoJS.enc.Utf8.parse("");

export const genPass = (phone: string): string => {
  // Mã hóa số điện thoại sử dụng AES
  const encryptedPassword = CryptoJS.AES.encrypt(phone, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  }).toString();
  return encryptedPassword;
};
