import { configureStore } from "@reduxjs/toolkit";
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from "redux-persist";

import authReducer from "./slices/authen/authSlice";
import productReducer from "./slices/product/productSlice";
import addressReducer from "./slices/address/addressSlice";
import productListReducer from "./slices/product/productListSlice";
import productCommentReducer from "./slices/product/productCommentSlice";
import orderReducer from "./slices/order/orderSlice";
import cartReducer from "./slices/cart/cartSlice";
import teamReducer from "./slices/team/team";
import myVoucherReducer from "./slices/voucher/myVoucherSlice";
import newsListReducer from "./slices/news/newsListSlice";
import notificationReducer from "./slices/notification/notificationSlice";
import alertReducer from "./slices/alert/alert";
import termReducer from "./slices/term/termSlice";
import configReducer from "./slices/configSlice";
import paymentReducer from "./slices/payment/paymentSlice";
import { persistConfig } from "./Store.persist";
import promotionReducer from "./slices/promotion/promotionSilice";
import warehouseReducer from "./slices/warehouse/warehouseSlice";
import commonReducer from "./slices/common/commonSlice";
import voucherReducer from "./slices/voucher/voucherSlice";
import loadingReducer from "./slices/loading/loadingSlice";

const persistedNewsReducer = persistReducer(persistConfig, newsListReducer);

const store = configureStore({
  reducer: {
    auth: authReducer,
    product: productReducer,
    productComment: productCommentReducer,
    address: addressReducer,
    productList: productListReducer,
    order: orderReducer,
    cart: cartReducer,
    team: teamReducer,
    myVouchers: myVoucherReducer,
    news: persistedNewsReducer,
    alert: alertReducer,
    notification: notificationReducer,
    term: termReducer,
    config: configReducer,
    payment: paymentReducer,
    promotion: promotionReducer,
    warehouse: warehouseReducer,
    common: commonReducer,
    vouchers: voucherReducer,
    loading: loadingReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          "alert/setAlert",
          FLUSH,
          REHYDRATE,
          PAUSE,
          PERSIST,
          PURGE,
          REGISTER,
        ],
        ignoredPaths: ["alert.alert"],
      },
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
