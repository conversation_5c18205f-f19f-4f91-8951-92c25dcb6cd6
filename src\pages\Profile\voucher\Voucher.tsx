import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import {
  FormControl,
  InputAdornment,
  Stack,
  TextField,
  RadioGroup,
  Box,
  CircularProgress,
} from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getMyVoucherList,
  getVoucherList,
  redeemPointToVoucher,
} from "../../../redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "../../../redux/store";
import { IVoucher } from "../../../types/voucher";
import { useNavigate } from "../../../utils/component-util";
import VoucherItem from "../../../components/discount/VourcherItem";
import { useDebounce } from "use-debounce";
import { removeMark, showToast } from "../../../utils/common";
import { useAlert } from "../../../redux/slices/alert/useAlert";
import { Icon } from "../../../constants/Assets";
import { ERROR_MESSAGE } from "../../../constants/Const";
import { Router } from "../../../constants/Route";
import NoDataView from "../../../components/UI/NoDataView";
import { getUser } from "../../../redux/slices/authen/authSlice";
import { useLocation } from "react-router-dom";
import FrameContainerNoIcon from "../../../components/layout/ContainerNoIconHeader";

export default function Voucher() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { state } = useLocation();
  const user = useSelector((state: RootState) => state.auth.user);
  const [showClearIcon, setShowClearIcon] = useState("none");
  const { voucherList, isLoading, myVoucherList } = useSelector(
    (state: RootState) => state.vouchers
  );
  const [voucherSelected, setVoucherSelected] = useState<IVoucher | null>();
  const [searchKey, setSearchKey] = useState("");
  const [debounceValue] = useDebounce(searchKey, 1000);
  const { showAlert } = useAlert();

  useEffect(() => {
    dispatch(getVoucherList());
  }, [user]);

  useEffect(() => {
    if (state && state.valueSearch) {
      setSearchKey(state.valueSearch);
    }
  }, [state]);

  useEffect(() => {
    if (!myVoucherList && user) {
      // dispatch(getMyVoucherList());
    }
  }, [user]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  const handleClick = (): void => {
    setSearchKey("");
  };

  const handleSelectVoucher = (item: IVoucher) => {
    setVoucherSelected(item);
  };

  const onChangePointToVoucher = async (voucherID) => {
    const res = await dispatch(
      redeemPointToVoucher({
        voucherId: voucherID,
      })
    ).unwrap();
    if (res.data) {
      if (res.message) {
        showAlert({
          title: ERROR_MESSAGE[res.message],
          content: "Xem lại tại kho voucher",
          icon: Icon.warning,
        });
      } else {
        dispatch(getUser());
        dispatch(getMyVoucherList());
        showAlert({
          title: voucherSelected?.matchPoint
            ? `Bạn đã đổi ${voucherSelected?.matchPoint} điểm lấy voucher thành công`
            : "Bạn đã lưu voucher thành công",
          content: "Xem lại tại kho voucher của mình",
        });
      }
    } else {
      showToast({
        content:
          ERROR_MESSAGE[res.message] ||
          "Đổi voucher không thành công, vui lòng thử lại sau",
        type: "error",
      });
    }
    setVoucherSelected(null);
  };

  const onApplyClick = (voucherSelect) => {
    showAlert({
      title: voucherSelect?.matchPoint ? "Đổi điểm lấy Voucher" : "Lưu Voucher",
      content: voucherSelect?.matchPoint
        ? `Bạn có muốn dùng ${voucherSelect?.matchPoint} điểm để đổi voucher này không?`
        : "Bạn có chắc chắn muốn lưu voucher này không?",
      icon: Icon.check,
      buttons: [
        {
          title: "Huỷ",
          action: () => {
            setVoucherSelected(null);
          },
        },
        {
          title: "Xác nhận",
          action: () => {
            onChangePointToVoucher(voucherSelect.id);
          },
        },
      ],
    });
  };

  const filteredVoucherList = useMemo(() => {
    let debounceValueSearchValue = debounceValue;
    if (state && state.valueSearch) {
      debounceValueSearchValue = searchKey;
    }
    return debounceValueSearchValue && voucherList.length
      ? voucherList?.filter((voucher: IVoucher) => {
          const removeMarkVoucherCode = removeMark(voucher.code).toLowerCase();
          const removeMarkSearchKey = removeMark(
            debounceValueSearchValue
          ).toLowerCase();
          return removeMarkVoucherCode.includes(removeMarkSearchKey);
        })
      : voucherList || [];
  }, [debounceValue, voucherList, state]);

  return (
    <Box>
      <FrameContainerNoIcon
        title="Kho Voucher"
        overrideStyle={{ background: "#e9ebed" }}
      >
        <Stack sx={styles.contentContainer}>
          <Stack direction={"row"} gap={2} justifyContent="space-between">
            <FormControl style={{ flexGrow: 1 }}>
              <TextField
                placeholder="Nhập mã ưu đãi"
                className="search-input"
                style={{ width: "100%" }}
                size="small"
                variant="outlined"
                onChange={handleChange}
                value={searchKey}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      {<SearchIcon />}
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment
                      position="end"
                      style={{ display: showClearIcon }}
                      onClick={handleClick}
                    >
                      <ClearIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </FormControl>
          </Stack>
          {isLoading ? (
            <Stack sx={styles.loadingContainer}>
              <CircularProgress />
            </Stack>
          ) : (
            <Stack className="voucher-profile" width={"100%"} marginBlock={2}>
              <RadioGroup
                row
                aria-labelledby="demo-form-control-label-placement"
                name="position"
                defaultValue="top"
              >
                {filteredVoucherList?.length > 0 ? (
                  filteredVoucherList?.map((item: IVoucher) => (
                    <Box sx={{ paddingBottom: 2, width: "100%" }} key={item.id}>
                      <VoucherItem
                        item={item}
                        onSelectVoucher={() => handleSelectVoucher(item)}
                        isShowMatchPoint={!!item.matchPoint}
                        onNavigateToDetail={() => {
                          navigate(`${Router.voucher.index}/${item.id}`, {
                            state: { voucher: item },
                          });
                        }}
                        isChecked={item.id === voucherSelected?.id}
                        onApplyClick={() => {
                          onApplyClick(item);
                        }}
                        myVoucherList={myVoucherList}
                      />
                    </Box>
                  ))
                ) : (
                  <NoDataView content="Không có voucher" />
                )}
              </RadioGroup>
            </Stack>
          )}
        </Stack>
      </FrameContainerNoIcon>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    marginTop: 1,
    marginBottom: 4,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
};
