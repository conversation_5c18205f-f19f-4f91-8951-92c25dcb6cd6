export enum BannerType {
  home_banner = "home_banner",
  collab_banner = "collab_banner",
}

export interface INews {
  id: number;
  title: string;
  content: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  type: BannerType;
  image: {
    data: {
      attributes: {
        formats: {
          large: {
            url: string;
          };
          medium: {
            url: string;
          };
          small: {
            url: string;
          };
          thumbnail: {
            url: string;
          };
        };
        url: string;
      };
    };
  };
}
