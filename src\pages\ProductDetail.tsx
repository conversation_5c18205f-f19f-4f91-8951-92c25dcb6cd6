import { CircularProgress, Container, Stack } from "@mui/material";
import React, { useEffect, useState } from "react";
import BottomTabNavigationDetail from "../components/menu/BottomTabNavigationDetail";
import FrameContainerFull from "../components/layout/ContainerFluid";
import { AppDispatch, RootState } from "../redux/store";
import { IProduct } from "../types/product";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { getProductDetail } from "../redux/slices/product/productSlice";

import ImagesProductSlider from "@/components/productDetail/ImagesProductSlider";
import ProductDetailContent from "@/components/productDetail/ProductDetailContent";
import ProductDetailComment from "@/components/productDetail/ProductDetailComment";
import { getProductComment } from "@/redux/slices/product/productCommentSlice";
import { ItemGallery } from "@/components/productDetail/ItemGallery";

export default function ProductDetail() {
  const { productDetail } = useSelector((state: RootState) => state.product);
  const [rateStat, setRateStat] = useState(0);
  const rateList = useSelector(
    (state: RootState) => state.productComment.productCommentList
  );
  const dispatch = useDispatch<AppDispatch>();
  const { id = "1" } = useParams();

  const [product, setProduct] = useState<IProduct>();

  const getProductCommentList = async (id) => {
    await dispatch(getProductComment(id));
  };

  useEffect(() => {
    if (!productDetail) return;

    setProduct(productDetail);
  }, [productDetail]);

  useEffect(() => {
    if (rateList.length) {
      setRateStat(
        Math.round(
          (rateList.reduce((a, b) => a + b.rate, 0) / rateList.length) * 10
        ) / 10
      );
    } else {
      setRateStat(0);
    }
  }, [rateList]);

  useEffect(() => {
    if (!id) return;
    dispatch(getProductDetail(id));
    getProductCommentList(id);
  }, [id]);

  const renderContent = () => {
    if (!product) {
      return (
        <Stack mt={8} justifyContent={"center"} alignItems={"center"}>
          <CircularProgress />
        </Stack>
      );
    }

    return (
      <>
        <ItemGallery images={productDetail?.image} />

        {/* product detail */}
        <ProductDetailContent
          product={product}
          rateList={rateList}
          rateStat={rateStat}
        />
        {/* comment */}
        <ProductDetailComment
          product={product}
          rateList={rateList}
          rateStat={rateStat}
        />
        <BottomTabNavigationDetail product={product} />
      </>
    );
  };

  return (
    <FrameContainerFull
      title={"Chi tiết sản phẩm"}
      overrideStyle={{ background: "transparent" }}
    >
      {renderContent()}
    </FrameContainerFull>
  );
}
