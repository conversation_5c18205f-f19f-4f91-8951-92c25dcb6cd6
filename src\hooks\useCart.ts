import { ICart } from "../types/cart";
import { StorageKeys } from "../constants/StorageKeys";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import { setCart } from "../redux/slices/cart/cartSlice";
import { getItem, setItem } from "../utils/storage";
import {
  createMac,
  createOrderOnServer,
  trackingPayment,
} from "@/redux/slices/order/orderSlice";
import { AppEnv, Platform } from "@/config";
import { Payment } from "zmp-sdk";
import { MethodValue } from "@/constants/Const";
const ship = 0;

export const useCart = () => {
  const { cart } = useSelector((state: RootState) => state.cart);
  const { user } = useSelector((state: RootState) => state.auth);
  const { FIRST_COMBO_NAME } = useSelector(
    (state: RootState) => state.config.data
  );
  const dispatch = useDispatch<AppDispatch>();
  // const list = useSelector((state: RootState) => state.promotion.list);
  const loadCartFromStorage = async () => {
    const cartFromStorage = await getItem(StorageKeys.Cart);
    if (cartFromStorage) {
      dispatch(setCart(cartFromStorage));
    }
  };

  const setCartAndSave = (newCart: ICart | null) => {
    dispatch(setCart(newCart));
    setItem(StorageKeys.Cart, newCart);
  };
  const reloadCart = () => {
    if (cart?.items && cart?.items.length > 0) {
      const items = cart?.items;
      setCartAndSave({
        items,
        ...recalculateCart(items),
      });
    }
  };

  const addProduct = (product, quantity = 0, promotion = {}) => {
    if (cart?.items?.find((item) => item.product.id === product.id)) {
      return cart.items.map((item) =>
        item.product.id === product.id
          ? {
              ...item,
              quantity: item.quantity + (quantity || 1),
              promotion: promotion,
            }
          : item
      );
    } else {
      return [
        ...(cart?.items || []),
        { product, quantity: quantity || 1, promotion: promotion },
      ];
    }
  };

  const recalculateCart = (items) => {
    const totalPrice = items.reduce((total, item) => {
      const discountedPrice = item.product.price - item.product.discount;
      return total + item.quantity * discountedPrice;
    }, 0);
    const totalDiscount = items.reduce(
      (total, item) => total + item.quantity * item.product.discount,
      0
    );
    const finalPrice = totalPrice + ship;

    return { totalPrice, totalDiscount, ship, finalPrice };
  };

  const addProductToCart = (product, quantity: number = 0, promotion = {}) => {
    const items = addProduct(product, quantity, promotion);
    setCartAndSave({
      items,
      ...recalculateCart(items),
    });
    return true;
  };

  const updateCart = (newItems) => {
    setCartAndSave({
      ...newItems,
    });
  };
  const removeProductFromCart = (productId: number) => {
    const items =
      cart?.items?.filter((item) => item.product.id !== productId) || [];
    if (items.length === 0) {
      setCartAndSave(null);
    } else
      setCartAndSave({
        items,
        ...recalculateCart(items),
      });
  };

  const changeProductQuantity = (productId: number, quantity: number) => {
    const items =
      cart?.items?.map((item) =>
        item.product.id === productId ? { ...item, quantity } : item
      ) || [];
    setCartAndSave({
      items,
      ...recalculateCart(items),
    });
  };

  const clearCart = () => {
    setCartAndSave({
      items: [],
      totalPrice: 0,
      totalDiscount: 0,
      ship: 0,
      finalPrice: 0,
    });
  };

  const createOrderZalo = async (body) => {
    // console.log("🚀 ~ createOrderZalo ~ body:", body);

    const item = body.orderData.items.map((item) => ({
      id: String(item.product.id),
      amount: item.quantity * item.product.price,
    }));
    const paymentMethod = {
      id:
        AppEnv === "production"
          ? MethodValue[body.paymentMethod]
          : MethodValue[body.paymentMethod] + "_SANDBOX",
      isCustom: false,
    };

    const extraData = {
      storeName: "Kho tổng",
      storeId: "1",
      orderId: body.id,
      notes: body.note,
    };

    const orderData: any = {
      desc: `Thanh toán GT${body.id}`,
      item,
      amount: body.finalPrice,
      extradata: JSON.stringify(extraData),
      method: JSON.stringify(paymentMethod),
    };
    // console.log("🚀 ~ createOrderZalo ~ zaloBody:", orderData);

    const mac = await dispatch(createMac(orderData)).unwrap();
    // console.log("🚀 ~ createOrderZalo ~ mac:", mac);
    orderData.mac = mac;

    dispatch(trackingPayment(orderData));

    return new Promise((resolve, reject) => {
      Payment.createOrder({
        ...orderData,
        success: resolve,
        fail: reject,
      });
    });
  };

  const createOrder = async (body) => {
    try {
      const res: any = await dispatch(createOrderOnServer(body)).unwrap();
      // console.log("🚀 ~ createOrder ~ res:", res);
      if (res?.data?.id) {
        if (Platform === "zalo") {
          // if (
          //   body.orderData.items.some(
          //     (item) => item.product?.product_cats[0]?.name === FIRST_COMBO_NAME
          //   )
          // ) {
          const resZalo = await createOrderZalo(res?.data);
          // }
          // console.log("🚀 ~ createOrder ~ resZalo:", resZalo);
        }
      }
      return res;
    } catch (e) {
      console.log(e);
    }
  };

  return {
    cart,
    addProductToCart,
    removeProductFromCart,
    changeProductQuantity,
    loadCartFromStorage,
    clearCart,
    updateCart,
    createOrder,
    recalculateCart,
    reloadCart,
  };
};
