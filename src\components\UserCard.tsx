import React from "react";
import { Box, Typography, Avatar } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { COLORS, theme } from "@/constants/themes";
import { Icon } from "@/constants/Assets";
import GradientText from "./UI/GradientTextProps";

interface UserCardProps {
  user: any;
  userZalo: any;
}

const UserCard = ({ user, userZalo }: UserCardProps) => {
  return (
    <Box
      sx={{ display: "flex", gap: 1 }}
      alignItems="center"
      justifyContent="flex-start"
    >
      <Box
        component="section"
        display="flex"
        alignItems="center"
        sx={{
          borderRadius: "50%",
          background: "white",
          border: '3px solid #007AFF',
        }}
      >
        <Avatar
          src={userZalo?.avatar || user?.avatarUrl || Icon.avatarDefault}
        />
      </Box>
      <Box>
        <GradientText
          style={{
            fontSize: 12,
            fontWeight: 500,
          }}
          text="Xin chào"
        />
        <Typography
          fontSize={12}
          fontWeight={750}
          color={COLORS.primary}
        >
          {userZalo?.name || user?.name}
        </Typography>
      </Box>
    </Box>
  );
};

export default UserCard;
