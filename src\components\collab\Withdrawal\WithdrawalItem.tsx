import GradientText from "@/components/UI/GradientTextProps";
import { WithdrawalStatus, WithdrawalStatusText } from "@/constants/Const";
import { COLORS, commonStyle } from "@/constants/themes";
import { IPayment } from "@/types/payment";
import { formatPrice } from "@/utils/formatPrice";
import { Stack, Typography } from "@mui/material";
import dayjs from "dayjs";
import React from "react";

interface IWithdrawalItem {
  item: IPayment;
}

export default function WithdrawalItem({ item }: IWithdrawalItem) {
  return (
    <Stack style={{ ...commonStyle.shadowBorder, ...styles.container }}>
      <Typography style={styles.title}>
        Chi trả hoa hồng tháng {dayjs(item.attributes.createdAt).format("MM")}
      </Typography>
      <Typography style={{ paddingBlock: 4 }}>
        Số tiền:{" "}
        <GradientText
          style={{ fontWeight: 700 }}
          text={formatPrice(item.attributes.amount)}
        />
      </Typography>
      <Typography style={{ paddingBlock: 4 }}>
        Ngày nhận: {dayjs(item.attributes.createdAt).format("DD/MM/YYYY")}
      </Typography>
      <Typography style={{ paddingBlock: 4 }}>
        Trạng thái:
        <span style={{ fontWeight: 700, color: COLORS.primary2 }}>
          {item.attributes.done
            ? WithdrawalStatusText[WithdrawalStatus.Paid]
            : WithdrawalStatusText[WithdrawalStatus.Pending]}
        </span>
      </Typography>
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    padding: 16,
    paddingBlock: 8,
    marginBlock: 4,
    color: COLORS.neutral1,
  },
  title: {
    paddingBlock: 4,
    color: COLORS.neutral1,
    fontWeight: 700,
  },
};
