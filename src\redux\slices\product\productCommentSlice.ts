import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProductComment } from "@/types/product";

interface productCommentSate {
  productCommentList: IProductComment[];
  isLoading: boolean;
  error: string | null;
}

const initialState: productCommentSate = {
  productCommentList: [],
  isLoading: false,
  error: null,
};

export const getProductComment = createAsyncThunk(
  "productComment/getProductList",
  async (productId: number) => {
    const response: any = await request(
      "get",
      "/api/product-comments?populate=user",
      {
        filters: {
          product: {
            id: {
              $eq: productId,
            },
          },
        },
      }
    );
    return response;
  }
);

export const createComment = createAsyncThunk(
  "productComment/createCommentForProduct",
  async (data: any) => {
    const response: any = await request("post", `/api/product-comments`, {
      data,
    });
    return response;
  }
);

const productComment = createSlice({
  name: "productList",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getProductComment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductComment.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productCommentList = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));

          state.isLoading = false;
        }
      )
      .addCase(getProductComment.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(createComment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        createComment.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
        }
      )
      .addCase(createComment.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});
export default productComment.reducer;
