import { Box, Container, Grid } from "@mui/material";
import React, { useEffect, useState } from "react";
import { Icon } from "../constants/Assets";
import HeaderPage from "../components/layout/LayoutHomepage";
import UserCard from "../components/UserCard";
import { Router } from "../constants/Route";
import ReportCard from "../components/report/ReportCard";
import OrderCard from "../components/order/OrderCard";
import { useTheme } from "@mui/material/styles";
import { useNavigate } from "../utils/component-util";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

const HomeAgentPage: React.FunctionComponent = () => {
  const [isData, setIsData] = useState(true);
  const theme = useTheme();
  const { user, userZalo } = useSelector((state: RootState) => state.auth);

  const navigate = useNavigate();

  useEffect(() => {
    // configView()
  }, []);
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  return (
    <HeaderPage>
      <Container>
        <Box
          style={{
            borderRadius: 20,
            background: "#fff",
            padding: 12,
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
          }}
        >
          <Grid container>
            <Grid item xs={7}>
              <Box sx={{ display: "flex", gap: 1 }} alignItems="center">
                <Box
                  height={25}
                  width={25}
                  component="section"
                  display="flex"
                  alignItems="center"
                  sx={{
                    p: 0.5,
                    borderRadius: "50%",
                    background: "white",
                  }}
                >
                  <img height={25} width={25} src={Icon.verify} alt="" />
                </Box>
                <Box style={{ width: "100%", fontSize: 12, fontWeight: 700 }}>
                  <p
                    style={{ marginTop: 10, color: theme.palette.primary.main }}
                  >
                    Tài khoản xác thực
                  </p>
                  <p style={{ marginTop: -12, color: "#2B7BE9" }}>
                    Hạng: <span style={{ color: "#1D1D5E" }}>Membership</span>
                  </p>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={5}>
              <UserCard user={user} userZalo={userZalo} />
            </Grid>
          </Grid>

          <Grid container spacing={2} style={{ marginTop: 12 }}>
            <Grid item xs={6}>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  borderRadius: 3,
                  boxShadow: "0px 0px 4px rgba(0, 0, 0, 0.25)",
                  padding: "4px 8px",
                  height: "63px",
                }}
                alignItems="center"
                justifyItems="center"
              >
                <Box
                  component="section"
                  display="flex"
                  alignItems="center"
                  sx={{
                    p: 0.5,
                    borderRadius: "50%",
                    background: "#F0F8FF",
                  }}
                >
                  <img
                    height={30}
                    width={30}
                    style={{
                      borderRadius: "50%",
                      background: "#D2EAFE",
                      margin: 3,
                      padding: 4,
                    }}
                    src={Icon.createOrder}
                    alt=""
                  />
                </Box>
                <Box style={{ fontSize: 14, fontWeight: 700 }}>
                  <p style={{ color: theme.palette.primary.main }}>
                    Tạo đơn hàng
                  </p>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  borderRadius: 3,
                  boxShadow: "0px 0px 4px rgba(0, 0, 0, 0.25)",
                  padding: "4px 8px",
                  height: "63px",
                }}
                alignItems="center"
                justifyItems="center"
                onClick={() => {
                  navigate(Router.refer.index);
                }}
              >
                <Box
                  component="section"
                  display="flex"
                  alignItems="center"
                  sx={{
                    p: 0.5,
                    borderRadius: "50%",
                    background: "#F0F8FF",
                  }}
                >
                  <img
                    height={30}
                    width={30}
                    style={{
                      borderRadius: "50%",
                      background: "#D2EAFE",
                      margin: 3,
                      padding: 4,
                    }}
                    src={Icon.regiterAgency}
                    alt=""
                  />
                </Box>
                <Box style={{ fontSize: 14, fontWeight: 700 }}>
                  <p style={{ color: theme.palette.primary.main }}>
                    Giới thiệu Bán hàng
                  </p>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  borderRadius: 3,
                  boxShadow: "0px 0px 4px rgba(0, 0, 0, 0.25)",
                  padding: "4px 8px",
                  height: "63px",
                }}
                alignItems="center"
                justifyItems="center"
                onClick={() => {
                  navigate(Router.collab.collabhistory.index);
                }}
              >
                <Box
                  component="section"
                  display="flex"
                  alignItems="center"
                  sx={{
                    p: 0.5,
                    borderRadius: "50%",
                    background: "#F0F8FF",
                  }}
                >
                  <img
                    height={30}
                    width={30}
                    style={{
                      borderRadius: "50%",
                      background: "#D2EAFE",
                      margin: 3,
                      padding: 4,
                    }}
                    src={Icon.historyOrder}
                    alt=""
                  />
                </Box>
                <Box style={{ fontSize: 14, fontWeight: 700 }}>
                  <p style={{ color: theme.palette.primary.main }}>
                    Lịch sử Đơn hàng
                  </p>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  borderRadius: 3,
                  boxShadow: "0px 0px 4px rgba(0, 0, 0, 0.25)",
                  padding: "4px 8px",
                  height: "63px",
                }}
                alignItems="center"
                justifyItems="center"
                onClick={() => {
                  navigate(Router.payment.index);
                }}
              >
                <Box
                  component="section"
                  display="flex"
                  alignItems="center"
                  sx={{
                    p: 0.5,
                    borderRadius: "50%",
                    background: "#F0F8FF",
                  }}
                >
                  <img
                    height={30}
                    width={30}
                    style={{
                      borderRadius: "50%",
                      background: "#D2EAFE",
                      margin: 3,
                      padding: 4,
                    }}
                    src={Icon.category}
                    alt=""
                  />
                </Box>
                <Box style={{ fontSize: 14, fontWeight: 700 }}>
                  <p style={{ color: theme.palette.primary.main }}>
                    Thông tin thanh toán
                  </p>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <p
          style={{
            fontSize: 18,
            fontWeight: 700,
            color: theme.palette.primary.main,
          }}
        >
          Báo cáo nhanh
        </p>
        <ReportCard />

        <p
          style={{
            fontSize: 18,
            fontWeight: 700,
            color: theme.palette.primary.main,
          }}
        >
          Đơn hàng gần đây
        </p>
        {isData ? (
          <OrderCard />
        ) : (
          <p
            style={{
              fontSize: 16,
              fontWeight: 400,
              color: "#666666",
              textAlign: "center",
            }}
          >
            Bạn chưa có đơn hàng nào từ hệ thống CTV
          </p>
        )}
      </Container>
    </HeaderPage>
  );
};

export default HomeAgentPage;
