import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProduct, IProductCategory } from "../../../types/product";
import { convertStrapiDocument } from "../../../utils/common";

interface AuthState {
  list: any;
  productDetail: IProduct | null;
  productCategoryList: IProductCategory[];
  isLoading: boolean;
}

const initialState: AuthState = {
  list: [],
  productDetail: null,
  productCategoryList: [],
  isLoading: true,
};

export const getProductCategoryList = createAsyncThunk(
  "product-cats",
  async () => {
    const response: any = await request(
      "get",
      "/api/product-cats?populate=image"
    );
    return response;
  }
);

export const getProductDetail = createAsyncThunk(
  "product/getProductDetail",
  async (id: string) => {
    const response: any = await request(
      "get",
      `/api/products/${id}`,
      {
        populate:["image","product_cats","supplier"],
      }
    );
    return response;
  }
);

const authSlice = createSlice({
  name: "product",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getProductCategoryList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductCategoryList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productCategoryList = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getProductCategoryList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getProductDetail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getProductDetail.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.productDetail = convertStrapiDocument(payload.data);
          state.isLoading = false;
        }
      )
      .addCase(getProductDetail.rejected, (state, action) => {
        state.isLoading = false;
      })
  },
});

export default authSlice.reducer;
