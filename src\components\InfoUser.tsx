import { Level, LevelIcon, LevelName } from "@/constants/Const";
import { RootState } from "@/redux/store";
import { useTheme } from "@emotion/react";
import { useSelector } from "react-redux";
import UserCard from "./UserCard";
import { Box, Grid, Typography } from "@mui/material";
import { Icon } from "@/constants/Assets";
import { COLORS } from "@/constants/themes";
import React from "react";
import GradientText from "./UI/GradientTextProps";

export default function InfoUser() {
  const { user, userZalo } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();

  if (!user) return null;
  return (
    <Grid container>
      <Grid item xs={7} display={"flex"} alignItems={"center"}>
        <Box sx={{ display: "flex", gap: 1 }} alignItems="center">
          <img
            style={{
              aspectRatio: "1/1",
            }}
            src={
              user.level !== Level.Member ? LevelIcon[user.level] : Icon.verify
            }
            alt=""
            width={30}
          />
          <Box>
            <GradientText
              style={{
                fontSize: 12,
                fontWeight: 700,
              }}
              text={
                user.level !== Level.Member
                  ? " Tài khoản xác thực"
                  : "Chưa đăng ký gói đại sứ"
              }
            />
            <Typography fontSize={12} fontWeight={700} color={"#2B7BE9"}>
              Hạng:{" "}
              <span style={{ color: "#1D1D5E" }}>
                {user?.level !== undefined && LevelName[user.level]}
              </span>
            </Typography>
          </Box>
        </Box>
      </Grid>
      <Grid item xs={5}>
        <UserCard user={user} userZalo={userZalo} />
      </Grid>
    </Grid>
  );
}
