import { Stack, Typography } from "@mui/material";
import React from "react";
import { IHomeTab } from "../../contants/Const";

export default function HomeTabItem(item: IHomeTab) {
  return (
    <Stack sx={styles.container}>
      <img src={item.icon} style={styles.iconStyle} />
      <Typography style={styles.titleStyle}>{item.title}</Typography>
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  iconStyle: {
    width: 25,
    height: 25,
    objectFit: "fill",
  },
  titleStyle: {
    fontSize: 12,
    fontWeight: 700,
    textAlign: "center",
    color: "#414141",
  },
};
