import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Slide,
  Stack,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import React, { useEffect } from "react";
import { useState } from "react";
import { Icon } from "../../constants/Assets";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { updateUser } from "../../redux/slices/authen/authSlice";
import { useTheme } from "@mui/material/styles";
import {
  getWarehouseList,
  setCurrentWarehouse,
} from "@/redux/slices/warehouse/warehouseSlice";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import GradientText from "./GradientTextProps";
import { COLORS } from "@/constants/themes";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function PopupChangeWareHouse() {
  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  const { user } = useSelector((state: RootState) => state.auth);
  const { currentWarehouse, list } = useSelector(
    (state: RootState) => state.warehouse
  );
  const [open, setOpen] = React.useState(false);
  const handleClickOpen = () => {
    checkLogin(() => {
      setOpen(true);
    });
  };
  const { checkLogin } = useCheckLogin();

  const [newWarehouseId, setNewWarehouseId] = useState<number | null>();

  useEffect(() => {
    if (
      user &&
      (!Array.isArray(currentWarehouse) || !currentWarehouse.length)
    ) {
      dispatch(getWarehouseList());
    }
  }, [user]);

  useEffect(() => {
    const warehouse = list?.find((item) => item.id === user?.warehouse);
    if (warehouse) {
      dispatch(setCurrentWarehouse(warehouse));
    }
  }, [user, list]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleChangeWarehouse = async () => {
    if (!Array.isArray(list)) return;

    setOpen(false);

    if (newWarehouseId) {
      if (user && user.warehouse !== newWarehouseId) {
        await dispatch(
          updateUser({
            id: user.id,
            warehouse: newWarehouseId,
          })
        );
      }

      const warehouse = list.find((item) => item.id === newWarehouseId);
      if (warehouse) {
        dispatch(setCurrentWarehouse(warehouse));
      }
    }
  };

  return (
    <>
      {currentWarehouse ? (
        <Stack direction="row" gap={3} onClick={handleClickOpen}>
          <img src={Icon.map} />
          <Stack>
            <span style={{ fontWeight: 700 }}>
              {currentWarehouse?.attributes?.code} -
              {currentWarehouse?.attributes?.name}
            </span>
            <span
              style={{
                color: "#969595",
                WebkitLineClamp: 1,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
                textAlign: "start",
                display: "-webkit-box",
              }}
            >
              {currentWarehouse?.attributes?.address}
            </span>
          </Stack>
          <IconButton style={{ marginRight: 0, marginLeft: "auto" }}>
            <KeyboardArrowRightIcon />
          </IconButton>
        </Stack>
      ) : (
        <Stack direction="row" gap={3} onClick={handleClickOpen}>
          <img src={Icon.map} />
          <Stack>
            <span style={{ color: "#F96968", fontWeight: 700 }}>
              Chưa chọn điểm nhận hàng
            </span>
            <span style={{ color: "#969595" }}>
              Lựa chọn điểm nhận hàng gần bạn
            </span>
          </Stack>
          <IconButton>
            <KeyboardArrowRightIcon />
          </IconButton>
        </Stack>
      )}

      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogContent id="alert-dialog-slide-description">
          <Stack direction="row" gap={2} style={{ fontSize: 16 }}>
            <FormControl style={{ width: "100%" }}>
              <Stack gap={2}>
                <RadioGroup
                  row
                  aria-labelledby="demo-form-control-label-placement"
                  name="position"
                  defaultValue="top"
                >
                  <Stack gap={2} width={"100%"}>
                    <FormControl>
                      <RadioGroup
                        aria-labelledby="demo-radio-buttons-group-label"
                        defaultValue={user?.addressId}
                        name="radio-buttons-group"
                      >
                        {list?.map((item, index) => (
                          <Stack key={`address-${index}`}>
                            <Stack
                              width={"100%"}
                              direction="row"
                              justifyContent="space-between"
                              onClick={() => setNewWarehouseId(item.id)}
                            >
                              <Stack direction={"row"} alignItems={"center"}>
                                <img height={24} width={24} src={Icon.map} />
                                <Stack ml={3}>
                                  <GradientText
                                    text={`${item?.attributes?.code} - ${item?.attributes?.name}`}
                                    style={{
                                      fontWeight: 700,
                                    }}
                                  />
                                  <span
                                    style={{
                                      WebkitLineClamp: 2,
                                      WebkitBoxOrient: "vertical",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      textAlign: "start",
                                      display: "-webkit-box",
                                    }}
                                  >
                                    {item?.attributes?.address}
                                  </span>
                                </Stack>
                              </Stack>
                              <FormControlLabel
                                value={item.id.toString()}
                                control={<Radio />}
                                labelPlacement="start"
                                sx={{
                                  ".Mui-checked": {
                                    color: COLORS.primary2,
                                  },
                                }}
                                label=""
                                checked={newWarehouseId == item.id}
                                onChange={async (e: any) => {
                                  setNewWarehouseId(e.target.value);
                                }}
                              />
                            </Stack>
                            {index !== list.length - 1 && (
                              <Divider sx={{ margin: "10px 0" }} />
                            )}
                          </Stack>
                        ))}
                      </RadioGroup>
                    </FormControl>
                  </Stack>
                </RadioGroup>
              </Stack>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions
          sx={{ justifyContent: "center", gap: 2, marginBlock: 2 }}
        >
          <Button
            style={{
              background: COLORS.primary1,
              color: "#1D1D5E",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
            }}
            // href={"/cart"}
            onClick={() => setOpen(false)}
            startIcon={<CloseIcon />}
          >
            Hủy bỏ
          </Button>
          <Button
            style={{
              background: COLORS.primary,
              color: "#fff",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
            }}
            startIcon={<CheckCircleOutlineIcon />}
            onClick={() => handleChangeWarehouse()}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
