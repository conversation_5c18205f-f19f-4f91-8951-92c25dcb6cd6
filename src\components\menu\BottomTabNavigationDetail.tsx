import React from "react";
import { Button, IconButton, Stack, Box } from "@mui/material";
import PopupAddToCart from "../UI/PopupAddToCart";
import { Icon } from "../../constants/Assets";
import { IProduct } from "../../types/product";
import { useCart } from "../../hooks/useCart";
import styles from "../../css/styles.module.css";
import { useNavigate } from "../../utils/component-util";
import { openChat } from "../../utils/openChat";
import { Router } from "../../constants/Route";
import { COLORS } from "@/constants/themes";

const BottomTabNavigationDetail: React.FC<{ product: IProduct }> = ({
  product,
}) => {
  const navigate = useNavigate();

  return (
    <Box className={styles.bottomCartElement}>
      <Stack
        sx={{ minWidth: "100%" }}
        style={{ fontSize: 12 }}
        spacing={2}
        direction="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <IconButton
          style={{
            marginInline: 8,
          }}
          aria-label="fingerprint"
          color="secondary"
          onClick={openChat}
        >
          <img src={Icon.chatting} sizes="26px" />
        </IconButton>
        <PopupAddToCart product={product} />

        <Button
          onClick={() => {
            navigate(Router.cart);
          }}
          style={{
            background: COLORS.primary,
            color: "#fff",
            margin: 0,
            height: "100%",
            borderRadius: 0,
            fontSize: 14,
            fontWeight: 700,
            flex: 1,
          }}
          startIcon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="19"
              height="19"
              fill="none"
              viewBox="0 0 19 19"
            >
              <path
                fill="#fff"
                fillRule="evenodd"
                d="M.999.042a.75.75 0 0 0-.497 1.416l.26.091c.669.235 1.108.39 1.43.55.304.148.437.27.525.397.09.132.16.314.2.677.04.38.042.875.042 1.615V7.39c0 2.942.063 3.912.93 4.826.866.914 2.26.914 5.05.914h5.302c1.56 0 2.342 0 2.893-.45.552-.45.71-1.214 1.025-2.742l.5-2.425c.347-1.74.52-2.609.076-3.186s-1.96-.577-3.645-.577H4.45a9.029 9.029 0 0 0-.042-.738c-.054-.497-.17-.95-.452-1.362-.284-.416-.662-.682-1.103-.899C2.44.55 1.917.365 1.3.148L.999.042ZM10.959 6a.75.75 0 0 1 .75.75V8h1.25a.75.75 0 1 1 0 1.5h-1.25v1.25a.75.75 0 1 1-1.5 0V9.5h-1.25a.75.75 0 1 1 0-1.5h1.25V6.75a.75.75 0 0 1 .75-.75Z"
                clipRule="evenodd"
              />
              <path
                fill="#fff"
                d="M5.459 15.75a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm9 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z"
              />
            </svg>
          }
        >
          <p>Xem giỏ hàng</p>
        </Button>
      </Stack>
    </Box>
  );
};

export default BottomTabNavigationDetail;
