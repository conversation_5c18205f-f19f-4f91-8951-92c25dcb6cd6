import { COLORS } from "../../constants/themes";
import { Stack, Typography } from "@mui/material";
import React from "react";

interface INoDataView {
  content?: string;
}

export default function NoDataView({ content }: INoDataView) {
  return (
    <Stack direction="row" justifyContent={"center"} width={"100%"}>
      <Typography style={styles.content} p={4}>
        {content ?? "Không có dữ liệu"}
      </Typography>
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  content: {
    color: COLORS.neutral5,
    textAlign: "center",
  },
};
