import React from "react";
import { Box, Container, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setPreviousPage } from "@/redux/slices/common/commonSlice";
import GradientText from "@/components/UI/GradientTextProps";
import { COLORS } from "@/constants/themes";

export default function ListItemHomePage({
  children,
  title,
  isShowMore = false,
  linkAfterClickShowMore = "",
}) {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  return (
    <Box marginBlock={"15px"}>
      <Box
        display={"flex"}
        justifyContent={"space-between"}
        style={{
          marginBottom: "10px",
        }}
      >
        <GradientText
          text={title}
          style={{
            fontWeight: 700,
            fontSize: "18px",
          }}
        />
        {isShowMore && (
          <Typography
            onClick={() => {
              navigate(linkAfterClickShowMore);
              dispatch(setPreviousPage(linkAfterClickShowMore));
            }}
            style={{
              color: COLORS.neutral6,
            }}
          >
            Xem thêm
          </Typography>
        )}
      </Box>
      {children}
    </Box>
  );
}
