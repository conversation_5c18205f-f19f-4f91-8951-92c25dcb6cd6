import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Icon } from "@/constants/Assets";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { AppDispatch, RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import {
  createComment,
  getProductComment,
} from "@/redux/slices/product/productCommentSlice";
import { showToast } from "@/utils/common";

const validationSchema = Yup.object().shape({
  content: Yup.string().required("Vui lòng nhập nội dung"),
});

type FormData = {
  content: string;
};

export default function ProductDetailComment({ product, rateList, rateStat }) {
  const { user, userZalo } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch<AppDispatch>();
  const theme = useTheme();
  const [valueRate, setValueRate] = useState<number | null>(5);

  const formOptions: any = {
    resolver: yupResolver(validationSchema),
    values: {
      content: "",
    },
  };

  const { handleSubmit, register, formState, reset } =
    useForm<FormData>(formOptions);
  const { errors } = formState;

  const onSubmit = async (data) => {
    const dataParams = {
      ...data,
      product: product?.id,
      user: user?.id,
      rate: valueRate,
    };
    const existItem = rateList.find((item) => item.user?.data?.id === user?.id);
    if (existItem) {
      showToast({
        content: "Bạn đã đánh giá sản phẩm này",
        type: "error",
      });
    } else {
      try {
        const res = await dispatch(createComment(dataParams));
        if (res.error) {
          showToast({
            content: res.error.message ?? "Đánh giá không thành công",
            type: "error",
          });
          return;
        }
        reset();
        setValueRate(5);
        await dispatch(getProductComment(product?.id));
      } catch (error) {
        showToast({
          content: error.message,
          type: "error",
        });
      }
    }
  };
  const onChangeRate = (event, newValue) => {
    if (newValue < 1) {
      setValueRate(1);
    } else {
      setValueRate(newValue);
    }
  };
  return (
    <>
      <Box style={{ background: "#fff", marginBlock: 12, paddingBlock: 20 }}>
        <Container>
          <Typography
            color={theme.palette.primary.main}
            fontWeight={700}
            fontSize={16}
          >
            Đánh giá sản phẩm
          </Typography>
          {rateList?.length == 0 ? (
            <Typography color={theme.palette.primary.main} fontSize={14}>
              Chưa có đánh giá
            </Typography>
          ) : (
            <Stack direction="row" alignItems={"center"} gap={2}>
              <Rating
                name="half-rating-read"
                value={rateStat}
                precision={0.5}
                readOnly
              />
              <Box>
                <b style={{ color: "#1D1D5E", fontSize: 14 }}>{rateStat}/5</b>
                <span style={{ fontSize: 12, marginInline: "5px" }}>
                  ({rateList?.length} đánh giá)
                </span>
              </Box>
            </Stack>
          )}
        </Container>
      </Box>

      {/* rating */}
      <Box
        style={{
          background: "#fff",
          marginBlock: 12,
          paddingBlock: 20,
          fontSize: 12,
        }}
      >
        <Container>
          {rateList?.map((item, i) => (
            <Stack key={`review-${i}`}>
              <Stack direction="row" alignItems="center" gap={"15px"}>
                <img
                  width={25}
                  src={
                    item.user?.data?.attributes.avatarUrl || Icon.avatarDefault
                  }
                  style={{ borderRadius: "50%" }}
                />
                <Stack>
                  <span style={{ fontWeight: 700 }}>
                    {item.user?.data?.attributes.name ||
                      item.user?.data?.attributes.phone ||
                      item.user?.data?.attributes.email}
                  </span>
                  <Rating
                    name="half-rating-read"
                    defaultValue={item.rate}
                    precision={0.5}
                    readOnly
                    size="small"
                  />
                </Stack>
              </Stack>
              <Stack pl={"40px"}>
                <p>{item.content}</p>
                <span style={{ color: "#666666" }}>
                  {dayjs(item.createdAt).format(" DD/MM/YYYY")}
                  <span style={{ marginInline: "5px" }}>
                    {dayjs(item.createdAt).format(" HH:mm")}
                  </span>
                </span>
              </Stack>
              <Divider variant="middle" style={{ marginBlock: 12 }} />
            </Stack>
          ))}
          {user && (
            <Stack>
              <form>
                <Stack direction="row" alignItems="center" gap={"15px"}>
                  <img
                    width={25}
                    src={
                      userZalo?.avatar || user?.avatarUrl || Icon.avatarDefault
                    }
                    style={{ borderRadius: "50%" }}
                  />
                  <Stack>
                    <span style={{ fontWeight: 700 }}>{user?.name}</span>
                    <Stack gap={"5px"}>
                      <Rating
                        name="half-rating-read"
                        value={valueRate}
                        onChange={onChangeRate}
                        precision={0.5}
                        size="small"
                      />
                    </Stack>
                  </Stack>
                </Stack>
                <Stack pl={"40px"} mt="8px" direction="column" gap={"10px"}>
                  <Stack gap={"5px"}>
                    <TextField
                      {...register("content")}
                      multiline
                      placeholder="Đánh giá cho sản phẩm này"
                      rows={3}
                      size="small"
                      fullWidth
                    />
                    {errors.content && (
                      <Typography fontSize={12} color={"red"}>
                        {errors.content.message}
                      </Typography>
                    )}
                  </Stack>
                  <Button
                    style={{
                      alignSelf: "flex-end",
                      color: "#fff",
                    }}
                    variant="contained"
                    disableElevation
                    onClick={handleSubmit(onSubmit)}
                  >
                    Đánh giá
                  </Button>
                </Stack>
              </form>
            </Stack>
          )}
        </Container>
      </Box>
    </>
  );
}
