import { Box } from "@mui/material";
import { useState } from "react";
import Slider from "react-slick";
import React from "react";

export default function ImagesProductSlider({ images }) {
  const [imageIndex, setImageIndex] = useState(0);
  const sliderSetting = {
    dots: false,
    speed: 500,
    slidesToShow: 4,
    arrows: false,
    infinite: false,
  };
  return (
    <>
      <Box style={styles.backgroundImageProduct} >
        <img
          width="100%"
          src={images[imageIndex]?.url}
          alt={"images"}
          style={{
            aspectRatio: "1 / 1",
            objectFit: "cover",
          }}
        />
      </Box>
      <Slider className="slider-images-product-detail" {...sliderSetting}>
        {images && images?.map((images, index) => (
          <Box key={index} onClick={() => setImageIndex(index)}  className="slider-images-product-detail-item" >
            <img
              width={"100%"}
              src={images?.url}
              style={{
                border: "1px solid #E9EBED",
                aspectRatio: "1 / 1",
                objectFit: "cover",
              }}
              alt="images"
            />
          </Box>
        ))}
      </Slider>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  backgroundImageProduct: {
    background: "#fff",
    border: "1px solid #E9EBED",
  },

};

