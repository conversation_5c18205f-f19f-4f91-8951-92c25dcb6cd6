/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { ITerm } from "../../../types/term";
import { request } from "../../../utils/request";

interface TermState {
  collaboratorPolicyList: ITerm[];
  termList: ITerm[];
  termDetail: ITerm | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: TermState = {
  collaboratorPolicyList: [],
  termList: [],
  termDetail: null,
  isLoading: true,
  error: null,
};

export const getCollaboratorPolicyList = createAsyncThunk(
  "termList/getCollaboratorPolicyList",
  async () => {
    const response = await request("get", "/api/terms", {
      filters: {
        isCollaboratorPolicy: {
          $eq: true
        }
      }
    });
    return response;
  }
);

export const getTermList = createAsyncThunk(
  "termList/getTermList",
  async () => {
    const response = await request("get", "/api/terms", {
      sort: ['id:asc'],
      filters: {
        $or: [
          { isCollaboratorPolicy: { $eq: false } },
          { isCollaboratorPolicy: { $eq: null } },
        ],
      }
    });
    return response;
  }
);

export const getTermDetail = createAsyncThunk(
  "termList/getTermDetail",
  async (id: string) => {
    const response = await request("get", `/api/terms/${id}`);
    return response;
  }
);
const termSlice = createSlice({
  name: "termList",
  initialState,
  reducers: {
    clearTermDetail: (state) => {
      state.termDetail = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCollaboratorPolicyList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getCollaboratorPolicyList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          state.collaboratorPolicyList = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getCollaboratorPolicyList.rejected, (state) => {
        state.isLoading = false;
      })
      .addCase(getTermList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getTermList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          state.termList = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getTermList.rejected, (state) => {
        state.isLoading = false;
      })
      .addCase(getTermDetail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getTermDetail.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.termDetail = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getTermDetail.rejected, (state) => {
        state.isLoading = false;
      });
  },
});
export const { clearTermDetail } = termSlice.actions;
export default termSlice.reducer;
