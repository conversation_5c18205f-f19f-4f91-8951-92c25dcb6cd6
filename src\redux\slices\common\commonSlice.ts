import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ICommonState {
  previousPage: string | null;
}

const initialState: ICommonState = {
  previousPage: null,
};

const commonSlice = createSlice({
  name: "previousPage",
  initialState,
  reducers: {
    setPreviousPage: (state, action: PayloadAction<string | null>) => {
      state.previousPage = action?.payload;
    },
  },
});

export const { setPreviousPage } = commonSlice.actions;

export default commonSlice.reducer;
