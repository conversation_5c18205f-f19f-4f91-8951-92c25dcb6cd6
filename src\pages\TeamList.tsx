import React, { useEffect, useState } from "react";
import {
  Stack,
  FormControl,
  TextField,
  InputAdornment,
  Button,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { getMember } from "@/redux/slices/team/team";
import MemberItem from "@/components/MemberItem";
import NoDataView from "@/components/NoDataView";
import FrameContainer from "@/components/layout/Container";
import ContentTreeView from "@/components/ContentTreeView";

export default function CollabMembers() {
  const [keyword, setKeyword] = useState("");
  const { list } = useSelector((state: RootState) => state.team);
  const dispatch = useDispatch<AppDispatch>();

  const [showClearIcon, setShowClearIcon] = useState("none");

  const handleChangeSearch = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setKeyword(event.target.value);
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
  };

  const handleClick = (): void => {
    setKeyword("");
  };

  useEffect(() => {
    dispatch(getMember(keyword));
  }, [keyword]);

  return (
    <FrameContainer title="Danh sách đội nhóm" style={{ background: "#FFF" }}>
      <Stack sx={{ marginBottom: 1 }}>
        <FormControl>
          <TextField
            placeholder="Nhập tên, SĐT hoặc ID thành viên"
            style={{ width: "100%" }}
            size="small"
            variant="outlined"
            value={keyword}
            onChange={handleChangeSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {<SearchIcon />}
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment
                  position="end"
                  style={{ display: showClearIcon }}
                  onClick={handleClick}
                >
                  <ClearIcon />
                </InputAdornment>
              ),
            }}
          />
        </FormControl>
      </Stack>
      <Stack>
        {list.length > 0 ? (
          <ContentTreeView
            items={list}
            isSearch={!!keyword}
            containerStyles={{ paddingTop: 2 }}
          />
        ) : (
          <NoDataView content="Đội nhóm của bạn chưa có thành viên nào" />
        )}
      </Stack>
    </FrameContainer>
  );
}
