import {
  <PERSON><PERSON>,
  <PERSON>ircularProgress,
  Container,
  Stack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { Icon } from "../../constants/Assets";
import { LogoutIcon } from "../../constants/IconSvg";
import { Router } from "../../constants/Route";
import { useTheme } from "@mui/material/styles";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import {
  getUserZalo,
  logout,
  register,
  registerCollabration,
} from "../../redux/slices/authen/authSlice";
import UserReferCode from "../../components/profile/UserReferCode";
import { APP_NAME } from "../../constants/AppInfo";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { useNavigate } from "../../utils/component-util";
import { Platform } from "../../config";
import { mapError, trackEvent } from "../../utils/common";
import LayoutAccountPage from "@/components/layout/LayoutAccountPage";
import MenuProfile from "@/components/profile/MenuProfile";
import InfoUser from "@/components/InfoUser";
import { DEFAULT_REFER_CODE } from "@/constants/Const";
import { getRouteParams } from "zmp-sdk";
import { COLORS } from "@/constants/themes";

export default function Profile() {
  const { user } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const theme = useTheme();
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = useState(false);
  const { showAlert } = useAlert();
  const { refCode } = getRouteParams();

  const onClickRegister = async () => {
    setLoading(true);
    const referCode = refCode || DEFAULT_REFER_CODE;
    trackEvent("registerHomePage", referCode);
    const res = await dispatch(register(referCode)).unwrap();
    if (res.jwt) {
      showAlert({
        icon: Icon.check,
        title: "Kích hoạt tài khoản thành công",
      });
      // await dispatch(
      //   registerCollabration({
      //     referCode: referCode,
      //   })
      // );
      await dispatch(getUserZalo());
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  const renderContent = () => {
    if (!user) {
      return (
        <Stack
          justifyContent={"center"}
          alignItems={"center"}
          padding={2}
          style={{
            borderRadius: 20,
            background: "#fff",
            padding: "15px",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.25)",
          }}
          height={500}
        >
          {loading ? (
            <CircularProgress />
          ) : (
            <>
              <Button
                style={{
                  background: COLORS.primary,
                  fontWeight: 700,
                }}
                variant="contained"
                onClick={onClickRegister}
              >
                Kích hoạt tài khoản
              </Button>
              <Typography variant="body2" textAlign={"center"} pt={2}>
                Bằng việc bấm "Kích hoạt tài khoản", chúng tôi hiểu rằng bạn đã
                đồng ý với điều khoản của {APP_NAME}.
              </Typography>
            </>
          )}
        </Stack>
      );
    } else {
      return (
        <>
          <Stack gap={2}>
            <Stack
              style={{
                borderRadius: 20,
                background: "#fff",
                padding: 15,
                boxShadow: "0px 0px 10px 0px #00000040",
              }}
            >
              <InfoUser />
            </Stack>
          </Stack>
          <MenuProfile />
          <UserReferCode />
          {Platform === "web" ? (
            <Stack
              sx={{
                fontWeight: 400,
                color: "#000",
                borderRadius: 99,
                background: "#fff",
                padding: "10px 16px",
              }}
              direction="row"
              justifyContent={"center"}
              alignItems="center"
              gap={1}
              onClick={() => {
                showAlert({
                  icon: Icon.warning,
                  title: "Đăng xuất",
                  content: "Bạn chắc chắn muốn đăng xuất chứ?",
                  buttons: [
                    {
                      title: "Huỷ",
                    },
                    {
                      title: "OK",
                      action: () => {
                        dispatch(logout());
                        navigate(Router.login);
                      },
                    },
                  ],
                });
              }}
            >
              <LogoutIcon />
              <Stack>
                <span style={{ fontWeight: 700, color: "red" }}>Đăng xuất</span>
              </Stack>
            </Stack>
          ) : null}
        </>
      );
    }
  };

  return (
    <LayoutAccountPage>
      <Container>{renderContent()}</Container>
    </LayoutAccountPage>
  );
}
