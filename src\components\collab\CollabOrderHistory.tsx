import React, { useEffect, useState } from "react";
import {
  <PERSON>ack,
  Grid,
  Typography,
  Box,
  TextField,
  CircularProgress,
} from "@mui/material";
import DropdownCollabHistory from "../dropdown/DropdownCollabHistory";
import {
  DefaultFilter,
  OrderStatus,
  OrderStatusText,
} from "../../constants/Const";
import { useDispatch, useSelector } from "react-redux";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import { AppDispatch, RootState } from "@/redux/store";
import { getTeamOrder } from "@/redux/slices/team/team";
import OrderItem from "@/pages/OrderItem";
import { showToast } from "@/utils/common";

interface ICollabOrderHistory {
  isFilter: boolean;
}

export default function CollabOrderHistory({ isFilter }: ICollabOrderHistory) {
  const dispatch = useDispatch<AppDispatch>();
  const { teamOrder } = useSelector((state: RootState) => state.team);
  const [isLoading, setIsLoading] = useState(false);
  const [orderStatus, setOrderStatus] = useState(0);
  const [selectedRange, setSelectedRange] = useState([]);
  const changeFilter = async () => {
    setIsLoading(true);
    const filters: any = {};
    if (orderStatus) {
      filters.orderStatus = orderStatus;
    }
    const [start, end] = selectedRange;
    if (start && end) {
      filters.createdAt = {
        $between: [
          moment(start).startOf("day").format(),
          moment(end).endOf("day").format(),
        ],
      };
    }
    try {
      await dispatch(getTeamOrder({ filters }));
    } catch (error: any) {
      showToast({
        content: error.message ?? "Xuất hiện lỗi trong quá trình lấy dữ liệu",
        type: "error",
      });
    }
    setIsLoading(false);
  };
  useEffect(() => {
    changeFilter();
  }, [orderStatus, selectedRange]);

  const OrderStatuses = [
    DefaultFilter,
    ...Object.keys(OrderStatusText)
      .map((key) => ({
        id: Number(key),
        name: OrderStatusText[key],
      }))
      .filter((item) => item.id !== OrderStatus.OutOfStock),
  ];

  return (
    <>
      <Box marginBlock={"15px"}>
        {isFilter && (
          <>
            <Grid container justifyContent="center" spacing={2}>
              <Grid item xs={6} sm={6}>
                {/* <DropdownCollabHistory
            label=""
            options={["Toàn thời gian", "Nửa ngày", "Cả ngày"]}
            defaultValue={sortBy}
            onChange={handleSortByChange}
          /> */}
                <DatePicker
                  className="datepicker-collab-order-history"
                  selected={selectedRange[0]}
                  onChange={setSelectedRange}
                  startDate={selectedRange[0]}
                  placeholderText="Khoảng ngày"
                  endDate={selectedRange[1]}
                  selectsRange
                />
              </Grid>
              <Grid item xs={6} sm={6}>
                <DropdownCollabHistory
                  label=""
                  options={OrderStatuses}
                  defaultValue={orderStatus}
                  onChange={setOrderStatus}
                />
              </Grid>
            </Grid>
          </>
        )}
        {isLoading ? (
          <Stack
            marginBlock={"15px"}
            justifyContent={"center"}
            alignItems={"center"}
          >
            <CircularProgress />
          </Stack>
        ) : (
          <Stack gap={1} marginBlock={"15px"}>
            {teamOrder?.length > 0 ? (
              teamOrder.map((order) => (
                <OrderItem order={order} key={String(order.id)} />
              ))
            ) : (
              <Stack direction="row" justifyContent={"center"}>
                <Typography
                  style={{
                    fontSize: 16,
                    fontWeight: 400,
                    color: "#666666",
                    textAlign: "center",
                  }}
                  p={4}
                >
                  Không có đơn hàng nào
                </Typography>
              </Stack>
            )}
          </Stack>
        )}
      </Box>
    </>
  );
}
