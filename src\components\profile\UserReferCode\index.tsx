import { getReferLink, showToast } from "../../../utils/common";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { saveImageToGallery } from "zmp-sdk/apis";
import { QRCode } from "zmp-qrcode";
import { Button, Grid, Stack, Typography, Box } from "@mui/material";
import React, { memo, useState } from "react";
import ShareIcon from "@mui/icons-material/Share";

import VerticalAlignBottomIcon from "@mui/icons-material/VerticalAlignBottom";
import { useShareReferLink } from "../../../hooks/useShareReferLink";
import { Platform } from "../../../config";

const UserReferCode = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const referLink = getReferLink(user?.referCode);
  const [src, setSrc] = useState("");
  const { shareLink } = useShareReferLink();

  const downloadQRCode = () => {
    if (!src) return;
    saveImageToGallery({
      imageUrl: src,
      success: () => {
        showToast({
          content: "Tải mã QR thành công",
          type: "success",
        });
      },
      fail: (error) => {
        showToast({
          content: "Tải mã QR thất bại",
          type: "error",
        });
        console.log(error);
      },
    });
  };
  return (
    <Stack>
      <Typography textAlign={"center"} pt={6}>
        Chia sẻ mã QR này tới Khách hàng tiềm năng
      </Typography>
      <Stack pt={1}>
        <Box
          style={{
            padding: 8,
            display: "flex",
            borderRadius: 8,
            margin: "auto",
            position: "relative",
            background: "white",
          }}
        >
          <QRCode
            size={256}
            value={referLink}
            ref={(el) =>
              el ? setTimeout(() => setSrc(el.getBase64()), 1000) : el
            }
          />
        </Box>
      </Stack>
      <Grid
        container
        pt={3}
        pb={3}
        spacing={3}
        direction="row"
        justifyContent={"center"}
        alignItems={"center"}
      >
        {Platform === "zalo" ? (
          <Grid item xs={6}>
            <Button
              style={{
                fontWeight: 400,
                color: "#000",
                borderRadius: 99,
                background: "#fff",
                padding: "10px 16px",
                width: "100%",
              }}
              onClick={downloadQRCode}
              startIcon={<VerticalAlignBottomIcon />}
            >
              Tải xuống
            </Button>
          </Grid>
        ) : null}
        <Grid item xs={6}>
          <Button
            style={{
              fontWeight: 400,
              color: "#000",
              borderRadius: 99,
              background: "#fff",
              padding: "10px 16px",
              width: "100%",
            }}
            startIcon={<ShareIcon />}
            onClick={shareLink}
          >
            Chia sẻ
          </Button>
        </Grid>
      </Grid>
    </Stack>
  );
};

export default memo(UserReferCode);
