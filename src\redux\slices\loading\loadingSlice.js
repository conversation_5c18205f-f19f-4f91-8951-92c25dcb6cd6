import { createSlice } from "@reduxjs/toolkit";

const loadingSlice = createSlice({
  name: "loading",
  initialState: {
    isLoading: true,
    loadingMessage: "",
  },
  reducers: {
    showLoading: (state, action) => {
      state.isLoading = true;
      state.loadingMessage = action.payload || "Loading...";
    },
    hideLoading: (state) => {
      state.isLoading = false;
      state.loadingMessage = "";
    },
  },
});

export const { showLoading, hideLoading } = loadingSlice.actions;

export default loadingSlice.reducer;
