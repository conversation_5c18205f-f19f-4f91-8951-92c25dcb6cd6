import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { ITeamReport } from "../../../types/team";
import { IOrder } from "../../../types/order";

interface TeamState {
  list: any;
  teamReport: ITeamReport | null;
  teamOrder: IOrder[] | [];
  isLoading: boolean;
}

const initialState: TeamState = {
  list: [],
  teamReport: null,
  isLoading: true,
  teamOrder: [],
};

export const getReferCodeInfo = createAsyncThunk(
  "team/getReferCodeInfo",
  async (data: any) => {
    const response: any = await request("get", "/api/my-team/refer-code", {
      referCode: data,
    });
    return response;
  }
);

export const joinTeam = createAsyncThunk("team/joinTeam", async (data: any) => {
  const response: any = await request("post", "/api/my-team/join", {
    referCode: data,
  });
  return response;
});

export const getMember = createAsyncThunk(
  "team/getMember",
  async ({ keyword, userId }: { keyword?: string; userId?: number }) => {
    const response = await request(
      "get",
      keyword
        ? `/api/my-team/member${
            userId ? `/${userId}` : ""
          }?name[$containsi]=${keyword}&phone[$containsi]=${keyword}`
        : `/api/my-team/member${userId ? `/${userId}` : ""}`
    );
    return response;
  }
);

export const getReport = createAsyncThunk("team/getReport", async () => {
  const response: any = await request("get", "/api/my-team/report");
  return response;
});
export const getTeamOrder = createAsyncThunk(
  "team/getTeamOrder",
  async (params?: any) => {
    const response: any = await request(
      "get",
      `/api/my-team/order?sort[createdAt]=desc`,
      params
    );
    return response;
  }
);

const TeamState = createSlice({
  name: "team",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(joinTeam.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(joinTeam.fulfilled, (state) => {
      state.isLoading = false;
    });
    builder.addCase(joinTeam.rejected, (state, action) => {
      state.isLoading = false;
    });
    builder.addCase(getMember.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(
      getMember.fulfilled,
      (state, action: PayloadAction<Partial<any>>) => {
        const { payload } = action;
        state.list = payload.data;
        state.isLoading = false;
      }
    );
    builder.addCase(
      getReport.fulfilled,
      (state, action: PayloadAction<any>) => {
        const { payload } = action;

        state.teamReport = payload;
        state.isLoading = false;
      }
    );
    builder.addCase(
      getTeamOrder.fulfilled,
      (state, action: PayloadAction<any>) => {
        const { payload } = action;
        state.teamOrder = payload.data;
        state.isLoading = false;
      }
    );
    builder.addCase(getMember.rejected, (state) => {
      state.isLoading = true;
    });
  },
});

export default TeamState.reducer;
