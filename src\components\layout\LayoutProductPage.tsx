import { Container, InputAdornment, TextField, useTheme } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import { useState } from "react";
import { Platform } from "@/config";
import { COLORS } from "@/constants/themes";

export default function LayoutProductPage({
  children,
  valueSearchText,
  onChangeValueSearch,
}) {
  const theme = useTheme();
  const [showClearIcon, setShowClearIcon] = useState("none");

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    onChangeValueSearch(event.target.value);
  };
  const handleClick = (): void => {
    // TODO: Clear the search input
    console.log("clicked the clear icon...");
    onChangeValueSearch("");
    setShowClearIcon("none");
  };
  return (
    <div style={{ position: "relative" }}>
      <div
        style={{
          background: COLORS.colorHeader,
          paddingTop: "50px",
          paddingBottom: "15px",
        }}
      >
        <Container>
          <TextField
            className="search-homepage"
            placeholder="Tìm sản phẩm"
            style={{
              width: `${Platform === "web" ? "100%" : "70%"}`,
              background: "#F4F5F6",
              borderRadius: "10px",
            }}
            size="small"
            variant="outlined"
            value={valueSearchText}
            onChange={handleChange}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  position="end"
                  style={{ display: showClearIcon }}
                  onClick={handleClick}
                >
                  <ClearIcon />
                </InputAdornment>
              ),
            }}
          />
        </Container>
      </div>
      {children}
    </div>
  );
}
