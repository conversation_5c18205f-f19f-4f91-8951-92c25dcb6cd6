import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INews } from "../../../types/news";
import { request } from "../../../utils/request";

interface NewsState {
  list: any;
  newsList: INews[];
  banner: INews[];
  recommend: INews[];
  newsListHome: INews[];
  reviewListHome: INews[];
  isLoading: boolean;
  error: string | null;
}

const initialState: NewsState = {
  list: [],
  newsList: [],
  banner: [],
  recommend: [],
  newsListHome: [],
  reviewListHome: [],
  isLoading: true,
  error: null,
};

export const getNewsList = createAsyncThunk(
  "newsList/getNewsList",
  async () => {
    const response: any = await request("get", "/api/articles?populate=image", {
      sort: ["id:desc"],
    });
    return response;
  }
);

export const getNewsRecommend = createAsyncThunk(
  "newsList/getNewsRecommend",
  async (newsDetailId: number) => {
    const response: any = await request("get", "/api/articles?populate=image", {
      pagination: {
        limit: 4,
      },
      sort: ["id:desc"],
      filters: {
        isBanner: false,
        id: {
          $nei: newsDetailId,
        },
      },
    });
    return response;
  }
);

export const getNewsBanner = createAsyncThunk(
  "newsList/getNewsBanner",
  async () => {
    const response: any = await request("get", "/api/articles?populate=image", {
      filters: { isBanner: true },
    });
    return response;
  }
);

export const getNewsListHomePage = createAsyncThunk(
  "newsList/getNewsListHomePage",
  async () => {
    const response: any = await request("get", "/api/articles?populate=image", {
      filters: { isBanner: false },
    });
    return response;
  }
);

export const getReviewListHomePage = createAsyncThunk(
  "newsList/getReviewListHomePage",
  async () => {
    const response: any = await request(
      "get",
      "/api/articles?populate=image&&populate=article_cats",
      {
        filters: {
          article_cats: {
            id: {
              $eq: 2,
            },
          },
        },
      }
    );
    return response;
  }
);

const newsSlice = createSlice({
  name: "newsList",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getNewsList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getNewsList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.newsList = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getNewsList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getNewsBanner.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getNewsBanner.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.banner = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getNewsBanner.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getNewsRecommend.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getNewsRecommend.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.recommend = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getNewsRecommend.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getNewsListHomePage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getNewsListHomePage.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.newsListHome = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getNewsListHomePage.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getReviewListHomePage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getReviewListHomePage.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.reviewListHome = payload.data?.map((data) => ({
            id: data.id,
            ...data.attributes,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getReviewListHomePage.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default newsSlice.reducer;
