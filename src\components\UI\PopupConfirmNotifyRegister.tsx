import Button from "@mui/material/Button";
import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { Container, Stack, Typography } from "@mui/material";
import { openChat } from "@/utils/openChat";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { COLORS } from "@/constants/themes";

export default function PopupConfirmNotifyRegister({ open, handleClose }) {
  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );

  const handleCallSupport = () => {
    openChat();
  };
  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        disableEscapeKeyDown={false}
        PaperProps={{
          style: {
            borderRadius: "10px",
            boxShadow: "none",
            margin: "8px",
          },
        }}
      >
        <DialogContent>
          <Container>
            <Stack justifyContent={"center"} alignItems={"center"} gap={2}>
              <img src="/icons/check-ctv.svg" alt="" />
              <Typography color={"#1D1D5E"} fontSize={18} fontWeight={700}>
                Đăng ký thành công
              </Typography>
              <Typography textAlign={"center"} fontSize={13}>
                Cảm ơn Bạn đã đăng ký trở thành CTV bán hàng cùng {APP_NAME}.
                Chúng tôi sẽ xét duyệt thông tin đăng ký của Bạn trong vòng 24h
                làm việc
              </Typography>
              <Stack direction={"row"} gap={2}>
                <Button
                  variant="contained"
                  disableElevation
                  style={{
                    color: "#fff",
                    height: 40,
                    fontSize: "16px",
                    fontWeight: 700,
                    lineHeight: "19.36px",
                    borderRadius: 40,
                    background: COLORS.primary,
                    minWidth: "150px",
                  }}
                  onClick={handleCallSupport}
                >
                  Chat support
                </Button>
              </Stack>
            </Stack>
          </Container>
        </DialogContent>
      </Dialog>
    </>
  );
}
