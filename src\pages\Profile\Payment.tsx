import { List, ListItem, Stack, Typography } from "@mui/material";
import React from "react";
import FrameContainer from "../../components/layout/Container";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { COLORS } from "../../constants/themes";
import PopupEditBank from "../../components/UI/PopupEditBank";
import PopupCccd from "@/components/UI/PopupCccd";

export default function PaymentInfo() {
  const user = useSelector((state: RootState) => state.auth.user);

  const listBankInfo = [
    {
      label: "Ngân hàng",
      value: user?.bankInfo?.bank,
    },
    {
      label: "Chủ tài khoản",
      value: user?.bankInfo?.owner,
    },
    {
      label: "Số tài khoản",
      value: user?.bankInfo?.accountNumber,
    },
  ];

  return (
    <FrameContainer title="Thông tin thanh toán">
      <Stack style={styles.container}>
        <List>
          {listBankInfo.map((item, idx) => (
            <ListItem key={idx}>
              <Stack style={styles.itemContainer} direction="row">
                <span style={{ color: COLORS.accent1 }}>{item.label}</span>
                <span style={{ width: "50%", textAlign: "end" }}>
                  {item.value}
                </span>
              </Stack>
            </ListItem>
          ))}
          {/* <Typography style={styles.validInfo}>
            Thông tin thanh toán đã được xác thực
          </Typography> */}
        </List>
        <Stack direction="row" style={styles.bottomContainer}>
          <PopupEditBank />
        </Stack>
      </Stack>
      <Stack style={styles.container}>
        {user?.cccd && (
          <List>
            <ListItem>
              <Stack style={styles.itemContainer} direction="row">
                <span style={{ color: COLORS.accent1 }}>
                  Số căn cước công dân
                </span>
                <span style={{ width: "50%", textAlign: "end" }}>
                  {user?.cccd}
                </span>
              </Stack>
            </ListItem>
          </List>
        )}
        <Stack direction="row" style={styles.bottomContainer}>
          <PopupCccd />
        </Stack>
      </Stack>
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    background: "#fff",
    borderRadius: 10,
    paddingBlock: 4,
    marginBottom: 10,
  },
  itemContainer: {
    width: "100%",
    justifyContent: "space-between",
    paddingBlock: 2,
  },
  validInfo: {
    textAlign: "center",
    padding: "10px 20px",
    background: COLORS.accent4,
    border: `1px solid ${COLORS.accent1}`,
    color: COLORS.neutral6,
    marginBlock: 8,
    marginInline: 16,
  },
  bottomContainer: {
    justifyContent: "center",
    paddingInline: 12,
  },
};
