import React, { memo } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import PopupEditPassword from "./PopupEditPassword";
import PopupEditProfile from "./PopupEditProfile";
import { Icon } from "@/constants/Assets";
import EditIcon from "@mui/icons-material/Edit";
import { COLORS } from "@/constants/themes";

export default function PopupEditInfo() {
  const [open, setOpen] = React.useState(false);
  const [openSetPassword, setOpenSetPassword] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Button
        onClick={handleClickOpen}
        style={{
          background: COLORS.primary,
          fontWeight: 700,
          color: "#fff",
          minWidth: "200px",
          borderRadius: "99px",
        }}
        endIcon={<EditIcon />}
      >
        Chỉnh sửa
      </Button>

      <PopupEditProfile {...{ open, setOpen }} />
      <PopupEditPassword {...{ openSetPassword, setOpenSetPassword }} />
    </>
  );
}
