import React, { useEffect, useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { getUser, updateMe } from "../../redux/slices/authen/authSlice";
import { showToast } from "../../utils/common";
import {
  Autocomplete,
  Box,
  FormControl,
  InputLabel,
  Stack,
  TextField,
} from "@mui/material";
import { COLORS } from "../../constants/themes";
import { getBank } from "../../redux/slices/address/addressSlice";

type FormData = {
  owner?: string;
  accountNumber?: string;
  bank?: string;
};

export default function PopupEditBank() {
  const [open, setOpen] = useState(false);
  const user = useSelector((state: RootState) => state.auth.user);
  const { bank } = useSelector((state: RootState) => state.address);
  const [isFocusSelect, setFocusSelect] = useState(false);
  const [bankDetails, setBankDetails] = useState<FormData>({
    owner: user?.bankInfo?.owner,
    accountNumber: user?.bankInfo?.accountNumber,
    bank: user?.bankInfo?.bank,
  });

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    dispatch(getBank());
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBankDetails((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };

  function checkValues() {
    let errorMess = "";
    if (!bankDetails.accountNumber) {
      errorMess = "Bạn cần nhập Số tài khoản";
    }
    if (!bankDetails.owner) {
      errorMess = "Bạn cần nhập thông tin Chủ tài khoản";
    }
    if (!bankDetails.bank) {
      errorMess = "Bạn cần chọn Ngân hàng";
    }
    if (errorMess) {
      showToast({
        content: errorMess,
        type: "error",
      });
      return false;
    }
    return true;
  }

  const onSubmit = async () => {
    const validated = checkValues();
    if (!validated) return;

    const res = await dispatch(
      updateMe({ bankInfo: { ...bankDetails } })
    ).unwrap();
    if (res) {
      setOpen(false);
      await dispatch(getUser());
      showToast({
        content: "Cập nhật thông tin thành công",
        type: "success",
      });
    } else {
      showToast({
        content: "Quá trình cập nhật lỗi. Vui lòng thử lại",
        type: "error",
      });
    }
  };

  const handleClose = () => {
    setOpen(false);
    setFocusSelect(false);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  return (
    <Box>
      <Button onClick={handleClickOpen} style={styles.editBtn}>
        {user?.bankInfo ? "Sửa thông tin" : "Thêm thông tin"}
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent>
          {open && (
            <Stack gap={4}>
              <FormControl variant="outlined" fullWidth sx={{ marginTop: 1 }}>
                <InputLabel>{!isFocusSelect ? "Ngân hàng" : null}</InputLabel>
                <Autocomplete
                  disablePortal
                  options={bank}
                  fullWidth
                  renderInput={(params) => (
                    <TextField {...params} name="bank" />
                  )}
                  getOptionLabel={(option: any) =>
                    `${option.vn_name} (${option.shortName})`
                  }
                  defaultValue={(bank as Array<any>).find(
                    (o) => o.shortName === user?.bankInfo?.bank
                  )}
                  onChange={(e, value: any) =>
                    (bankDetails.bank = value?.shortName)
                  }
                  onFocus={() => setFocusSelect(true)}
                />
              </FormControl>
              <TextField
                id="outlined-required"
                label="Chủ tài khoản"
                onChange={handleInputChange}
                name="owner"
                value={bankDetails?.owner || ""}
              />
              <TextField
                id="outlined-required"
                label="Số tài khoản"
                onChange={handleInputChange}
                name="accountNumber"
                value={bankDetails?.accountNumber || ""}
              />
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button type="submit" style={styles.editBtn} onClick={onSubmit}>
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  editBtn: {
    background: COLORS.primary,
    fontWeight: 400,
    color: "#fff",
    width: "120px",
    marginBlock: 8,
  },
};
