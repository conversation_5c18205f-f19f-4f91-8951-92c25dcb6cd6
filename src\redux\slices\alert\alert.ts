import { PayloadAction, createSlice } from "@reduxjs/toolkit";

interface IAlertButton {
  title: string;
  action?: () => void;
  customButtonStyle?: React.CSSProperties;
}

export interface ICustomAlert {
  isShow?: boolean;
  icon?: string;
  title?: string;
  content?: string;
  buttons?: Array<IAlertButton>;
  iconStyle?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  loading?: boolean;
}

interface AlertState {
  alert: ICustomAlert | null;
}

const initialState: AlertState = {
  alert: null,
};

const alertSlice = createSlice({
  name: "alert",
  initialState,
  reducers: {
    setAlert: (state, action: PayloadAction<ICustomAlert | null>) => {
      state.alert = action?.payload;
    },
  },
});

export const { setAlert } = alertSlice.actions;

export default alertSlice.reducer;
