import React from "react";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { Level, LevelIcon, LevelName } from "@/constants/Const";
import { formatPrice } from "@/utils/formatPrice";
import { Icon } from "@/constants/Assets";
import InfoUser from "./InfoUser";
import { COLORS } from "@/constants/themes";

export default function MemberShip() {
  const { user } = useSelector((state: RootState) => state.auth);
  const { teamReport } = useSelector((state: RootState) => state.team);

  if (!user)
    return (
      <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
        <CircularProgress />
      </Stack>
    );

  // const iconCurLevel = LevelIcon[user.level] ?? Icon.verify;
  const iconCurLevel = Icon.memberTQ;
  // const iconNextLevel = LevelIcon[user.level + 1] ?? Icon.verify;
  const iconNextLevel = Icon.memberVip;
  const strNextLevel = LevelName[user.level + 1] ?? "N/A";
  const curLevelValue = parseInt(teamReport?.userLevelData?.curLevelValue);
  const nextLevelValue = parseInt(teamReport?.userLevelData?.nextLevelValue);
  const isCurrentValueMoreThanNextValue = curLevelValue > nextLevelValue;
  const valueToNextLevel = isCurrentValueMoreThanNextValue
    ? 0
    : nextLevelValue - curLevelValue; // more than next level value;
  const currentProcessPercent = nextLevelValue
    ? Math.round(
        (isCurrentValueMoreThanNextValue ? 1 : curLevelValue / nextLevelValue) *
          100
      )
    : 0;

  return (
    <Stack style={{ ...styles.container }}>
      <InfoUser />
      {user.level < Level.DiamondMember && (
        <Stack>
          <Stack
            direction="row"
            alignItems={"center"}
            justifyContent={"center"}
            gap={1}
            paddingTop={2}
          >
            <img src={iconCurLevel} alt="" width={40} />
            <Stack flex={1}>
              <Typography style={{ textAlign: "center" }}>
                Bạn cần tích luỹ thêm{" "}
                <b style={{ color: "#1D1D5E", fontWeight: 700 }}>
                  {formatPrice(valueToNextLevel)}
                </b>{" "}
                để nâng hạng {strNextLevel}
              </Typography>
            </Stack>
            <img src={iconNextLevel} alt="" width={40} />
          </Stack>
          <Stack>
            <Box style={styles.processContainer}>
              <Box
                style={{
                  ...styles.currentProcess,
                  width: `${currentProcessPercent}%`,
                }}
              />
            </Box>
            <Stack direction="row" justifyContent={"space-between"}>
              <Typography>{formatPrice(0)}</Typography>
              <Typography>
                {formatPrice(
                  parseInt(teamReport?.userLevelData?.nextLevelValue)
                )}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    display: "flex",
    borderRadius: 20,
    padding: 12,
    boxShadow: "0px 0px 10px 0px #D9D9D9",
    background: "#fff",
  },
  smallTitle: {
    fontSize: 12,
    fontWeight: 700,
  },
  processContainer: {
    background: "#B3B3B3",
    height: 7,
    width: "100%",
    marginBlock: 2,
  },
  currentProcess: {
    background: COLORS.primary,
    height: 7,
  },
};
