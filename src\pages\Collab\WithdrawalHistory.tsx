import FrameContainer from "@/components/layout/Container";
import ReportCard from "@/components/report/ReportCard";
import { Icon } from "@/constants/Assets";
import { COLORS, commonStyle } from "@/constants/themes";
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import store, { RootState } from "../../redux/store";
import DropdownCollabHistory from "../../components/dropdown/DropdownCollabHistory";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  DefaultFilter,
  ERROR_MESSAGE,
  WithdrawalStatusText,
} from "@/constants/Const";
import {
  getPayments,
  withdrawalCommission,
} from "@/redux/slices/payment/paymentSlice";
import NoDataView from "@/components/NoDataView";
import { showToast } from "@/utils/common";
import { getUser } from "@/redux/slices/authen/authSlice";
import { useAlert } from "@/redux/slices/alert/useAlert";
import PopupInputWithdrawal from "@/components/collab/Withdrawal/PopupInputWithdrawal";
import WithdrawalItem from "@/components/collab/Withdrawal/WithdrawalItem";
import { formatPrice } from "@/utils/formatPrice";

export default function WithdrawalHistory() {
  const { payments } = useSelector((state: RootState) => state.payment);
  const { user } = useSelector((state: RootState) => state.auth);
  const [withdrawalStatus, setWithdrawalStatus] = useState(0);
  const [selectedRange, setSelectedRange] = useState<Array<any>>([]);
  const [openWithdrawal, setOpenWithdrawal] = React.useState(false);
  const [loading, setLoading] = useState(false);
  const { showAlert } = useAlert();

  const withdrawalStatuses = [
    DefaultFilter,
    ...Object.keys(WithdrawalStatusText).map((key) => ({
      id: Number(key),
      name: WithdrawalStatusText[key],
    })),
  ];

  const convertRangerDate = (rangerDate: Array<Date>) => {
    const startDate = rangerDate[0];
    const endDate = rangerDate[1];
    const newEndDate = new Date(endDate);
    newEndDate.setDate(newEndDate.getDate() + 1); //increase 1 day because the endDate result start at 00:00
    return {
      startDate: startDate.toISOString(),
      endDate: newEndDate.toISOString(),
    };
  };

  const getPaymentsList = async () => {
    setLoading(true);
    if (selectedRange.length === 2 && !selectedRange.includes(null)) {
      const convertedValue = convertRangerDate(selectedRange);
      await store.dispatch(
        getPayments({
          withdrawalStatus,
          startDate: convertedValue.startDate,
          endDate: convertedValue.endDate,
        })
      );
    } else {
      await store.dispatch(getPayments({ withdrawalStatus }));
    }
    setLoading(false);
  };

  useEffect(() => {
    getPaymentsList();
  }, [selectedRange, withdrawalStatus]);

  const submitWithdrawal = async (values: any) => {
    if (values.amount) {
      showAlert({
        icon: Icon.check,
        title: "Xác nhận rút tiền",
        content: "Bạn có chắc chắn muốn rút tiền không?",
        buttons: [
          {
            title: "Huỷ",
          },
          {
            title: "Xác nhận",
            action: () => onWithdrawal(values.amount),
          },
        ],
      });
    } else {
      showToast({
        content: "Vui lòng nhập số tiền cần rút",
        type: "error",
      });
    }
  };

  const onWithdrawal = async (amount: number) => {
    setOpenWithdrawal(false);
    const res = await store
      .dispatch(withdrawalCommission({ withdrawalValue: amount }))
      .unwrap();
    if (res?.success) {
      setOpenWithdrawal(false);
      await store.dispatch(getUser());
      await getPaymentsList();
      showToast({
        content: "Yêu cầu rút tiền được tạo thành công.",
        type: "success",
      });
    } else {
      showToast({
        content: `${
          ERROR_MESSAGE[res?.message] ||
          "Quá trình xuất hiện lỗi. Vui lòng thử lại"
        }`,
        type: "error",
      });
    }
  };

  const resetPickerDate = () => {
    setSelectedRange([]);
  };
  const items = [
    {
      icon: Icon.icon_dtcn,
      title: "Tổng hoa hồng",
      value: user?.commission.totalCommission,
      isMoney: true,
    },
    {
      icon: Icon.icon_dtcn,
      title: "Hoa hồng đã rút",
      value: user?.commission.withdrawalCommission,
      isMoney: true,
    },
    {
      icon: Icon.icon_dtcn,
      title: "Hoa hồng chưa rút",
      value: user?.commission
        ? user.commission.totalCommission - user.commission.withdrawalCommission
        : 0,
      isMoney: true,
    },
    {
      icon: Icon.icon_dtcn,
      title: "Số dư khả dụng",
      value: user?.balance,
      isMoney: true,
    },
  ];

  return (
    <FrameContainer title="Lịch sử hoa hồng" style={{ background: "#FFF" }}>
      <Stack
        style={{
          ...commonStyle.shadowBorder,
          borderRadius: 10,
          background: COLORS.primary,
          color: "#FFFFFF",
        }}
      >
        <Grid style={{ ...styles.availableCommission }}>
          <Box justifyContent="space-between">
            <Typography
              style={{ ...styles.text, fontSize: 18, fontWeight: 700 }}
            >
              Hoa hồng có thể nhận
            </Typography>
            <Typography
              style={{
                ...styles.text,
                fontSize: 30,
                fontWeight: 700,
                textAlign: "center",
              }}
            >
              {formatPrice(
                user?.commission
                  ? user.commission.totalCommission -
                      user.commission.withdrawalCommission
                  : 0
              )}
            </Typography>
          </Box>
          {/* <Box>
            <Button
              style={{
                backgroundColor: "#FFFFFF",
                borderRadius: 100,
                padding: "5px 12px",
              }}
              onClick={() => {
                setOpenWithdrawal(true);
              }}
            >
              Rút tiền
            </Button>
          </Box> */}
        </Grid>
      </Stack>
      <ReportCard items={items} />
      {/* <Button
        style={{
          width: "100%",
          paddingBlock: 8,
          background: COLORS.primary,
          borderRadius: 5,
          textAlign: "center",
          color: "#FFF",
          fontWeight: 700,
          marginTop: 16,
        }}
        onClick={() => setOpenWithdrawal(true)}
      >
        Rút tiền
      </Button> */}
      <Grid
        container
        justifyContent="center"
        spacing={2}
        style={{ paddingBlock: 16 }}
      >
        <Grid item xs={6} sm={12}>
          <DatePicker
            className="datepicker-collab-order-history"
            selected={selectedRange[0]}
            onChange={setSelectedRange}
            startDate={selectedRange[0]}
            placeholderText="Toàn thời gian"
            endDate={selectedRange[1]}
            selectsRange
          />
          {selectedRange.length > 0 && (
            <Typography sx={styles.resetBtn} onClick={resetPickerDate}>
              x
            </Typography>
          )}
        </Grid>
        <Grid item xs={6} sm={12}>
          <DropdownCollabHistory
            label=""
            options={withdrawalStatuses}
            defaultValue={withdrawalStatus}
            onChange={setWithdrawalStatus}
          />
        </Grid>
      </Grid>
      <Stack gap={1}>
        {loading ? (
          <Stack justifyContent={"center"} alignItems={"center"} paddingTop={4}>
            <CircularProgress />
          </Stack>
        ) : payments?.length > 0 ? (
          payments.map((payment, index) => (
            <WithdrawalItem item={payment} key={index} />
          ))
        ) : (
          <NoDataView content="Không có lịch sử hoa hồng" />
        )}
      </Stack>
      <PopupInputWithdrawal
        {...{ openWithdrawal, setOpenWithdrawal, submitWithdrawal }}
      />
    </FrameContainer>
  );
}

const styles: Record<string, React.CSSProperties> = {
  title: {
    fontSize: 14,
    fontWeight: 700,
    color: COLORS.blue,
  },
  resetBtn: {
    width: 16,
    height: 16,
    position: "relative",
    left: "88%",
    top: "-50%",
    textAlign: "center",
    alignContent: "center",
    borderRadius: 8,
    background: "#E3E6E8",
    color: COLORS.gray,
    fontSize: 9,
    paddingTop: -1,
  },
  availableCommission: {
    width: "100%",
    padding: 12,
    display: "flex",
    justifyContent: "space-around",
    alignItems: "center",
  },
  text: {
    fontWeight: 700,
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
};
