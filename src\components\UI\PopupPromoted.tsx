import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import { Box, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../redux/store";
import { LevelBg } from "../../constants/Const";
import { updateUser } from "../../redux/slices/authen/authSlice";
import CloseIcon from "@mui/icons-material/Close";

export default function PopupPromoted() {
  const dispatch = useDispatch<AppDispatch>();
  const [open, setOpen] = React.useState(true);
  const { user, userZalo } = useSelector((state: RootState) => state.auth);

  const handleClose = async () => {
    setOpen(false);

    if (!user) return;
    await dispatch(
      updateUser({
        extra: {
          showPopupNewLevel: false,
        },
      })
    );
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        maxWidth="lg"
        fullWidth={true}
        sx={{
          ".MuiPaper-root": {
            overflow: "hidden",
            borderRadius: "10px",
          },
        }}
      >
        <>
          <img
            src={(user?.level && LevelBg[user?.level]) || ""}
            style={{
              borderRadius: "10px",
              width: "100%",
            }}
            alt=""
          />
          <Box
            sx={{
              position: "absolute",
              bottom: "55.2%",
              left: "27.5%",
              borderRadius: "50%",
              width: "100%",
            }}
          >
            <img
              style={{
                position: "absolute",
                zIndex: 11,
                width: "45%",
                borderRadius: "50%",
              }}
              src={userZalo?.avatar || user?.avatarUrl}
              alt=""
            />
          </Box>
          <Box
            sx={{
              position: "absolute",
              bottom: "13%",
              borderRadius: "50%",
              width: "100%",
            }}
          >
            <Typography
              sx={{
                color: "white",
                textAlign: "center",
                fontWeight: "bold",
                fontSize: "24px",
              }}
              variant="body2"
            >
              {userZalo?.name || user?.name}
            </Typography>
          </Box>
        </>
        <DialogActions
          style={{
            margin: 0,
            paddingBottom: 18,
            position: "absolute",
            right: "-15px",
          }}
        >
          <Stack
            gap={2}
            direction="row"
            justifyContent={"center"}
            width={"100%"}
          >
            <Button
              style={{ color: "white", borderRadius: 99 }}
              onClick={handleClose}
            >
              <CloseIcon />
            </Button>
          </Stack>
        </DialogActions>
      </Dialog>
    </>
  );
}
