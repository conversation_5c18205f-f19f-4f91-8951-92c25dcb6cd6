import React from "react";
import { Box, Grid, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { COLORS, commonStyle } from "@/constants/themes";
import { formatPrice } from "@/utils/formatPrice";
import GradientText from "@/components/UI/GradientTextProps";

export type ReportCardItem = {
  icon: string;
  title: string;
  value?: any;
  isMoney?: boolean;
  onClick?: () => void;
};

interface IReportCard {
  items: Array<ReportCardItem>;
  containerStyles?: React.CSSProperties;
}

export default function ReportCard({ items, containerStyles }: IReportCard) {
  const theme = useTheme();

  return (
    <Grid container spacing={2} style={{ marginTop: 0, ...containerStyles }}>
      {items.map((item, index) => (
        <Grid item xs={6} key={String(index)} onClick={item.onClick}>
          <Box
            sx={{ ...commonStyle.shadowBorder, ...styles.container }}
            alignItems="center"
            justifyContent="space-around"
          >
            <img height={31} width={31} src={item.icon} alt="" />
            <Box style={{ overflow: "hidden" }}>
              <GradientText
                style={{ ...styles.text, fontSize: 11, paddingBottom: 8 }}
                text={item.title}
              />
              <br />
              <GradientText
                style={styles.text}
                text={item.isMoney ? formatPrice(item.value || 0) : item.value}
              />
            </Box>
          </Box>
        </Grid>
      ))}
    </Grid>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    display: "flex",
    gap: 1,
    height: 84,
    padding: 0.5,
  },
  text: {
    color: COLORS.blue,
    fontWeight: 700,
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
};
