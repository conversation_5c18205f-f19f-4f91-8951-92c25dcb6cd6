import {
  Box,
  Container,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";

import { useTheme } from "@mui/material/styles";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import zIndex from "@mui/material/styles/zIndex";
import { COLORS } from "@/constants/themes";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
export default function LayoutAccountPage({ children }: any) {
  const APP_NAME = useSelector(
    (state: RootState) => state.config.data.appName ?? ""
  );
  const APP_SLOGAN = useSelector(
    (state: RootState) => state.config.data.appSlogan ?? ""
  );

  return (
    <Box style={{ position: "relative", paddingBottom: "100px" }}>
      <Box
        style={{
          background: COLORS.colorHeader,
          position: "absolute",
          width: "100%",
          height: "170px",
          zIndex: "-1",
          borderRadius: "0 0 25px 25px",
        }}
      ></Box>
      <Box
        style={{
          paddingTop: "50px",
          paddingBottom: "10px",
        }}
      >
        <Container>
          <Stack direction="row" gap={2} alignItems={"center"}>
            <img
              src="/images/logo.png"
              alt="Logo"
              style={{
                width: "120px",
                height: "100%",
                objectFit: "contain",
              }}
            />
            <Box sx={{ color: COLORS.white }}>
              <Typography fontWeight={700} fontSize={16}>
                {APP_NAME}
              </Typography>
              <Typography fontSize={12} marginLeft={0.2}>
                {APP_SLOGAN}
              </Typography>
            </Box>
          </Stack>
        </Container>
      </Box>
      {children}
    </Box>
  );
}
