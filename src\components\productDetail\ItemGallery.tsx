import { IProductImage } from "@/types/product";
import React, { useEffect, useState } from "react";
import ImageGallery from "react-image-gallery";

export interface IImage {
  original: string;
  thumbnail: string;
  type: string;
  alt: string;
}

interface ItemGalleryProps {
  images?: IProductImage[];
}

export const ItemGallery = ({ images }: ItemGalleryProps) => {
  const [loadingImages, setLoadingImages] = useState<boolean>(true);
  const [processedImages, setProcessedImages] = useState<IImage[]>([]);
  const [displayImages, setDisplayImages] = useState<IImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    return () => {
      setLoadingImages(true);
      setProcessedImages([]);
      setDisplayImages([]);
    };
  }, []);

  useEffect(() => {
    if (!images || !images.length) {
      return;
    }
    try {
      processImages(images).then((processedImages) => {
        setProcessedImages(processedImages);
      });
    } catch (e) {
      console.error(e);
    }
  }, [images]);

  useEffect(() => {
    if (processedImages.length === 0) {
      return;
    }
    try {
      setDisplayImages(
        processedImages.map((img) => ({
          ...img,
          renderItem: () => {
            if (img.type.includes("image/")) {
              return (
                <img
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    aspectRatio: 1,
                  }}
                  role="presentation"
                  src={img.original}
                  alt={img.alt}
                />
              );
            }
            // if (img.type.includes("video/")) {
            //   return <CustomVideo src={img.original} type={img.type} />;
            // }
            return <></>;
          },
          renderThumbInner: () => {
            if (img.type.includes("video/")) {
              return (
                <video
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "fill",
                  }}
                  controlsList="nodownload nofullscreen noremoteplayback"
                  preload="metadata"
                  disablePictureInPicture={true}
                  disableRemotePlayback={true}
                >
                  <source src={`${img.thumbnail}#t=0.1`} type={img.type} />
                </video>
              );
            }
            return (
              <img
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  aspectRatio: 4 / 3,
                }}
                role="presentation"
                src={img.thumbnail}
                alt={img.alt}
              />
            );
          },
        }))
      );
    } catch (e) {
      console.error(e);
    } finally {
      setLoadingImages(false);
    }
  }, [processedImages]);

  const processImages = async (images: IProductImage[]) => {
    const newImages: IImage[] = images.map((img) => ({
      original: img.url,
      thumbnail: img.url,
      type: img.mime || "image/png",
      alt: img.name || "alt",
    }));

    return await Promise.all(
      newImages.map(async (image) => {
        return {
          ...image,
          thumbnail: await generateThumbnail(image),
        };
      })
    );
  };

  const generateThumbnail = async (image: IImage): Promise<string> => {
    let thumbnail = "";
    if (image.type.includes("image/")) {
      thumbnail = image.original;
      // thumbnail = await generateSmallImage(image);
    }
    if (image.type.includes("video/")) {
      thumbnail = image.original;
      // thumbnail = await captureVideoThumbnail(image.original);
    }
    return thumbnail;
  };

  if (loadingImages) {
    return <div style={{ height: "40dvh" }}></div>;
  }
  const handleSlide = (currentIndex) => {
    setCurrentIndex(currentIndex);
  };

  return (
    <ImageGallery
      items={displayImages}
      onSlide={handleSlide}
      showPlayButton={false}
      showFullscreenButton={false}
      showNav={false}
      autoPlay={false}
      swipingTransitionDuration={200}
      showThumbnails={false}
      renderCustomControls={() => {
        if (!images || images.length === 0) {
          return <></>;
        }
        return (
          <div
            className="total-file-gallery"
            style={{
              position: "absolute",
              bottom: "5px",
              right: "5px",
              padding: "10px 7px",
              // backgroundColor: COLORS.red1,
              // color: COLORS.white,
              borderRadius: "10px",
              zIndex: 1,
              fontSize: 11,
            }}
          >
            {`${currentIndex + 1}/${images.length}`}
          </div>
        );
      }}
    />
  );
};
