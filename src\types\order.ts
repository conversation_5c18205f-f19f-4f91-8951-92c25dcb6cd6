import { IProductGift } from "./product";

export interface IOrder {
  id: number;
  orderData: {
    ship: number;
    commissionDiscount?: number;
    userInfo: {
      level?: number;
    };
    items: {
      product: {
        id: number;
        name: string;
        image: {
          data: [
            {
              id: number;
              attributes: {
                ext: string;
                url: string;
                hash: string;
                mime: string;
                name: string;
                size: number;
                width: number;
                height: number;
                caption?: string | null;
                formats: {
                  thumbnail: {
                    ext: string;
                    url: string;
                    hash: string;
                    mime: string;
                    name: string;
                    path?: string | null;
                    size: number;
                    width: number;
                    height: number;
                  };
                };
                provider: string;
                createdAt: string;
                updatedAt: string;
                previewUrl?: string | null;
                alternativeText?: string | null;
                provider_metadata?: any;
              };
            }
          ];
        };
        price: number;
        discount: number;
        quantity: number;
        createdAt: string;
        updatedAt: string;
        bonusPoint: number;
        commission: number;
        description: string;
        publishedAt: string;
      };
      quantity: number;
      promotion?: IProductGift;
    }[];
  };
  discountValue: number;
  finalPrice: number;
  depositValue: number;
  note?: string | null;
  orderId: string;
  voucherId?: string | null;
  paymentMethod: number;
  createdAt: string;
  updatedAt: string;
  orderStatus: number;
  totalCommission: number;
  receiver: {
    id: number;
    note?: string | null;
    ward: string;
    phone: string;
    address: string;
    district: string;
    province: string;
    userName: string;
    createdAt: string;
    updatedAt: string;
    addressObj: {
      wardText: string;
      districtText: string;
      provinceText: string;
    };
  };
}
