import {
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  Icon<PERSON>utton,
  Slide,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import HorizontalRuleIcon from "@mui/icons-material/HorizontalRule";
import AddIcon from "@mui/icons-material/Add";
import { TransitionProps } from "@mui/material/transitions";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CloseIcon from "@mui/icons-material/Close";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS } from "@/constants/themes";
import GradientText from "../UI/GradientTextProps";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function ChangeInfoATC({ item, onChangeQuantity }) {
  const [open, setOpen] = React.useState(false);
  const [quantity, setQuantity] = useState(0);

  useEffect(() => {
    setQuantity(item?.quantity);
  }, [item?.quantity]);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const reduce = () => {
    setQuantity((quantity) => (quantity > 0 ? quantity - 1 : quantity));
  };
  const increase = () => {
    setQuantity(quantity + 1);
  };

  return (
    <>
      <Stack onClick={handleClickOpen} direction={"row"} alignItems={"center"}>
        <img width={60} height={60} src={item.product?.image?.[0]?.url} />
        <Stack direction={"column"} ml={2} flex={1}>
          <Typography style={{ color: "#000" }}>{item.product.name}</Typography>
          <span
            style={{
              color: COLORS.primary,
              fontWeight: 700,
              fontSize: 16,
            }}
          >
            <GradientText
              text={`${formatPrice(
                item.product?.price - item.product?.discount || 0
              )} x ${item.quantity}`}
            />
          </span>
        </Stack>
      </Stack>
      <Dialog
        className="popup-add-to-cart"
        open={open}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        {/* <DialogTitle>{"Add To Cart"}</DialogTitle> */}
        <DialogContent>
          <Stack
            direction="row"
            width={"100%"}
            style={{
              border: "1px solid #D9D9D9",
              borderRadius: 99,
              padding: "6px 0px",
            }}
            justifyContent="space-between"
            alignItems={"center"}
          >
            <IconButton
              style={{
                marginInline: 8,
                background: "#EDF7FE",
                borderRadius: "50%",
              }}
              aria-label="fingerprint"
              color="secondary"
              onClick={reduce}
            >
              <HorizontalRuleIcon />
            </IconButton>

            <Stack direction="row" alignItems={"center"} gap={2}>
              <span>Số lượng</span>
              <input
                style={{
                  width: 60,
                  height: 30,
                  borderRadius: 20,
                  border: "1px solid #D9D9D9",
                  textAlign: "center",
                }}
                onChange={(e) => {
                  setQuantity(Number(e.target.value));
                }}
                value={quantity}
              />
            </Stack>
            <IconButton
              style={{
                marginInline: 8,
                background: "#EDF7FE",
                borderRadius: "50%",
              }}
              aria-label="fingerprint"
              color="secondary"
              onClick={increase}
            >
              <AddIcon />
            </IconButton>
          </Stack>
        </DialogContent>
        <DialogActions
          sx={{ justifyContent: "center", gap: 2, marginBlock: 2 }}
        >
          <Button
            style={{
              background: "#EDF7FE",
              color: "#1D1D5E",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
            }}
            onClick={handleClose}
            startIcon={<CloseIcon />}
          >
            Hủy bỏ
          </Button>
          <Button
            style={{
              background: COLORS.primary,
              color: "#fff",
              margin: 0,
              height: "100%",
              padding: "10px 30px",
              borderRadius: 99,
              fontSize: 12,
            }}
            onClick={() => {
              if (item?.product?.id) {
                onChangeQuantity(item.product.id, quantity);
                handleClose();
              }
            }}
            startIcon={<CheckCircleOutlineIcon />}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
