import { Box, Container, Grid, Stack, CircularProgress } from "@mui/material";

import React, { useEffect, useState, useRef } from "react";
import FrameContainerFull from "../../components/layout/ContainerFluid";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { INews } from "../../types/news";
import { useNavigate } from "../../utils/component-util";
import { request } from "../../utils/request";

const getNewsList = async ({ page }) => {
  try {
    const response: any = await request("get", "/api/articles?populate=image", {
      pagination: {
        pageSize: 10,
        page: page,
        withCount: true,
      },
      sort: ["id:desc"],
    });

    return {
      ...response,
      data: response?.data?.map((data) => ({
        id: data.id,
        ...data.attributes,
      })),
    };
  } catch (e) {
    return {
      data: [],
      meta: {
        pagination: {
          total: 0,
        },
      },
    };
  }
};
export default function Posts() {
  const navigate = useNavigate();
  const newsList = useSelector((state: RootState) => state.news.newsList);
  const [list, setList] = useState(newsList);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const loader = useRef(null);

  const getList = async () => {
    try {
      setLoading(true);
      const response = await getNewsList({ page });
      setList((prevList) =>
        page === 1 ? response?.data : [...prevList, ...response?.data]
      );
      setTotal(response?.meta?.pagination?.total);
    } catch (e) {
      console.log("getNewsListError", e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getList();
  }, [page]);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "20px",
      threshold: 1.0,
    };

    const observer = new IntersectionObserver(handleObserver, options);

    if (loader.current) {
      observer.observe(loader.current);
    }

    return () => {
      if (loader.current && list?.length < total) {
        observer.unobserve(loader.current);
      }
    };
  }, []);

  const handleObserver = (entities) => {
    const target = entities[0];
    if (target.isIntersecting) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <FrameContainerFull
      title="Tin tức"
      overrideStyle={{ background: "transparent" }}
    >
      <Stack paddingBlock={2} style={{ background: "#fff" }}>
        <Container>
          <p
            style={{
              margin: 1,
              fontWeight: 700,
              fontSize: 18,
              background: "#fff",
            }}
          >
            Danh mục
          </p>
        </Container>
      </Stack>

      <Stack style={{ background: "#FFF", marginTop: 12 }}>
        <Grid container style={{ borderRadius: 10, padding: 8 }} pb={4}>
          {list?.length ? (
            list.map((news: INews, index) => (
              <Grid key={`news-${index}`} item xs={6} sx={{ padding: 1 }}>
                <Stack
                  fontSize={14}
                  onClick={() => {
                    navigate(`/posts/${news.id}`);
                  }}
                  key={news.id}
                >
                  <img
                    width="100%"
                    style={{
                      aspectRatio: 1,
                      objectFit: "cover",
                      backgroundColor: "gray",
                    }}
                    src={`${import.meta.env.VITE_API_URL}${
                      news?.image?.data?.attributes?.formats?.small?.url
                    }`}
                    alt={`${news.title}`}
                  />
                  <Box p={1}>
                    <p
                      style={{
                        fontWeight: 700,
                        color: "#555555",
                        marginBottom: 0,
                      }}
                    >
                      {news.title}
                    </p>
                    <span
                      style={{
                        fontWeight: 500,
                        fontSize: 12,
                        color: "#969595",
                      }}
                    >
                      {dayjs(news.createdAt).format("HH:mm, DD/MM")}
                    </span>
                  </Box>
                </Stack>
              </Grid>
            ))
          ) : (
            <Stack
              direction="row"
              justifyContent={"center"}
              padding={2}
              width={"100%"}
            >
              Không có dữ liệu
            </Stack>
          )}
        </Grid>
        {loading && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            padding={4}
          >
            <CircularProgress size={18} />
          </Box>
        )}
        <Box ref={loader} />
      </Stack>
    </FrameContainerFull>
  );
}
