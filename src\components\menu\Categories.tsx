import React, { useEffect, useState } from "react";
import { Tab, Tabs, Box } from "@mui/material";
import store, { RootState } from "../../redux/store";
import { getProductCategoryList } from "../../redux/slices/product/productSlice";
import { useSelector } from "react-redux";
import { IProductCategory } from "../../types/product";
import { useTheme } from "@mui/material/styles";
import { useNavigate } from "../../utils/component-util";
import { COLORS } from "@/constants/themes";
import GradientText from "../UI/GradientTextProps";

interface CategoriesProps {
  onChange: (categoryId: number) => void;
  categoryId: number | null;
}

const Categories: React.FC<CategoriesProps> = ({ onChange, categoryId }) => {
  const [value, setValue] = useState<number | null>(categoryId || 0);
  const navigate = useNavigate();
  const theme = useTheme();

  const { productCategoryList: productCategoryList } = useSelector(
    (state: RootState) => state.product
  );

  useEffect(() => {
    if (!Array.isArray(productCategoryList) || !productCategoryList.length)
      getList();
  }, []);

  const getList = async () => {
    store.dispatch(getProductCategoryList());
  };

  const handleCategoryChange = (event: React.SyntheticEvent, value: number) => {
    setValue(value);
    onChange(value);
  };

  return (
    <Tabs
      className="category-product-page"
      value={value}
      style={{ height: 40, minHeight: 40, marginTop: 0, paddingInline: 10 }}
      onChange={handleCategoryChange}
      variant="scrollable"
      scrollButtons="auto"
      aria-label="scrollable auto tabs example"
      TabIndicatorProps={{
        children: (
          <span
            className="category-product-page-indicatorSpan"
            style={{ background: COLORS.primary }}
          />
        ),
        style: {
          display: "flex",
          justifyContent: "center",
          backgroundColor: "transparent",
        },
      }}
    >
      {/* <Tab
        value={0}
        label={
          <div
            className="category-product-page-label"
            style={{
              fontSize: 14,
            }}
          >
            Tất cả
          </div>
        }
      /> */}
      {productCategoryList?.map((item: IProductCategory) => (
        <Tab
          className="category-product-page-item"
          key={item.id}
          style={{
            width: "fit-content",
            color: value === item.id ? theme.palette.primary.main : COLORS.gray,
            fontWeight: value === item.id ? 700 : 400,
            // maxWidth: "100px",
          }}
          label={
            value === item.id ? (
              <GradientText
                text={item.name}
                style={{
                  fontSize: 16,
                }}
              />
            ) : (
              <div
                className="category-product-page-label"
                style={{
                  fontSize: 16,
                }}
              >
                {item.name}
              </div>
            )
          }
          value={item.id}
          onClick={() => {
            navigate("/product", { state: { id: item.id } });
          }}
        />
      ))}
    </Tabs>
  );
};

export default Categories;
