import React from "react";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS, commonStyle } from "../../constants/themes";
import { Typography } from "@mui/material";
import { VoucherText, VoucherType } from "../../constants/Const";
import { IVoucher } from "../../types/voucher";

const VoucherTypeDesc = (item: IVoucher) => {
  const { type, discountValue, minPrice } = item;
  switch (type) {
    case VoucherType.DiscountPrice:
      return (
        <Typography
          style={{ ...commonStyle.headline12, color: COLORS.accent1 }}
        >
          <PERSON><PERSON><PERSON><PERSON> {formatPrice(discountValue)} cho đơn từ {formatPrice(minPrice)}
        </Typography>
      );
    case VoucherType.DiscountPercent:
      return (
        <Typography
          style={{ ...commonStyle.headline12, color: COLORS.accent1 }}
        >
          <PERSON><PERSON><PERSON><PERSON> {discountValue}% cho đơn từ {formatPrice(minPrice)}
        </Typography>
      );
    case VoucherType.FreeShip:
      return (
        <Typography
          style={{ ...commonStyle.headline12, color: COLORS.accent1 }}
        >
          <PERSON><PERSON><PERSON> phí giao hàng cho đơn từ {formatPrice(minPrice)}
        </Typography>
      );
    default:
      return null;
  }
};

const VoucherTypeLabel = (item: IVoucher) => {
  const { type } = item;
  return <Typography style={{ fontSize: 12 }}>{VoucherText[type]}</Typography>;
};

const SelectedVoucher = (item: IVoucher) => {
  const { type, discountValue } = item;
  if (!type) return null;
  switch (type) {
    case VoucherType.DiscountPrice:
      return `Giảm ${formatPrice(discountValue)}`;
    case VoucherType.DiscountPercent:
      return `Giảm ${discountValue}%`;
    case VoucherType.FreeShip:
      return `Miễn phí giao hàng`;
    default:
      return "";
  }
};

export { VoucherTypeDesc, VoucherTypeLabel, SelectedVoucher };
