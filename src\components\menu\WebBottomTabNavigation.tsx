import {
  Badge,
  BottomNavigation,
  BottomNavigationAction,
  Paper,
} from "@mui/material";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { NO_BOTTOM_NAVIGATION_PAGES } from "../../constants/Const";
import { useLocation } from "react-router-dom";
import { useCart } from "../../hooks/useCart";
import { BottomTabs } from "./BottomTabs";
import { COLORS } from "../../constants/themes";

export default function WebBottomTabNavigation() {
  const { cart } = useCart();
  const [activeTab, setActiveTab] = useState("chat");
  const location = useLocation();

  useEffect(() => {
    setActiveTab(location.pathname);
  }, [location]);

  const totalProduct = useMemo(() => {
    if (!Array.isArray(cart?.items) || !cart?.items.length) return 0;
    return cart?.items.reduce((acc, item) => acc + item.quantity, 0);
  }, [cart]);

  const noBottomNav = useMemo(() => {
    return NO_BOTTOM_NAVIGATION_PAGES.some((path) =>
      location.pathname.includes(path)
    );
  }, [location]);

  if (noBottomNav) {
    return null;
  }
  const clearHoldPosition = () => {
    sessionStorage.removeItem("scroll-position");
  };

  return (
    <Paper
      className="web-bottom-tab-navigation"
      sx={{ position: "fixed", bottom: 0, width: "100%" }}
      elevation={3}
    >
      <BottomNavigation
        showLabels
        value={activeTab}
        onChange={(event, newValue) => {
          setActiveTab(newValue);
        }}
      >
        {BottomTabs.map((item) => (
          <BottomNavigationAction
            // onClick={clearHoldPosition}
            label={item.label}
            href={item.value}
            icon={
              item.value === "/cart" && totalProduct ? (
                <Badge
                  badgeContent={totalProduct}
                  color="error"
                  sx={{
                    span: {
                      height: "18px",
                      minWidth: "18px",
                    },
                  }}
                >
                  {item.icon(COLORS.default)}
                </Badge>
              ) : (
                item.icon(
                  activeTab === item.value
                    ? COLORS.primaryActive
                    : COLORS.default
                )
              )
            }
            key={item.value}
            style={
              activeTab === item.value
                ? { color: COLORS.primaryActive }
                : { color: COLORS.default }
            }
          />
        ))}
      </BottomNavigation>
    </Paper>
  );
}
