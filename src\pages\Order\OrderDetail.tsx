import { Box, Button, Stack, Typography } from "@mui/material";
import React, { useEffect } from "react";
import FrameContainer from "../../components/layout/Container";
import { Icon } from "../../constants/Assets";
import { ZaloIcon } from "../../constants/IconSvg";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  getOrderDetail,
  updateOrder,
} from "../../redux/slices/order/orderSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { formatPrice } from "../../utils/formatPrice";
import moment from "moment";
import {
  Env,
  LevelName,
  MethodText,
  OrderPrefix,
  OrderStatus,
  OrderStatusText,
  PaymentMethods,
} from "../../constants/Const";
import { useTheme } from "@mui/material/styles";
import { APP_NAME } from "../../constants/AppInfo";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { useNavigate } from "../../utils/component-util";
import { openChat } from "../../utils/openChat";
import { Router } from "@/constants/Route";
import GradientText from "@/components/UI/GradientTextProps";
import { COLORS } from "@/constants/themes";

export default function OrderDetail() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const params = useParams();
  const theme = useTheme();
  const { orderDetail } = useSelector((state: RootState) => state.order);
  const { showAlert } = useAlert();

  useEffect(() => {
    if (!params?.id) return;

    dispatch(getOrderDetail(params.id));
  }, [params?.id]);

  const onCancelOrder = async () => {
    if (!params.id) return;

    const res = await dispatch(
      updateOrder({
        id: params.id,
        orderStatus: OrderStatus.Cancelled,
      })
    );

    if (res) navigate("/order");
  };

  const onClickCancelButton = () => {
    showAlert({
      icon: Icon.warning,
      title: "Xác nhận hủy đơn hàng",
      content: "Bạn có chắc chắn muốn hủy đơn hàng không?",
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: onCancelOrder,
        },
      ],
    });
  };

  const onClickRePayButton = () => {
    navigate(Router.bankTransfer.index, {
      state: {
        id: orderDetail?.id,
        finalPrice: orderDetail?.finalPrice,
        depositValue: orderDetail?.depositValue,
      },
    });
  };

  return (
    <FrameContainer title="Thông tin đơn hàng">
      <Stack>
        <Stack borderRadius={4} bgcolor="#fff" padding={2}>
          <Stack direction="row" paddingBottom={2} gap={2}>
            <img src={Icon.ship} />
            <b>
              <GradientText text="Địa chỉ nhận hàng" />
            </b>
          </Stack>
          <Stack gap={2}>
            <Stack direction="row" gap={2} alignItems={"center"}>
              <Stack gap={1}>
                <Typography fontSize={14} color={"#666666"}>
                  {orderDetail?.receiver?.userName}
                </Typography>
                <Typography fontSize={14} color={"#666666"}>
                  {orderDetail?.receiver?.phone}
                </Typography>
                <Typography fontSize={14} color={"#666666"}>
                  {orderDetail?.receiver.address},{" "}
                  {orderDetail?.receiver?.addressObj?.wardText},{" "}
                  {orderDetail?.receiver?.addressObj?.districtText},{" "}
                  {orderDetail?.receiver?.addressObj?.provinceText}
                </Typography>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        <Stack borderRadius={4} bgcolor="#fff" padding={2} mt={2}>
          <Stack direction="row" paddingBottom={2} gap={2} color="#1D1D5E">
            <img src={Icon.package} />
            <b>
              <GradientText text="Sản phẩm đặt mua" />
            </b>
          </Stack>
          <Stack gap={2}>
            {orderDetail?.orderData.items?.map((item) => (
              <Stack
                key={`${item.product.id}`}
                direction="row"
                gap={2}
                alignItems={"center"}
              >
                <img
                  width={60}
                  src={
                    item.product?.image?.[0]?.url ||
                    `${Env.BackendUrl}${item?.product?.image?.data?.[0]?.attributes?.url}`
                  }
                />
                <Stack color={theme.palette.primary.main} gap={1}>
                  <Typography fontSize={14} color={"#252627"}>
                    {item?.product?.name}
                  </Typography>
                  {/* {item?.promotion ? (
                    <Box>
                      <Typography
                        fontSize={"12px"}
                        fontWeight={700}
                        color={theme.palette.primary.main}
                        mb={0.2}
                      >
                        Quà tặng
                      </Typography>
                      <Typography color={"#000"} fontSize={12}>
                        {item?.promotion?.product?.name}
                        <span
                          style={{
                            color: theme.palette.primary.main,
                            fontWeight: "700",
                            paddingLeft: "5px",
                            fontSize: "12px",
                          }}
                        >
                          x
                          {Number(item?.promotion.quantity) *
                            Number(item.quantity)}
                        </span>
                      </Typography>
                    </Box>
                  ) : null} */}
                  <GradientText
                    style={{
                      fontWeight: 700,
                      fontSize: 16,
                    }}
                    text={`${formatPrice(
                      item.product?.price || 0 - item.product?.discount || 0,
                      "đ"
                    )} x ${item.quantity}`}
                  />
                </Stack>
              </Stack>
            ))}
          </Stack>
        </Stack>
        <Stack borderRadius={4} bgcolor="#fff" padding={2} mt={2}>
          <Stack direction="row" paddingBottom={2} gap={2} color="#1D1D5E">
            <img src={Icon.payment} />
            <b>
              <GradientText text="Chi tiết thanh toán" />
            </b>
          </Stack>
          <Stack gap={2}>
            <Stack gap={1} alignItems={"center"}>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14} color={"#666666"}>
                  Tổng tiền hàng
                </Typography>
                <Typography fontSize={14}>
                  {orderDetail !== null
                    ? formatPrice(
                        Number(orderDetail.finalPrice) +
                          Number(
                            orderDetail?.orderData?.commissionDiscount || 0
                          ) +
                          Number(orderDetail.discountValue || 0),
                        "đ"
                      )
                    : 0}
                </Typography>
              </Stack>
              {/* <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Stack direction={"column"}>
                  <Typography fontSize={14} color={"#666666"}>
                    Phí vận chuyển
                  </Typography>
                </Stack>
                <Typography fontSize={14}>{formatPrice(23000)}</Typography>
              </Stack>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14} color={"#666666"}>
                  Giảm giá phí vận chuyển
                </Typography>
                <Typography fontSize={14}>0đ</Typography>
              </Stack> */}
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14} color={"#666666"}>
                  Chiết khấu{" "}
                  {orderDetail?.orderData?.userInfo?.level &&
                  orderDetail?.orderData?.userInfo?.level > 0
                    ? LevelName[orderDetail?.orderData?.userInfo?.level]
                    : " "}
                </Typography>
                <Typography fontSize={14}>
                  {formatPrice(orderDetail?.totalCommission || 0, "đ")}
                </Typography>
              </Stack>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14} color={"#666666"}>
                  Voucher giảm giá{" "}
                </Typography>
                <Typography fontSize={14}>
                  {formatPrice(orderDetail?.discountValue || 0, "đ")}
                </Typography>
              </Stack>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <GradientText
                  style={{
                    fontWeight: 700,
                    fontSize: 14,
                  }}
                  text="Thành tiền"
                />
                <GradientText
                  style={{
                    fontWeight: 700,
                    fontSize: 14,
                  }}
                  text={formatPrice(orderDetail?.finalPrice || 0, "đ")}
                />
              </Stack>
              {/* {orderDetail?.depositValue && (
                <Stack
                  direction={"row"}
                  justifyContent="space-between"
                  width={"100%"}
                >
                  <Typography fontWeight={700} color="#1D1D5E" fontSize={14}>
                    Đã đặt cọc
                  </Typography>
                  <Typography fontWeight={700} color="#1D1D5E" fontSize={14}>
                    {formatPrice(orderDetail?.depositValue || 0, "đ")}
                  </Typography>
                </Stack>
              )} */}
            </Stack>
          </Stack>
        </Stack>
        <Stack borderRadius={4} bgcolor="#fff" padding={2} mt={2}>
          <Stack direction="row" paddingBottom={2} gap={2} color="#1D1D5E">
            <img src={Icon.payment} />
            <b>
              <GradientText text="Phương thức thanh toán" />
            </b>
          </Stack>
          <Stack gap={2} paddingBottom={2}>
            <Stack gap={1} alignItems={"center"}>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <span>
                  {MethodText[orderDetail?.paymentMethod || PaymentMethods.Cod]}
                </span>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        <Stack borderRadius={4} bgcolor="#fff" padding={2} mt={2}>
          <Stack gap={2} paddingBottom={2}>
            <Stack gap={1} alignItems={"center"}>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14}>Mã đơn hàng</Typography>
                <Typography fontSize={14}>{orderDetail?.id}</Typography>
              </Stack>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14}>Thời gian đặt hàng</Typography>
                <Typography fontSize={14}>
                  {moment(orderDetail?.createdAt).format("DD-MM-YYYY HH:MM")}
                </Typography>
              </Stack>
              <Stack
                direction={"row"}
                justifyContent="space-between"
                width={"100%"}
              >
                <Typography fontSize={14}>Trạng thái đơn hàng</Typography>
                <Typography fontSize={14}>
                  {OrderStatusText[orderDetail?.orderStatus || 1]}
                </Typography>
              </Stack>
            </Stack>
            <Stack paddingInline={4} gap={2}>
              <Typography
                color={"#666666"}
                fontSize={14}
                style={{ textAlign: "center" }}
              >
                Hãy chat với chúng tôi nếu bạn muốn thay đổi thông tin đơn hàng
                bạn nhé
              </Typography>
              <Button
                variant="outlined"
                style={{
                  borderRadius: 99,
                  borderColor: COLORS.primaryActive,
                  paddingBlock: 8,
                }}
                onClick={openChat}
                fullWidth
                // startIcon={<ZaloIcon />}
              >
                <GradientText text={`Chat với ${APP_NAME} nhé`} />
              </Button>
              {orderDetail?.orderStatus == OrderStatus.Pending && (
                <>
                  {/* <Button
                    variant="outlined"
                    style={{
                      borderRadius: 99,
                      borderColor: "#969595",
                      color: "#969595",
                      paddingBlock: 8,
                    }}
                    onClick={onClickRePayButton}
                  >
                    Thanh toán lại
                  </Button> */}
                  <Button
                    style={{
                      borderRadius: 99,
                      color: "#969595",
                      paddingBlock: 8,
                    }}
                    onClick={onClickCancelButton}
                  >
                    Huỷ đơn hàng
                  </Button>
                </>
              )}
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </FrameContainer>
  );
}
