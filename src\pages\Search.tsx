import {
  Container,
  FormControl,
  InputAdornment,
  Stack,
  TextField,
  Box,
} from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import React, { useState } from "react";
import SearchIcon from "@mui/icons-material/Search";
import FrameContainerNoIcon from "../components/layout/ContainerNoIconHeader";

export default function Search() {
  const [showClearIcon, setShowClearIcon] = useState("none");

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
  };

  const handleClick = (): void => {
    // TODO: Clear the search input
    console.log("clicked the clear icon...");
  };

  return (
    <FrameContainerNoIcon title="Tìm kiếm">
      <Stack>
        <FormControl>
          <TextField
            placeholder="Tìm kiếm sản phẩm"
            style={{ width: "100%" }}
            size="small"
            variant="outlined"
            onChange={handleChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {<SearchIcon />}
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment
                  position="end"
                  style={{ display: showClearIcon }}
                  onClick={handleClick}
                >
                  <ClearIcon />
                </InputAdornment>
              ),
            }}
          />
        </FormControl>
      </Stack>
      <Box>
        <p style={{ fontWeight: 700 }}>Kết quả (0)</p>
        <p style={{ textAlign: "center" }}>
          Không tìm thấy sản phẩm. Vui lòng thử lại
        </p>
      </Box>
    </FrameContainerNoIcon>
  );
}
