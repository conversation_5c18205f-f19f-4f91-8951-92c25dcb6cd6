import React from "react";
import { Box, Grid, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";

import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { formatPrice } from "../../utils/formatPrice";
import { COLORS } from "@/constants/themes";

export type ReportCardItem = {
  icon: string;
  title: string;
  value?: number | string | null;
  isMoney?: boolean;
};

interface IReportCard {
  items: Array<ReportCardItem>;
}

const ReportCard = ({items}:IReportCard) => {
  return (
    <Grid container spacing={2} style={{ marginTop: 0 }} alignContent={"stretch"}>
      {items.map((item, index) => (
        <Grid item xs={6} key={String(index)} style={{ height: "100%" }} >
          <Box
            sx={{
              display: "flex",
              borderRadius: "10px",
              gap: "5px",
              backgroundColor: "#FFFFFF",
              boxShadow: " 0px 0px 10px 0px   #E9EBED",
              textWrap:"nowrap"
            }}
            p={1}
            alignItems="center"
            justifyItems="center"
          >
            <img height={30} width={30} src={item.icon} alt="" />
            <Box>
              <Typography
                style={{
                  color: COLORS.infoText,
                  fontSize: 11,
                  fontWeight: 700,
                }}
              >
                {item.title}
              </Typography>
              <Typography
                color={COLORS.primary}
                style={{ fontSize: 14, fontWeight: 700 }}
              >
                {item.isMoney ? formatPrice(item.value || 0) : item.value || 0}
              </Typography>
            </Box>
          </Box>
        </Grid>
      ))}
    </Grid>
  );
};

export default ReportCard;
