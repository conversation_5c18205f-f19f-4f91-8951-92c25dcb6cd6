/* eslint-disable react/prop-types */
import {
  Box,
  Button,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  Input,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import { Stack } from "@mui/system";
import React, { useEffect, useMemo, useState } from "react";
import { Icon } from "../constants/Assets";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import PopupChangeAddress from "../components/UI/PopupChangeAddress";
import ChangeInfoATC from "../components/products/PopupChangeInfoATC";
import FrameContainer from "../components/layout/Container";
import { useCart } from "../hooks/useCart";
import { IProduct, IProductGift, IPromotion } from "../types/product";
import { showToast } from "../utils/common";
import { useDispatch, useSelector } from "react-redux";
import { createOrder } from "../redux/slices/order/orderSlice";
import { AppDispatch, RootState } from "../redux/store";
import {
  DISCOUNT_TYPES,
  ERROR_MESSAGE,
  Level,
  LevelName,
  PaymentMethodText,
  PaymentMethods,
  PointPercent,
} from "../constants/Const";
import moment from "moment";
import { formatPrice } from "../utils/formatPrice";
import { IVoucher } from "../types/voucher";
import { useTheme } from "@mui/material/styles";
import { APP_NAME } from "../constants/AppInfo";
import _, { omit } from "lodash";
import { Router } from "../constants/Route";
import { useCheckLogin } from "../hooks/useCheckLogin";
import { useAlert } from "../redux/slices/alert/useAlert";
import styles from "../css/styles.module.css";
import { useNavigate } from "../utils/component-util";
import { Platform } from "@/config";
import PopupDeposit from "@/components/UI/PopupDeposit";
import { COLORS } from "@/constants/themes";
import PopupChangeWareHouse from "@/components/UI/PopupChangeWareHouse";
import SelectVoucher from "@/components/discount/SelectVoucher";
import { setCurVoucher } from "@/redux/slices/voucher/voucherSlice";
import GradientText from "@/components/UI/GradientTextProps";
import { getUser } from "@/redux/slices/authen/authSlice";
import { useHandlePayment } from "@/hooks/useHandlePayment";

function BottomVerify({
  paymentMethod,
  setDepositValue,
  depositValue,
  cart,
  currentAddress,
  finalPrice,
  handlerCreateOrder,
  currentWarehouse,
}) {
  const { showAlert } = useAlert();

  const [isDeposite, setisDeposite] = useState(false);

  const totalProduct = useMemo(() => {
    if (!Array.isArray(cart?.items) || !cart?.items.length) return 0;
    return cart?.items.reduce((acc, item) => acc + item.quantity, 0);
  }, [cart]);

  const onClickOrderButton = () => {
    // if (paymentMethod === PaymentMethods.Deposit && depositValue === 0) {
    //   setisDeposite(true);
    // } else {
    showAlert({
      icon: Icon.check,
      title: "Xác nhận đặt hàng",
      content: "Bạn có chắc muốn đặt hàng không?",
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: () => {
            if (!currentAddress) {
              showToast({
                content: "Bạn chưa có thông tin địa chỉ",
                type: "error",
              });
              return;
            }
            if (!currentWarehouse) {
              showToast({
                content: "Bạn chưa chọn điểm kho hàng",
                type: "error",
              });
              return;
            }
            handlerCreateOrder();
            setisDeposite(false);
          },
        },
      ],
    });
    // }
  };

  return (
    <Box className={styles.bottomPayElement}>
      <Container>
        <Stack
          style={{ fontSize: 14 }}
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Stack>
            <span>{totalProduct} sản phẩm</span>
            <span style={{ color: "#555555", fontWeight: 700 }}>
              Tổng thanh toán:
            </span>
            <GradientText
              text={formatPrice(finalPrice || 0, "đ")}
              style={{ fontWeight: 700, fontSize: 20 }}
            />
          </Stack>
          <Button
            style={{
              background: COLORS.primary,
              color: "#fff",
              paddingInline: 30,
              borderRadius: 99,
              fontSize: 16,
            }}
            onClick={onClickOrderButton}
          >
            Đặt hàng
          </Button>
        </Stack>
        {isDeposite && (
          <PopupDeposit
            handleContinue={() => {
              setisDeposite(false);
              // onClickOrderButton();
            }}
            setDepositValue={setDepositValue}
            open={isDeposite}
            setOpen={setisDeposite}
            finalPrice={finalPrice}
          />
        )}
      </Container>
    </Box>
  );
}

export default function Cart() {
  const theme = useTheme();
  const [products, setProducts] = useState<
    Array<{
      product: IProduct;
      quantity: number;
      promotion: IProductGift;
    }>
  >([]);

  const { user } = useSelector((state: RootState) => state.auth);
  const { reloadCart } = useCart();
  const isReviewer = true; //ReviewerNumbers.includes(user?.phone || "");
  // const DefaultPaymentMethod = isReviewer
  //   ? PaymentMethods.Cod
  //   : PaymentMethods.Bank;
  // const DefaultPaymentMethod = data.reviewing
  //   ? PaymentMethods.Cod
  //   : PaymentMethods.Bank;
  // const newPaymentMethods = PaymentMethodText.map((o) => {
  //   if (o.value === PaymentMethods.Cod) {
  //     o.active = isReviewer;
  //   } else if (o.value === PaymentMethods.Bank) {
  //     o.active = !isReviewer;
  //   }
  //   return o;
  // });

  const {
    clearCart,
    changeProductQuantity,
    removeProductFromCart,
    createOrder,
  } = useCart();
  const { cart } = useSelector((state: RootState) => state.cart);
  const dispatch = useDispatch<AppDispatch>();
  const { currentAddress } = useSelector((state: RootState) => state.address);
  const currentWarehouse = useSelector(
    (state: RootState) => state.warehouse.currentWarehouse
  );
  const navigate = useNavigate();
  useHandlePayment();
  const [paymentMethod, setPaymentMethod] = useState(PaymentMethods.Vnpay);
  const [isEmpty, setIsEmpty] = useState(true);
  const { selectedVoucher } = useSelector((state: RootState) => state.vouchers);
  const [depositValue, setDepositValue] = useState(0);
  const { checkLogin } = useCheckLogin();
  const { showAlert } = useAlert();

  // const [discountValue, setDiscountValue] = useState<Number>(0)
  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    await dispatch(getUser());
  };
  useEffect(() => {
    reloadCart();
  }, [user]);

  const discountValue = useMemo(() => {
    let finalValue = 0;
    if (!selectedVoucher || !cart) {
      finalValue = 0;
      return finalValue;
    }
    switch (selectedVoucher.type) {
      case DISCOUNT_TYPES.discountPercent: {
        if (selectedVoucher.discountValue && cart?.finalPrice) {
          finalValue = (cart.finalPrice * selectedVoucher.discountValue) / 100;

          // If Having max price and discout price is more than max price, get max price
          if (
            selectedVoucher.maxPrice &&
            finalValue > selectedVoucher.maxPrice
          ) {
            finalValue = selectedVoucher.maxPrice;
          }
        }

        break;
      }
      case DISCOUNT_TYPES.discountPrice:
        // Having min price (min price to apply voucher)
        if (selectedVoucher.minPrice && cart?.finalPrice) {
          if (cart.finalPrice < selectedVoucher.minPrice) {
            showToast({
              content: "Voucher không hợp lệ",
              type: "error",
            });
          }
          finalValue = selectedVoucher.discountValue;
        } else {
          finalValue = selectedVoucher.discountValue;
        }
        break;

      default:
        finalValue = 0;
        break;
    }
    if (finalValue >= cart?.finalPrice) {
      return cart?.finalPrice;
    }
    return finalValue;
  }, [selectedVoucher, cart?.finalPrice]);

  useEffect(() => {
    const checkEmpty = products?.reduce((acc, item) => {
      return acc + item.quantity;
    }, 0);

    if (checkEmpty > 0) {
      setIsEmpty(false);
    }
  }, [products]);

  useEffect(() => {
    if (Array.isArray(cart?.items) && cart?.items.length > 0) {
      setProducts(cart.items);
    } else {
      setProducts([]);
    }
  }, [cart]);

  const handlerCreateOrder = () => {
    checkLogin(checkCreateOrder);
  };
  const totalCommission = cart?.items.reduce(
    (total, item) => total + item.quantity * item.product.price,
    0
  );

  const checkCreateOrder = async () => {
    if (!currentAddress || !currentWarehouse) return;

    const address = omit(currentAddress, ["id"]);
    const affCode = sessionStorage.getItem("affiliateCode");

    const body = {
      creatorId: user?.id,
      voucherId: selectedVoucher?.id,
      payPrice: cart!.finalPrice - discountValue,
      finalPrice: cart!.finalPrice - discountValue,
      discountValue,
      paymentMethod,
      depositValue,
      totalCommission,
      orderData: {
        items: products,
        ship: 0,
        userInfo: {
          level: user?.level,
        },
      },
      warehouse: currentWarehouse,
      receiver: address,
      affCode: affCode,
    };
    const res: any = await createOrder(body);

    if (res) {
      if (res.data?.id) {
        clearCart();
      } else {
        showAlert({
          title: "Lỗi",
          content: `${ERROR_MESSAGE[res.message] || "Có lỗi xảy ra"}`,
        });
      }
    }
    setDepositValue(0);
  };

  const onCreateOrderSuccess = (order) => {
    showAlert({
      icon: Icon.check,
      title: "Đặt hàng thành công",
      content: `Cảm ơn Bạn đã đặt hàng tại ${APP_NAME}`,
      buttons: [
        {
          title: "OK",
          action: () => {
            clearCart();
            // if (paymentMethod === PaymentMethods.Bank) {
            //   navigate(Router.bankTransfer.index, {
            //     state: { id: order.id, finalPrice: cart?.finalPrice },
            //   });
            // } else if (paymentMethod === PaymentMethods.Deposit) {
            //   navigate(Router.bankTransfer.index, {
            //     state: {
            //       id: order.id,
            //       finalPrice: cart?.finalPrice,
            //       depositValue: depositValue,
            //     },
            //   });
            // } else {
            navigate(Router.order.index);
            // }
          },
        },
      ],
    });
  };

  // const pointValue = () => {
  //   const userLevel = user?.level || 0;
  //   const cartFinalPrice =
  //     userLevel === Level.Customer
  //       ? cart?.totalPrice - cart?.totalDiscount || 0
  //       : cart?.finalPrice || 0;
  //   const pointResult = cartFinalPrice * PointPercent;
  //   return pointResult;
  // };

  return (
    <Box>
      <FrameContainer title="Giỏ hàng">
        <Stack borderRadius={4} bgcolor="#fff" padding={2} marginBottom={1}>
          <Stack
            direction="row"
            paddingBottom={2}
            gap={2}
            color={COLORS.darkBlue}
          >
            <img src={Icon.package} />
            <b>
              <GradientText text="Sản phẩm đặt mua" />
            </b>
          </Stack>
          <Stack gap={2}>
            {products?.length > 0 && !isEmpty ? (
              products?.map((item, index) => (
                <Box key={index}>
                  <Stack
                    key={index}
                    direction="row"
                    gap={2}
                    alignItems={"center"}
                  >
                    <Stack color={theme.palette.primary.main} gap={1} flex={1}>
                      {/* <span>{item.name}</span> */}
                      <ChangeInfoATC
                        item={item}
                        onChangeQuantity={(id, quantity) => {
                          if (quantity === 0) {
                            removeProductFromCart(id);
                          } else changeProductQuantity(id, quantity);
                        }}
                      />
                    </Stack>
                    <IconButton
                      onClick={() => {
                        removeProductFromCart(item.product.id);
                        showToast({
                          content: "Xóa thành công sản phẩm",
                          type: "success",
                        });
                      }}
                    >
                      <HighlightOffIcon />
                    </IconButton>
                  </Stack>
                  {/* <Stack
                    borderRadius={4}
                    bgcolor="#fff"
                    padding={2}
                    marginBlock={1}
                    gap={2}
                    marginLeft={"-16px"}
                  >
                    <Stack
                      direction="row"
                      gap={2}
                      color={COLORS.darkBlue}
                      alignItems={"center"}
                    >
                      <img src={Icon.gift} />
                      <b>Quà tặng</b>
                    </Stack>

                    <FormControl>
                      <Stack gap={2}>
                        <RadioGroup
                          aria-labelledby="demo-form-control-label-placement"
                          name="position"
                          defaultValue={DefaultPaymentMethod}
                          onChange={(e) =>
                            setPaymentMethod(Number(e.target.value))
                          }
                        >
                          <Stack>
                            <Stack
                              key={index}
                              width={"100%"}
                              direction="row"
                              justifyContent="flex-start"
                            >
                              <Stack
                                gap={1}
                                direction="row"
                                alignItems={"center"}
                              >
                                <Box>
                                  <Typography fontSize={14}>
                                    {item?.promotion?.product?.name}
                                    <span
                                      style={{
                                        color: theme.palette.info.main,
                                        fontWeight: "700",
                                        paddingLeft: "5px",
                                      }}
                                    >
                                      x
                                      {Number(item?.promotion?.quantity) *
                                        Number(item.quantity)}
                                    </span>
                                  </Typography>
                                </Box>
                              </Stack>
                            </Stack>
                          </Stack>
                        </RadioGroup>
                      </Stack>
                    </FormControl>
                  </Stack> */}
                </Box>
              ))
            ) : (
              <Stack direction="row" justifyContent={"center"} padding={2}>
                Giỏ hàng trống
              </Stack>
            )}
          </Stack>
        </Stack>
        <Stack
          borderRadius={4}
          bgcolor="#fff"
          padding={2}
          marginBlock={1}
          gap={2}
        >
          <Stack direction="row" gap={2} color={COLORS.darkBlue}>
            <img src={Icon.ship} />
            <b>
              <GradientText text="Thông tin vận chuyển" />
            </b>
          </Stack>
          <Stack gap={2}>
            <PopupChangeAddress />
            <Divider />
            <Stack direction="row" gap={3}>
              <img src={Icon.lock} />
              <Stack>
                <span style={{ fontWeight: 700 }}>
                  Ngày {moment().add("3", "days").format("DD")} Th{" "}
                  {moment().add("3", "days").format("MM")} - Ngày{" "}
                  {moment().add("4", "days").format("DD")} Th{" "}
                  {moment().add("4", "days").format("MM")}
                </span>
                <span style={{ color: "#969595" }}>
                  Thời gian nhận hàng dự kiến
                </span>
              </Stack>
            </Stack>
            <Divider />
            <Stack direction="row" gap={3}>
              <img src={Icon.note} />
              <Stack width={"100%"}>
                <Input placeholder="Nhập ghi chú..." />
              </Stack>
            </Stack>
          </Stack>
        </Stack>

        <Stack
          borderRadius={4}
          bgcolor="#fff"
          padding={2}
          marginBlock={1}
          gap={2}
        >
          <Stack direction="row" gap={2} color={COLORS.darkBlue}>
            <img src={Icon.warehose} />
            <b>
              <GradientText text="Điểm kho hàng" />
            </b>
          </Stack>
          <Stack gap={2}>
            <PopupChangeWareHouse />
          </Stack>
        </Stack>

        <Stack
          borderRadius={4}
          bgcolor="#fff"
          padding={2}
          marginBlock={1}
          gap={2}
        >
          <Stack direction="row" gap={2} color={COLORS.darkBlue}>
            <img src={Icon.discount} />
            <b>
              <GradientText text="Thông tin khuyến mại" />
            </b>
          </Stack>
          <SelectVoucher
            onApplyVoucher={(curVoucher) => dispatch(setCurVoucher(curVoucher))}
            initVoucher={selectedVoucher}
          />
        </Stack>

        <Stack
          borderRadius={4}
          bgcolor="#fff"
          padding={2}
          marginBlock={1}
          gap={2}
        >
          <Stack direction="row" gap={2} color={COLORS.darkBlue}>
            <img src={Icon.payment} />
            <b>
              <GradientText text="Chi tiết thanh toán" />
            </b>
          </Stack>
          <Stack direction="row" justifyContent={"flex-start"} gap={2}>
            <img src={Icon.money} />
            <Stack>
              <span style={{ fontWeight: 700 }}>
                {formatPrice(
                  cart?.items.reduce(
                    (total, item) =>
                      total +
                      item.quantity *
                        (item.product.price - item.product.discount),

                    0
                  )
                )}
              </span>
              <span style={{ color: "#969595" }}>Tổng tiền hàng</span>
            </Stack>
          </Stack>
          <Divider />
          <Stack direction="row" justifyContent={"flex-start"} gap={2}>
            <img src={Icon.voucher2} />
            <Stack>
              <span style={{ fontWeight: 700 }}>
                {formatPrice(totalCommission)}
              </span>
              <span style={{ color: "#969595" }}>
                Chiết khấu bv cho vị trí {LevelName[user?.level]}
              </span>
            </Stack>
          </Stack>
          <Divider />
          {/* <Stack direction="row" justifyContent={"flex-start"} gap={2}>
            <img src={Icon.ship2} />
            <Stack>
              <span style={{ color: COLORS.darkBlue, fontWeight: 700 }}>
                {formatPrice(23000)}
              </span>
              <span style={{ color: "#969595" }}>Phí vận chuyển</span>
              <span style={{ color: "#969595" }}>
                Vui lòng thanh toán phí vận chuyển cho đơn vị giao hàng
              </span>
            </Stack>
          </Stack>
          <Divider /> */}

          <Stack direction="row" justifyContent={"flex-start"} gap={2}>
            <img src={Icon.voucher2} />
            <Stack>
              <span style={{ fontWeight: 700 }}>
                {formatPrice(discountValue)}
              </span>
              <span style={{ color: "#969595" }}>Tổng cộng mã giảm giá</span>
            </Stack>
          </Stack>
          {/* <Divider /> */}
          {/* <Stack direction="row" justifyContent={"flex-start"} gap={2}>
            <img src={Icon.voucher2} />
            <Stack>
              <span style={{ color: COLORS.darkBlue, fontWeight: 700 }}>
                {pointValue()}
              </span>
              <span style={{ color: "#969595" }}>Tích điểm</span>
            </Stack>
          </Stack> */}
        </Stack>
        {/* {(user && !isReviewer) || Platform === "web" ? (
          <Stack
            borderRadius={4}
            bgcolor="#fff"
            padding={2}
            marginBlock={1}
            gap={2}
          >
            <Stack direction="row" gap={2} color={COLORS.darkBlue}>
              <img src={Icon.payment} />
              <b>
                <GradientText text="Phương thức thanh toán" />
              </b>
            </Stack>

            <FormControl>
              <Stack gap={2}>
                <RadioGroup
                  aria-labelledby="demo-form-control-label-placement"
                  name="position"
                  defaultValue={DefaultPaymentMethod}
                  onChange={(e) => setPaymentMethod(Number(e.target.value))}
                >
                  <Stack gap={2}>
                    {newPaymentMethods.map((item, index) => (
                      <Stack
                        key={index}
                        width={"100%"}
                        direction="row"
                        justifyContent="flex-start"
                        style={{ border: "1px solid #D9D9D9", padding: 8 }}
                      >
                        <FormControlLabel
                          sx={{
                            flex: 1,
                            display: "flex",
                            justifyContent: "space-between",
                            ".Mui-checked": {
                              color: COLORS.default,
                            },
                          }}
                          value={item.value}
                          control={<Radio />}
                          label={
                            <span style={{ fontWeight: 700 }}>
                              {item.label}
                            </span>
                          }
                          labelPlacement="start"
                        />
                      </Stack>
                    ))}
                  </Stack>
                </RadioGroup>
              </Stack>
            </FormControl>
          </Stack>
        ) : null} */}

        <Typography pb={6} paddingInline={2} fontSize={14} textAlign={"center"}>
          Bằng việc tiến hành đặt hàng, bạn đồng ý với điều kiện và điều khoản
          sử dụng của {APP_NAME}
        </Typography>
      </FrameContainer>
      {Array.isArray(products) && products.length > 0 && (
        <BottomVerify
          paymentMethod={paymentMethod}
          depositValue={depositValue}
          setDepositValue={setDepositValue}
          cart={cart}
          currentAddress={currentAddress}
          finalPrice={cart?.finalPrice ? cart.finalPrice - discountValue : 0}
          handlerCreateOrder={handlerCreateOrder}
          currentWarehouse={currentWarehouse}
        />
      )}
    </Box>
  );
}
