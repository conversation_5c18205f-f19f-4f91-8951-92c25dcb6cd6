import React, { memo } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Stack, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../redux/store";
import { updateMe } from "../../redux/slices/authen/authSlice";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { showToast } from "../../utils/common";
import { COLORS } from "@/constants/themes";

const validationSchema = Yup.object().shape({
  password: Yup.string()
    .required("Vui lòng nhập mật khẩu")
    .min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
  confirmPassword: Yup.string()
    .required("Vui lòng xác nhận mật khẩu")
    .oneOf([Yup.ref("password")], "Mật khẩu xác nhận không đúng"),
});

type FormData = {
  password: string;
  confirmPassword: string;
};

const PopupEditPassword = memo(
  ({
    openSetPassword,
    setOpenSetPassword,
  }: {
    openSetPassword: boolean;
    setOpenSetPassword: (status: boolean) => void;
  }) => {
    const dispatch = useDispatch<AppDispatch>();

    const formOptions = { resolver: yupResolver(validationSchema) };

    const { handleSubmit, register, formState, reset } =
      useForm<FormData>(formOptions);
    const { errors } = formState;

    const submitSetPassword = async (values) => {
      const res: any = await dispatch(
        updateMe({
          password: values.password,
        })
      );

      if (!res.error) {
        setOpenSetPassword(false);
        reset();
        showToast({
          content: "Cập nhật mật khẩu thành công",
          type: "success",
        });
      } else {
        showToast({
          content:
            res.error?.message ?? "Quá trình cập nhật lỗi. Vui lòng thử lại",
          type: "error",
        });
      }
    };

    const handleCloseSetPassword = () => {
      setOpenSetPassword(false);
      reset();
    };

    return (
      <Dialog
        open={openSetPassword}
        onClose={handleCloseSetPassword}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <form onSubmit={handleSubmit(submitSetPassword)}>
          <DialogContent>
            <Stack gap={2}>
              <TextField
                type="password"
                required
                id="outlined-required"
                label="Mật khẩu"
                {...register("password")}
              />
              {errors.password && <p>{errors.password.message}</p>}

              <TextField
                required
                type="password"
                id="outlined-required"
                label="Nhập lại mật khẩu"
                {...register("confirmPassword")}
              />
              {errors.confirmPassword && (
                <p>{errors.confirmPassword.message}</p>
              )}
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              type="submit"
              style={{
                background: COLORS.primary,
                fontWeight: 400,
                color: "#fff",
                width: "120px",
              }}
            >
              Xác nhận
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    );
  }
);

export default memo(PopupEditPassword);
