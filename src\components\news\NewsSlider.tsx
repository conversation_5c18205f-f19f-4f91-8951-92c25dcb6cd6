import { Icon } from "@/constants/Assets";
import NewsItem from "./NewsItem";
import Slider from "react-slick";

const NewsSlider = ({ news }) => {
  const sliderSetting = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 1.5,
    arrows: false,
  };
  return (
    <Slider {...sliderSetting}>
      {news?.map((news) => (
        <NewsItem key={news.id} item={news} />
      ))}
    </Slider>
  );
};
export default NewsSlider;
