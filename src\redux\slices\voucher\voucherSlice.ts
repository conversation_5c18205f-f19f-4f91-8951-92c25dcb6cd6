import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IVoucher } from "../../../types/voucher";

interface AuthState {
  voucherList: IVoucher[];
  myVoucherList: IVoucher[];
  isLoading: boolean;
  selectedVoucher: IVoucher | null;
}

const initialState: AuthState = {
  voucherList: [],
  myVoucherList: [],
  isLoading: true,
  selectedVoucher: null,
};

export const getVoucherList = createAsyncThunk(
  "voucher/getAllVoucherList",
  async () => {
    const response: any = await request("get", "/api/vouchers", {
      sort: ["id:desc"],
      populate: ["banner"],
    });
    return response;
  }
);

export const getMyVoucherList = createAsyncThunk(
  "voucher/getMyVoucherList",
  async () => {
    const response: any = await request("get", "/api/my-voucher", {
      sort: ["id:desc"],
      populate: ["banner"],
    });
    return response;
  }
);

export const redeemPointToVoucher = createAsyncThunk(
  "voucher/redeemPointToVoucher",
  async (data: { voucherId: number }, { rejectWithValue }) => {
    const { voucherId } = data;
    try {
      const response: any = await request(
        "post",
        "/api/my-voucher/redeem-point",
        {
          voucherId,
        }
      );

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const voucherSlice = createSlice({
  name: "voucherLists",
  initialState,
  reducers: {
    setCurVoucher: (state, action: PayloadAction<IVoucher | null>) => {
      state.selectedVoucher = action?.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getVoucherList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getVoucherList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          if (payload.data) {
            state.voucherList = payload.data.map((e) => {
              return {
                id: e.id,
                ...e.attributes,
                banner: e.attributes.banner?.data?.attributes,
              };
            });
          }
          state.isLoading = false;
        }
      )
      .addCase(getVoucherList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getMyVoucherList.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getMyVoucherList.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.myVoucherList = payload.data;
          state.isLoading = false;
        }
      )
      .addCase(getMyVoucherList.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(redeemPointToVoucher.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        redeemPointToVoucher.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
        }
      )
      .addCase(redeemPointToVoucher.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const { setCurVoucher } = voucherSlice.actions;

export default voucherSlice.reducer;
