import { COLORS } from "../../constants/themes";
import { Box, Typography } from "@mui/material";
import React from "react";
import { Icon } from "@/constants/Assets";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { APP_NAME } from "@/constants/AppInfo";

export default function FixButton() {
  const { showAlert } = useAlert();

  const onClickAcademy = () => {
    showAlert({
      icon: Icon.check,
      title: "Thông báo",
      content: `Tính năng đang được phát triển!`,
      buttons: [
        {
          title: "OK",
          action: () => {},
        },
      ],
    });
  };
  const onClickBotAi = () => {
    showAlert({
      icon: Icon.check,
      title: "Thông báo",
      content: `Tính năng đang được phát triển!`,
      buttons: [
        {
          title: "OK",
          action: () => {},
        },
      ],
    });
  };
  return (
    <Box
      sx={{
        position: "fixed",
        right: 5,
        top: "50%",
        transform: "translateY(-50%)",
        zIndex: 1000,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Box
        sx={{
          width: "50px",
          height: "50px",
          borderRadius: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          overflow: "hidden",
        }}
        onClick={() => onClickAcademy()}
      >
        <img
          src={Icon.academy_icon}
          style={{
            width: "100%",
            objectFit: "cover",
            aspectRatio: "1/1",
          }}
        />
      </Box>
      <Typography
        sx={{
          fontSize: 12,
          color: "#004747",
          fontWeight: 700,
          textAlign: "center",
          marginBottom: 1,
        }}
      >
        ACADEMY
      </Typography>
      <Box
        sx={{
          width: "50px",
          height: "50px",
          borderRadius: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          overflow: "hidden",
        }}
        onClick={() => onClickBotAi()}
      >
        <img
          src={Icon.ai_icon}
          style={{
            width: "100%",
            objectFit: "cover",
            aspectRatio: "1/1",
          }}
        />
      </Box>
      <Typography
        sx={{
          fontSize: 12,
          color: "#004747",
          fontWeight: 700,
          textAlign: "center",
        }}
      >
        TRỢ LÝ AI
      </Typography>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  content: {
    color: COLORS.neutral5,
    textAlign: "center",
  },
};
