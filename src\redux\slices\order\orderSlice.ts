import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";
import { IProductCategory } from "../../../types/product";
import { IOrder } from "../../../types/order";

interface OrderState {
  list: IOrder[];
  orderDetail: IOrder | null;
  orderList: IOrder[];
  isLoading: boolean;
}

const initialState: OrderState = {
  list: [],
  orderDetail: null,
  orderList: [],
  isLoading: false,
};

export const createOrderOnServer = createAsyncThunk(
  "order/createOrder",
  async (data: any, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/my-order", {
        data,
      });

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);
export const updateOrder = createAsyncThunk(
  "order/updateOrder",
  async (data: any, { rejectWithValue }) => {
    try {
      const id = data.id;
      delete data.id;

      console.log(id);

      const response: any = await request("put", `/api/orders/${id}`, {
        data,
      });

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getAllOrder = createAsyncThunk("order/getAllOrder", async () => {
  const response: any = await request("get", "/api/my-order");
  return response;
});

export const getOrderByOrderStatus = createAsyncThunk(
  "order/getOrderByOrderStatus",
  async (orderStatus: number) => {
    const response: any = await request(
      "get",
      `/api/my-order?populate[0]=orderStatus&filters[orderStatus][$eq]=${orderStatus}&sort[0]=updatedAt:desc`
    );

    return response;
  }
);

export const getOrderDetail = createAsyncThunk(
  "order/getOrderDetail",
  async (id: string) => {
    const response: any = await request("get", `/api/my-order/${id}`);
    return response;
  }
);

export const createMac = createAsyncThunk(
  "order/createMac",
  async (data: any, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/my-order/mac", {
        ...data,
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const trackingPayment = createAsyncThunk(
  "order/trackingPayment",
  async (data: any, { rejectWithValue }) => {
    try {
      const response: any = await request("post", "/api/request-logs", {
        data: {
          body: data,
        },
      });

      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const authSlice = createSlice({
  name: "order",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createOrderOnServer.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        createOrderOnServer.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
        }
      )
      .addCase(createOrderOnServer.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getAllOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getAllOrder.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          state.orderDetail = payload.data?.map((data) => ({
            id: data.id,
            ...data.attribute,
          }));
          state.isLoading = false;
        }
      )
      .addCase(getAllOrder.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getOrderByOrderStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getOrderByOrderStatus.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;
          state.orderList = [...payload.data];
          state.isLoading = false;
        }
      )
      .addCase(getOrderByOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(getOrderDetail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getOrderDetail.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const { payload } = action;

          state.orderDetail = payload?.data;
          state.isLoading = false;
        }
      )
      .addCase(getOrderDetail.rejected, (state, action) => {
        state.isLoading = false;
      })

      .addCase(
        updateOrder.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          state.isLoading = false;
        }
      );
  },
});

export default authSlice.reducer;
