import React, { useEffect } from "react";
import { Box } from "@mui/system";
import "react-datepicker/dist/react-datepicker.css";
import ReportCard from "@/components/report/ReportCard";
import FrameContainer from "@/components/layout/Container";
import CollabMenuTool from "@/components/collab/CollabMenuTool";
import MemberShip from "@/components/MemberShip";
import { useSelector } from "react-redux";
import store, { RootState } from "@/redux/store";
import { Icon } from "@/constants/Assets";
import { getReport } from "@/redux/slices/team/team";

export default function CollabReportPage() {
  const { teamReport } = useSelector((state: RootState) => state.team);
  const { user } = useSelector((state: RootState) => state.auth);
  const items = [
    {
      icon: Icon.icon_dhtc,
      title: "<PERSON><PERSON>h thu đội nhóm",
      value: user?.myTeamSale,
      isMoney: true,
    },
    {
      icon: Icon.icon_dtcn,
      title: "<PERSON><PERSON>h thu cá nhân",
      value: user?.mySale,
      isMoney: true,
    },
    {
      icon: Icon.icon_dhtc,
      title: "<PERSON><PERSON>n hàng thành công",
      value: teamReport?.totalOrderSuccess,
    },

    {
      icon: Icon.icon_hhdd,
      title: "Hoa hồng đã duyệt",
      value: user?.commission?.totalCommission,
      isMoney: true,
    },
    {
      icon: Icon.icon_point,
      title: "Số dư khả dụng",
      value: user?.balance,
      isMoney: true,
    },
    {
      icon: Icon.icon_teams,
      title: "Số thành viên nhóm",
      value: teamReport?.totalMembers,
    },
  ];
  useEffect(() => {
    store.dispatch(getReport());
  }, []);
  return (
    <>
      <FrameContainer title="Báo cáo cộng tác viên">
        <Box>
          <MemberShip />
        </Box>
        <CollabMenuTool />
        <ReportCard items={items} />
      </FrameContainer>
    </>
  );
}
