import { formatPrice } from "@/utils/formatPrice";
import { Platform } from "../config";
import { Icon } from "./Assets";
import { Router } from "./Route";

export const Env = {
  BackendUrl: import.meta.env.VITE_API_URL,
};

export const OrderPrefix = "FN";

export const MockPhone = "097694682131";

export const OrderStatus = {
  Pending: 1,
  Paid: 2,
  Shipping: 3,
  Cancelled: 4,
  Success: 5,
  OutOfStock: 6,
  Deposited: 7,
};

export const OrderStatusText = {
  [OrderStatus.Pending]: "Chờ xác nhận",
  [OrderStatus.Paid]: "Đã thanh toán",
  [OrderStatus.Shipping]: "Đang giao hàng",
  [OrderStatus.Cancelled]: "Đã hủy",
  [OrderStatus.Success]: "Hoàn thành",
  [OrderStatus.OutOfStock]: "Hết hàng",
  [OrderStatus.Deposited]: "Đã đặt cọc",
};

export const PaymentMethods = {
  Cod: 1,
  Bank: 2,
  Vnpay: 3,
};

export const MethodValue = {
  [PaymentMethods.Cod]: "COD",
  [PaymentMethods.Bank]: "BANK",
  [PaymentMethods.Vnpay]: "VNPAY",
};

export const MethodText = {
  [PaymentMethods.Cod]: "Thanh toán khi nhận hàng",
  [PaymentMethods.Bank]: "Chuyển khoản ngân hàng",
  [PaymentMethods.Vnpay]: "Thanh toán qua VNPAY",
};

export const PaymentMethodText = [
  {
    value: PaymentMethods.Cod,
    label: "Thành toán khi nhận hàng",
    iconPath: Icon.banker,
  },
  {
    value: PaymentMethods.Bank,
    label: "Chuyển khoản ngân hàng",
    iconPath: Icon.transfer,
  },
  {
    value: PaymentMethods.Vnpay,
    label: "Thanh toán VNPAY",
    iconPath: Icon.transfer,
  },
];

export const ERROR_MESSAGE = {
  "Product not found":
    "Không tìm thấy sản phẩm, vui lòng xoá giỏ hàng và thử lại!",
  "Product price mismatch":
    "Giá sản phẩm chưa đúng, vui lòng xoá giỏ hàng, tải lại trang và thử lại!",
  "Final price mismatch":
    "Có lỗi xảy ra, vui lòng xoá giỏ hàng, tải lại trang và thử lại!",
  "The quantity of vouchers has been depleted":
    "Voucher đã hết lượt sử dụng, vui lòng kiểm tra lại!",
  "Insufficient stock": "Sản phẩm không còn đủ hàng, vui lòng kiểm tra lại!",
  "Phone is already taken": "Số điện thoại đã được sử dụng",
  "Refer code is invalid": "Mã giới thiệu không hợp lệ",
  "You cannot join your own team":
    "Bạn không thể nhập mã giới thiệu của chính bạn",
};
export const SomethingWrong = "Có lỗi xảy ra, vui lòng thử lại sau!";

export const DISCOUNT_TYPES = {
  discountPrice: "discountPrice",
  discountPercent: "discountPercent",
  freeship: "freeship",
};

export const AppId = "1136346436675888090";
export const PhoneRegex = /^0[35789]\d{8}$/;

export const DefaultFilter = {
  id: 0,
  name: "Tất cả",
};

export const Level = {
  Member: 0,
  Member1: 1,
  Member2: 2,
  Member3: 3,
  Member4: 4,
  Member5: 5,
  Member6: 6,
  Member7: 7,
  Member8: 8,
};

export const LevelName = {
  [Level.Member]: "Khách hàng thành viên",
  [Level.Member1]: "Đại sứ affiliate",
  [Level.Member2]: "Đại lý",
  [Level.Member3]: "Nhà phân phối",
  [Level.Member4]: "Trưởng phòng kinh doanh",
  [Level.Member5]: "Phó giám đốc thị trường",
  [Level.Member6]: "Giám đốc thị trường",
  [Level.Member7]: "Giám đốc Miền",
  [Level.Member8]: "Giám đốc toàn quốc",
};

export const LevelIcon = {
  [Level.Member]: Icon.verify,
  [Level.Member1]: Icon.verify,
  [Level.Member2]: Icon.verify,
  [Level.Member3]: Icon.verify,
  [Level.Member4]: Icon.verify,
  [Level.Member5]: Icon.verify,
  [Level.Member6]: Icon.verify,
  [Level.Member7]: Icon.verify,
  [Level.Member8]: Icon.verify,
};

export const NO_BOTTOM_NAVIGATION_PAGES = [
  Router.cart,
  Router.productDetail,
  Router.bankTransfer.index,
  Router.login,
  Router.register,
  Router.auth,
];

export const AppLink = `https://zalo.me/app/link/zapps/${AppId}`;

export const ReviewerNumbers = ["**********", "**********", "**********"];

export const WithdrawalStatus = {
  Pending: 1,
  Paid: 2,
};

export const WithdrawalStatusText = {
  [WithdrawalStatus.Pending]: "Chờ xử lý",
  [WithdrawalStatus.Paid]: "Đã thanh toán",
};

export const TransactionType = {
  CommissionOrder: "CommissionOrder",
  Withdraw: "Withdraw",
};

export const WithdrawalRanger = {
  Min: 50000,
  Max: ********,
};

export const WithdrawalRangerWarning = {
  [WithdrawalRanger.Max]: `Số tiền tối đa có thể rút là ${formatPrice(
    WithdrawalRanger.Max
  )}`,
  [WithdrawalRanger.Min]: `Số tiền tối thiểu có thể rút là ${formatPrice(
    WithdrawalRanger.Min
  )}`,
};

export const ColabStatus = {
  Not_Waiting: "Not waiting for Approve",
  Waiting: "Waiting for Approve",
  Approved: "Approved",
};

export const DEFAULT_REFER_CODE = "FN1";

export type IHomeTab = {
  id: number;
  icon: string;
  title: string;
};

export const HomeTabType = {
  AccumulatePoint: 1,
  OrderHistory: 2,
  Academy: 3,
  Ai: 4,
  Contact: 5,
};
const HomeTabText = {
  [HomeTabType.AccumulatePoint]: "Ưu đãi",
  [HomeTabType.OrderHistory]: "Đơn hàng",
  [HomeTabType.Academy]: "Academy",
  [HomeTabType.Ai]: "Trợ lý AI",
};

export const HomeTabs: Record<string, Array<IHomeTab>> = {
  First: [
    {
      id: HomeTabType.AccumulatePoint,
      icon: Icon.icon_accumulate_point,
      title: HomeTabText[HomeTabType.AccumulatePoint],
    },

    {
      id: HomeTabType.OrderHistory,
      icon: Icon.icon_order_history,
      title: HomeTabText[HomeTabType.OrderHistory],
    },
    {
      id: HomeTabType.Academy,
      icon: Icon.academy_icon,
      title: HomeTabText[HomeTabType.Academy],
    },
    {
      id: HomeTabType.Ai,
      icon: Icon.ai_icon,
      title: HomeTabText[HomeTabType.Ai],
    },
  ],
};

export const PointPercent = 0.05;

export const VoucherType = {
  DiscountPrice: "discountPrice",
  DiscountPercent: "discountPercent",
  FreeShip: "freeship",
};
export const VoucherText = {
  [VoucherType.DiscountPrice]: "Mã giảm giá",
  [VoucherType.DiscountPercent]: "Mã giảm giá",
  [VoucherType.FreeShip]: "Mã freeship",
};
