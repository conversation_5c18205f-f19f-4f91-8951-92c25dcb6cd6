import { COLORS } from "@/constants/themes";
import { formatPrice } from "@/utils/formatPrice";
import { Box, Typography } from "@mui/material";
import React from "react";

interface ICommissionItem {
  title: string;
  value: number;
}

export default function CommissionItem({ title, value }: ICommissionItem) {
  return (
    <Box sx={styles.container}>
      <Box display="flex" justifyContent="space-between">
        <Typography style={styles.title}>{title}</Typography>
        <Box display="flex" alignItems="center" justifyContent="center">
          <Typography style={styles.priceText}>{formatPrice(value)}</Typography>
        </Box>
      </Box>
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    borderRadius: 2,
    background: "#fff",
    padding: 1.5,
    border: 1,
    borderColor: "#D9D9D9",
    marginBlock: 1,
  },
  priceText: {
    fontWeight: 700,
    color: COLORS.primary,
  },
  title: {
    fontWeight: 700,
    color: COLORS.blue,
  },
};
