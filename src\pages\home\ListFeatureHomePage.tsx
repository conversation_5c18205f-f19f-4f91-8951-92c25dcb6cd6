import { Icon } from "@/constants/Assets";
import { Box, Container, Typography } from "@mui/material";

export default function ListFeatureHomePage() {
  const listFeature = [
    {
      id: 1,
      title: "<PERSON><PERSON><PERSON><PERSON> hàng thanh toán",
      icon: Icon.paymentHome,
    },
    {
      id: 2,
      title: "Xác nhận thanh toán",
      icon: Icon.transferHome,
    },
    {
      id: 3,
      title: "Giao hàng thành công",
      icon: Icon.cod,
    },
    {
      id: 4,
      title: "Nhận hoa hồng",
      icon: Icon.icon_tragop,
    },
  ];
  return (
    <Container>
      <Box display={"flex"} flexDirection={"column"}>
        <Box marginBottom={"10px"}>
          <Typography
            style={{
              fontWeight: 600,
              color: "#141415",
              fontSize: "18px",
            }}
          >
            Trở thành Cộng tác viên dễ dàng
          </Typography>
        </Box>
        <Box display={"flex"} justifyContent={"space-between"}>
          {listFeature.map((feature, index) => (
            <Box
              key={index}
              display={"flex"}
              flexDirection={"column"}
              alignItems={"center"}
            >
              <img
                width={80}
                height={80}
                src={feature.icon}
                alt={feature.title}
              />
              <div
                style={{
                  fontSize: 14,
                  color: "#1D1D5E",
                  textAlign: "center",
                  marginTop: "10px",
                  width: "80px",
                }}
              >
                {feature.title}
              </div>
            </Box>
          ))}
        </Box>
      </Box>
    </Container>
  );
}
