import React, { memo } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { Stack, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../redux/store";
import { getUser, updateMe } from "../../redux/slices/authen/authSlice";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { showToast } from "../../utils/common";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";

const validationSchema = Yup.object().shape({
  name: Yup.string(),
  dob: Yup.string(),
  email: Yup.string(),
  address: Yup.string(),
});

type FormData = {
  name: string;
  dob: string;
  email: string;
  address: string;
};

const PopupEditProfile = memo(
  ({
    open,
    setOpen,
  }: {
    open: boolean;
    setOpen: (status: boolean) => void;
  }) => {
    const user = useSelector((state: RootState) => state.auth.user);

    const dispatch = useDispatch<AppDispatch>();

    const formOptions: any = {
      resolver: yupResolver(validationSchema),
      values: {
        name: user?.name,
        dob: user?.dob,
        email: user?.email,
        address: user?.address,
      },
    };

    const { handleSubmit, register, reset } = useForm<FormData>(formOptions);

    const submitSetPassword = async (values) => {
      console.log("values", values);
      const res: any = await dispatch(updateMe(values));

      if (!res.error) {
        setOpen(false);
        reset();
        await dispatch(getUser());
        showToast({
          content: "Cập nhật thông tin thành công",
          type: "success",
        });
      } else {
        showToast({
          content:
            res.error?.message ?? "Quá trình cập nhật lỗi. Vui lòng thử lại",
          type: "error",
        });
      }
    };

    const handleCloseSetPassword = () => {
      setOpen(false);
      reset();
    };

    return (
      <Dialog
        open={open}
        onClose={handleCloseSetPassword}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <form onSubmit={handleSubmit(submitSetPassword)}>
          <DialogContent>
            <Stack gap={2}>
              <TextField
                id="outlined-required"
                label="Họ và tên"
                {...register("name")}
              />
              <TextField
                type="date"
                id="outlined-required"
                label="Ngày sinh"
                {...register("dob")}
              />

              <TextField
                id="outlined-required"
                label="Địa chỉ"
                {...register("address")}
              />
              <TextField
                id="outlined-required"
                label="Email"
                {...register("email")}
              />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              type="submit"
              style={{
                background: "#2677E5",
                fontWeight: 400,
                color: "#fff",
                width: "120px",
              }}
            >
              Xác nhận
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    );
  }
);
PopupEditProfile.displayName = 'PopupEditProfile';
export default memo(PopupEditProfile);
