import { useEffect } from "react";
import { useCart } from "./useCart";
import { useDispatch, useSelector } from "react-redux";
import store, { AppDispatch, RootState } from "../redux/store";
import {
  getUser,
  getUserZalo,
  loginWithZaloId,
  updateUser,
} from "../redux/slices/authen/authSlice";
import { getRouteParams } from "zmp-sdk";
import { getProductCategoryList } from "../redux/slices/product/productSlice";
import {
  getNewsList,
  getNewsBanner,
  getNewsListHomePage,
  getReviewListHomePage,
} from "../redux/slices/news/newsListSlice";
import {
  getProductBestSellerList,
  getProductRecommendList,
  getNewProductList,
} from "../redux/slices/product/productListSlice";
import { getReport } from "../redux/slices/team/team";
import { axiosInstance } from "../utils/request";
import { getItem } from "../utils/storage";
import { StorageKeys } from "../constants/StorageKeys";
import { Router } from "../constants/Route";
import { useLocation, useSearchParams } from "react-router-dom";
import { useNavigate } from "../utils/component-util";
import { Platform } from "../config";
import {
  getConfig,
  setDeepLinkStartAppNavigated,
} from "@/redux/slices/configSlice";
import { hideLoading } from "@/redux/slices/loading/loadingSlice";

const UnauthenticatedRoutes = [Router.login, Router.register, Router.auth];

export default function initData() {
  const { loadCartFromStorage } = useCart();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user, userZalo, token } = useSelector(
    (state: RootState) => state.auth
  );
  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();
  // Handle click refer link
  const { refCode, targetType, productId, page, id } = getRouteParams();
  const deepLinkStartAppNavigated = useSelector(
    (state: RootState) => state.config.deepLinkStartAppNavigated
  );

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    const token = await getItem(StorageKeys.AccessToken);

    if (!token) {
      if (Platform === "zalo") {
        // await dispatch(getUserZalo());
        fetchData();
        return;
      }
      if (!UnauthenticatedRoutes.includes(pathname)) {
        const refCode = searchParams.get("referCode");
        if (refCode) {
          navigate(Router.register + "?referCode=" + refCode);
          return;
        }
        navigate(Router.login);
      }
    } else {
      axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
      await dispatch(getUserZalo());
      await dispatch(getUser());
      await fetchData();
    }
    dispatch(hideLoading());
  };

  const fetchData = async () => {
    updateName();
    loadCartFromStorage();
    await store.dispatch(getConfig());
    await store.dispatch(getReport());
    await store.dispatch(getProductCategoryList());
    await store.dispatch(getNewsList());
    await store.dispatch(getNewsBanner());
    await store.dispatch(getNewsListHomePage());
    await store.dispatch(getReviewListHomePage());
    await store.dispatch(getProductRecommendList());
    await store.dispatch(getProductBestSellerList());
    await store.dispatch(getNewProductList());
    dispatch(hideLoading());
  };

  useEffect(() => {
    if (user?.id && token) {
      axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
      updateName();
    } else if (!token && userZalo && Platform === "zalo") {
      dispatch(loginWithZaloId(userZalo.id));
      // navigate(Router.homepage);
    }
  }, [userZalo, token]);

  //Update name
  const updateName = async () => {
    const objToUpdate: any = {};
    userZalo?.name &&
      user?.name !== userZalo?.name &&
      (objToUpdate.name = userZalo?.name);
    userZalo?.avatar &&
      user?.avatarUrl !== userZalo?.avatar &&
      (objToUpdate.avatarUrl = userZalo?.avatar);
    userZalo?.id &&
      user?.zaloId !== userZalo?.id &&
      (objToUpdate.zaloId = userZalo?.id);
    if (Object.keys(objToUpdate).length > 0 && user?.id) {
      dispatch(updateUser(objToUpdate));
    } else {
      console.log("No update needed, user name is already the same.");
    }
  };

  useEffect(() => {
    if (!token) return;
    axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
    init();
  }, [token]);

  useEffect(() => {
    if (!deepLinkStartAppNavigated) {
      if (targetType && targetType === "REFER") {
        navigate(Router.refer.index + "?refCode=" + refCode);
      }
      if (targetType && targetType === "AFF") {
        navigate(`/product-detail/${productId}`);
      }
      if (page) {
        navigate(page + `/${id}`);
      }
      dispatch(setDeepLinkStartAppNavigated(true));
    }
  }, [targetType]);
}
